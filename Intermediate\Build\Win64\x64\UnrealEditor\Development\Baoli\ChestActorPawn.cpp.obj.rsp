"H:\P4\dev\Baoli\Source\Baoli\Interactable\Chest\ChestActorPawn.cpp"
/FI"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\BaoliEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h"
/FI"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Definitions.Baoli.h"
/Yu"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\BaoliEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h"
/Fp"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\BaoliEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h.pch"
/Fo"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\ChestActorPawn.cpp.obj"
/experimental:log "H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\ChestActorPawn.cpp.sarif"
/sourceDependencies "H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\ChestActorPawn.cpp.dep.json"
@"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Baoli.Shared.rsp"
/d2ssa-cfg-question-
/Zc:inline
/nologo
/Oi
/FC
/c
/Gw
/Gy
/utf-8
/wd4819
/DSAL_NO_ATTRIBUTE_DECLARATIONS=1
/permissive-
/Zc:strictStrings-
/Zc:__cplusplus
/D_CRT_STDIO_LEGACY_WIDE_SPECIFIERS=1
/D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS=1
/D_WINDLL
/D_DISABLE_EXTENDED_ALIGNED_STORAGE
/Ob2
/d2ExtendedWarningInfo
/Ox
/Ot
/GF
/errorReport:prompt
/EHsc
/DPLATFORM_EXCEPTIONS_DISABLED=0
/Z7
/MD
/bigobj
/fp:fast
/Zo
/Zp8
/we4456
/we4458
/we4459
/we4668
/wd4244
/wd4838
/TP
/GR-
/W4
/std:c++20
/Zc:preprocessor
/wd5054