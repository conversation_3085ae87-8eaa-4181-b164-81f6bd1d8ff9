<?xml version="1.0" encoding="utf-8"?>
<root xmlns:android="http://schemas.android.com/apk/res/android">
	<init>
		<log text="OptimizedWebBrowser Plugin Init"/>
		<setBoolFromPropertyContains result="bPackageForOculusQuest" ini="Engine" section="/Script/AndroidRuntimeSettings.AndroidRuntimeSettings" property="PackageForOculusMobile" contains="Quest"/>
		<setBoolFromPropertyContains result="bPackageForOculusQuest2" ini="Engine" section="/Script/AndroidRuntimeSettings.AndroidRuntimeSettings" property="PackageForOculusMobile" contains="Quest2"/>
		<setBoolFromPropertyContains result="bPackageForOculusQuestPro" ini="Engine" section="/Script/AndroidRuntimeSettings.AndroidRuntimeSettings" property="PackageForOculusMobile" contains="QuestPro"/>

		<setBoolOr result="bPackageForOculusMobile" 
			arg1="$B(bPackageForOculusQuest)" arg2="$B(bPackageForOculusQuest2)"/>
		<setBoolOr result="bPackageForOculusMobile" 
			arg1="$B(bPackageForOculusMobile)" arg2="$B(bPackageForOculusQuestPro)"/>

		<if condition="bPackageForOculusMobile">
			<true>
				<log text="Adding com.oculus.always_draw_view_root for Oculus devices to manifest for hardware acceleration."/>
			</true>
		</if>
	</init>

	<androidManifestUpdates>
		<if condition="bPackageForOculusMobile">
			<true>
				<addElements tag="application">
					<meta-data android:name="com.oculus.always_draw_view_root" android:value="true"/>
				</addElements>
			</true>
		</if>
		
		<!-- Add hardware acceleration for all Android devices -->
		<addAttribute tag="application" name="android:hardwareAccelerated" value="true"/>
		<addAttribute tag="activity" name="android:hardwareAccelerated" value="true"/>
	</androidManifestUpdates>
</root>
