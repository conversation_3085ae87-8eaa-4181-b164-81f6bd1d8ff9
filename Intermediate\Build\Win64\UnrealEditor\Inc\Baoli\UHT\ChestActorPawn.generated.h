// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Interactable/Chest/ChestActorPawn.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef BAOLI_ChestActorPawn_generated_h
#error "ChestActorPawn.generated.h already included, missing '#pragma once' in ChestActorPawn.h"
#endif
#define BAOLI_ChestActorPawn_generated_h

#define FID_Baoli_Source_Baoli_Interactable_Chest_ChestActorPawn_h_13_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAChestActorPawn(); \
	friend struct Z_Construct_UClass_AChestActorPawn_Statics; \
public: \
	DECLARE_CLASS(AChestActorPawn, APawn, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Baoli"), NO_API) \
	DECLARE_SERIALIZER(AChestActorPawn)


#define FID_Baoli_Source_Baoli_Interactable_Chest_ChestActorPawn_h_13_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AChestActorPawn(AChestActorPawn&&); \
	AChestActorPawn(const AChestActorPawn&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AChestActorPawn); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AChestActorPawn); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AChestActorPawn) \
	NO_API virtual ~AChestActorPawn();


#define FID_Baoli_Source_Baoli_Interactable_Chest_ChestActorPawn_h_10_PROLOG
#define FID_Baoli_Source_Baoli_Interactable_Chest_ChestActorPawn_h_13_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Baoli_Source_Baoli_Interactable_Chest_ChestActorPawn_h_13_INCLASS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_Interactable_Chest_ChestActorPawn_h_13_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BAOLI_API UClass* StaticClass<class AChestActorPawn>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Baoli_Source_Baoli_Interactable_Chest_ChestActorPawn_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
