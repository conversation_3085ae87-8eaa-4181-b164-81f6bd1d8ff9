// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "OptimizedWebBrowser.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
struct FColor;
#ifdef OPTIMIZEDWEBBROWSER_OptimizedWebBrowser_generated_h
#error "OptimizedWebBrowser.generated.h already included, missing '#pragma once' in OptimizedWebBrowser.h"
#endif
#define OPTIMIZEDWEBBROWSER_OptimizedWebBrowser_generated_h

#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_23_DELEGATE \
static void FOnUrlChanged_DelegateWrapper(const FMulticastScriptDelegate& OnUrlChanged, FText const& Text);


#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_24_DELEGATE \
static void FOnBeforePopup_DelegateWrapper(const FMulticastScriptDelegate& OnBeforePopup, const FString& URL, const FString& Frame);


#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_25_DELEGATE \
static void FOnConsoleMessage_DelegateWrapper(const FMulticastScriptDelegate& OnConsoleMessage, const FString& Message, const FString& Source, int32 Line);


#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_20_RPC_WRAPPERS \
	DECLARE_FUNCTION(execSetBackgroundTabThrottlingEnabled); \
	DECLARE_FUNCTION(execSetBackgroundColor); \
	DECLARE_FUNCTION(execSetHardwareAccelerationEnabled); \
	DECLARE_FUNCTION(execSetBrowserFrameRate); \
	DECLARE_FUNCTION(execLogToConsole); \
	DECLARE_FUNCTION(execGetUrl); \
	DECLARE_FUNCTION(execGetTitleText); \
	DECLARE_FUNCTION(execExecuteJavascript); \
	DECLARE_FUNCTION(execLoadString); \
	DECLARE_FUNCTION(execLoadURL);


#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_20_INCLASS \
private: \
	static void StaticRegisterNativesUOptimizedWebBrowser(); \
	friend struct Z_Construct_UClass_UOptimizedWebBrowser_Statics; \
public: \
	DECLARE_CLASS(UOptimizedWebBrowser, UWidget, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OptimizedWebBrowser"), NO_API) \
	DECLARE_SERIALIZER(UOptimizedWebBrowser)


#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_20_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UOptimizedWebBrowser(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UOptimizedWebBrowser) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOptimizedWebBrowser); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOptimizedWebBrowser); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UOptimizedWebBrowser(UOptimizedWebBrowser&&); \
	UOptimizedWebBrowser(const UOptimizedWebBrowser&); \
public: \
	NO_API virtual ~UOptimizedWebBrowser();


#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_17_PROLOG
#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_20_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_20_RPC_WRAPPERS \
	FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_20_INCLASS \
	FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_20_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> OPTIMIZEDWEBBROWSER_API UClass* StaticClass<class UOptimizedWebBrowser>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
