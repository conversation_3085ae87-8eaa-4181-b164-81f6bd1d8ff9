{"Version": "1.2", "Data": {"Source": "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\optimizedwebbrowser\\module.optimizedwebbrowser.cpp", "ProvidedModule": "", "PCH": "h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\x64\\baolieditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\optimizedwebbrowser\\definitions.optimizedwebbrowser.h", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\intermediate\\build\\win64\\unrealeditor\\inc\\optimizedwebbrowser\\uht\\optimizedwebbrowser.gen.cpp", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\source\\optimizedwebbrowser\\public\\optimizedwebbrowser.h", "d:\\ue_5.5\\engine\\source\\runtime\\umg\\public\\components\\widget.h", "d:\\ue_5.5\\engine\\source\\runtime\\umg\\public\\binding\\states\\widgetstatebitfield.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetstatebitfield.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\fieldnotification\\public\\fieldnotificationdeclaration.h", "d:\\ue_5.5\\engine\\source\\runtime\\fieldnotification\\public\\ifieldnotificationclassdescriptor.h", "d:\\ue_5.5\\engine\\source\\runtime\\fieldnotification\\public\\inotifyfieldvaluechanged.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\fieldnotification\\uht\\inotifyfieldvaluechanged.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\umg\\public\\components\\visual.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\visual.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\umg\\public\\components\\slatewrappertypes.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\slatewrappertypes.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\umg\\public\\slate\\widgettransform.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgettransform.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\umg\\public\\blueprint\\widgetnavigation.h", "d:\\ue_5.5\\engine\\source\\runtime\\slatecore\\public\\types\\navigationmetadata.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetnavigation.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widget.generated.h", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\intermediate\\build\\win64\\unrealeditor\\inc\\optimizedwebbrowser\\uht\\optimizedwebbrowser.generated.h", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\intermediate\\build\\win64\\unrealeditor\\inc\\optimizedwebbrowser\\uht\\optimizedwebbrowser.init.gen.cpp", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\intermediate\\build\\win64\\unrealeditor\\inc\\optimizedwebbrowser\\uht\\webbrowseroptimizer.gen.cpp", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\source\\optimizedwebbrowser\\public\\webbrowseroptimizer.h", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\intermediate\\build\\win64\\unrealeditor\\inc\\optimizedwebbrowser\\uht\\webbrowseroptimizer.generated.h", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\optimizedwebbrowser\\permoduleinline.gen.cpp", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\source\\optimizedwebbrowser\\private\\optimizedwebbrowser.cpp", "d:\\ue_5.5\\engine\\source\\runtime\\webbrowser\\public\\swebbrowser.h", "d:\\ue_5.5\\engine\\source\\runtime\\webbrowser\\public\\swebbrowserview.h", "d:\\ue_5.5\\engine\\source\\runtime\\slate\\public\\widgets\\sviewport.h", "d:\\ue_5.5\\engine\\source\\runtime\\webbrowser\\public\\iwebbrowsersingleton.h", "d:\\ue_5.5\\engine\\source\\runtime\\webbrowser\\public\\iwebbrowserresourceloader.h", "d:\\ue_5.5\\engine\\source\\runtime\\webbrowser\\public\\webbrowsermodule.h", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\source\\optimizedwebbrowser\\private\\optimizedwebbrowsermodule.cpp", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\source\\optimizedwebbrowser\\public\\optimizedwebbrowsermodule.h", "h:\\p4\\dev\\baoli\\plugins\\optimizedwebbrowser\\source\\optimizedwebbrowser\\private\\webbrowseroptimizer.cpp"], "ImportedModules": [], "ImportedHeaderUnits": []}}