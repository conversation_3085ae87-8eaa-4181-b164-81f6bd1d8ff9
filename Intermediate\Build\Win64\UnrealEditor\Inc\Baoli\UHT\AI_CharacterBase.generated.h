// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AI/AI_CharacterBase.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef BAOLI_AI_CharacterBase_generated_h
#error "AI_CharacterBase.generated.h already included, missing '#pragma once' in AI_CharacterBase.h"
#endif
#define BAOLI_AI_CharacterBase_generated_h

#define FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_12_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execForceDash);


#define FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_12_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAI_CharacterBase(); \
	friend struct Z_Construct_UClass_AAI_CharacterBase_Statics; \
public: \
	DECLARE_CLASS(AAI_CharacterBase, ACharacter, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Baoli"), NO_API) \
	DECLARE_SERIALIZER(AAI_CharacterBase)


#define FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_12_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AAI_CharacterBase(AAI_CharacterBase&&); \
	AAI_CharacterBase(const AAI_CharacterBase&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAI_CharacterBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAI_CharacterBase); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAI_CharacterBase) \
	NO_API virtual ~AAI_CharacterBase();


#define FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_9_PROLOG
#define FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_12_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_12_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_12_INCLASS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_12_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BAOLI_API UClass* StaticClass<class AAI_CharacterBase>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
