"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\BaoliEditor\Development\Core\SharedPCH.Core.Cpp20.cpp"
/I "."
/I "Runtime\Core\Public"
/I "Runtime\Core\Internal"
/I "Runtime\TraceLog\Public"
/external:W0
/external:I "ThirdParty\GuidelinesSupportLibrary\GSL-1144\include"
/external:I "E:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\INCLUDE"
/external:I "C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt"
/external:I "C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared"
/external:I "C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um"
/external:I "C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt"
/Yc"SharedPCH.Core.Cpp20.h"
/Fp"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\BaoliEditor\Development\Core\SharedPCH.Core.Cpp20.h.pch"
/Fo"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\BaoliEditor\Development\Core\SharedPCH.Core.Cpp20.h.obj"
/experimental:log "H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\BaoliEditor\Development\Core\SharedPCH.Core.Cpp20.h.sarif"
/sourceDependencies "H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\BaoliEditor\Development\Core\SharedPCH.Core.Cpp20.h.dep.json"
/d2ssa-cfg-question-
/Zc:inline
/nologo
/Oi
/FC
/c
/Gw
/Gy
/utf-8
/wd4819
/DSAL_NO_ATTRIBUTE_DECLARATIONS=1
/permissive-
/Zc:strictStrings-
/Zc:__cplusplus
/D_CRT_STDIO_LEGACY_WIDE_SPECIFIERS=1
/D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS=1
/D_WINDLL
/D_DISABLE_EXTENDED_ALIGNED_STORAGE
/Ob2
/d2ExtendedWarningInfo
/Ox
/Ot
/GF
/errorReport:prompt
/EHsc
/DPLATFORM_EXCEPTIONS_DISABLED=0
/Z7
/MD
/bigobj
/fp:fast
/Zo
/Zp8
/we4668
/wd4244
/wd4838
/TP
/GR-
/W4
/std:c++20
/Zc:preprocessor
/wd5054