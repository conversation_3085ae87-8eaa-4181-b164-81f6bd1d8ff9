﻿Log file open, 06/05/25 11:04:57
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=35532)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: Baoli
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\P4\dev\Baoli\Baoli.uproject -skipcompile""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.261689
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-A77CEF544AB65F0D885C3AB8F2A6C6D5
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/P4/dev/Baoli/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.04 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogConfig: Display: Loading Android ini files took 0.04 seconds
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading Windows ini files took 0.05 seconds
LogConfig: Display: Loading Unix ini files took 0.05 seconds
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading Linux ini files took 0.06 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin Chooser
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin JsonBlueprintUtilities
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin AssetReferenceRestrictions
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ScriptableToolsEditorMode
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin ActorPalette
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GPULightmass
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin Paper2D
LogAssetRegistry: Display: Asset registry cache read as 106.3 MiB from H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationLocomotionLibrary
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin AnimationWarping
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin BlendStack
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin PoseSearch
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin MotionWarping
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AxFImporter
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin MDLImporter
LogPluginManager: Mounting Engine plugin NiagaraFluids
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin USDImporter
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin GameFeatures
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin ModularGameplay
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ScriptableToolsFramework
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WebBrowserWidget
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin MotionTrajectory
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin AnalyticsBlueprintLibrary
LogPluginManager: Mounting Engine plugin Reflex
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Project plugin Inkpot
LogPluginManager: Mounting Project plugin PlatformFunctions
LogPluginManager: Mounting Project plugin OptimizedWebBrowser
LogPluginManager: Mounting Project plugin SnappingHelper
LogPluginManager: Mounting Project plugin rdBPtools
SourceControl: Revision control is disabled
LogGPULightmass: GPULightmass module is loaded
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: NVIDIA GeForce RTX 2080 Ti
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: -skipcompile
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 36
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.54ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Chooser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'JsonBlueprintUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetReferenceRestrictions' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ScriptableToolsEditorMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorPalette' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GPULightmass' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationLocomotionLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationWarping' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayInsights' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PoseSearch' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MotionWarping' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AxFImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/MDLImporter.ini) has wildcard redirect /DatasmithContent/Materials/MDL/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/NiagaraFluids.ini) has wildcard redirect /NiagaraSimulationStages/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'USDImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataRegistry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameFeatures' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryScripting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ModularGameplay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ScriptableToolsFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebBrowserWidget' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MotionTrajectory' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnalyticsBlueprintLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Reflex' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Inkpot' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformFunctions' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OptimizedWebBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SnappingHelper' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'rdBPtools' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[r.Mobile.EnableNoPrecomputedLightingCSMShader:1]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.AllowGlobalClipPlane:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.CustomDepth:3]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.VirtualTextures:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.AntiAliasingMethod:4]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.ReflectionCaptureResolution:32]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.MegaLights.EnableForProject:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.PathTracing:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.RayTracing.Shadows:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.RayTracing.UseTextureLod:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.CustomDepthTemporalAAJitter:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default:75.000000]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default.Desktop.Mode:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.Shadow.CSMCaching:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.MSAACount:8]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileSize:32]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileBorderSize:2]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.MaxTextureSize:4096]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.UseCompression:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.VT.TileBorderSize:4]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.DefaultTexelsPerVertex:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.AllowStaticLighting:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.Shadow.UnbuiltPreviewInGame:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.NormalMapsForStaticLighting:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing.LightingMode:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.VirtualTexturedLightmaps:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.Lumen.ScreenTracingSource:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Bias:0.000000]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Method:2]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.vt.rvt.HighQualityPerPixelHeight:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.05-05.34.58:058][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.05-05.34.58:058][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.05-05.34.58:058][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.05-05.34.58:058][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.05-05.34.58:062][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.05-05.34.58:062][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.06.05-05.34.58:062][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.05-05.34.58:062][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.05-05.34.58:062][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.05-05.34.58:062][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.05-05.34.58:065][  0]LogRHI: Using Default RHI: D3D12
[2025.06.05-05.34.58:065][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.05-05.34.58:065][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.05-05.34.58:069][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.05-05.34.58:069][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.05-05.34.58:188][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.05-05.34.58:188][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-05.34.58:188][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 3 output[s]
[2025.06.05-05.34.58:189][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.05-05.34.58:189][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.05-05.34.58:364][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.05-05.34.58:364][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-05.34.58:364][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-05.34.58:364][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.05-05.34.58:364][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.05-05.34.58:369][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.05-05.34.58:369][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.05-05.34.58:369][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-05.34.58:488][  0]LogD3D12RHI: Found D3D12 adapter 3: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.05-05.34.58:488][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-05.34.58:488][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-05.34.58:488][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.05-05.34.58:488][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.05-05.34.58:488][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.05-05.34.58:488][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.05-05.34.58:489][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.05-05.34.58:489][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.05-05.34.58:490][  0]LogHAL: Display: Platform has ~ 64 GB [68630138880 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.05-05.34.58:490][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.05-05.34.58:490][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.05-05.34.58:490][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.05-05.34.58:490][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.05-05.34.58:490][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.05-05.34.58:491][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.05-05.34.58:491][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.05-05.34.58:491][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.05-05.34.58:491][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.05-05.34.58:491][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.05-05.34.58:491][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.05-05.34.58:491][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.05-05.34.58:491][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.06.05-05.34.58:491][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/P4/dev/Baoli/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.05-05.34.58:491][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.06.05-05.34.58:491][  0]LogInit: User: Shashank
[2025.06.05-05.34.58:491][  0]LogInit: CPU Page size=4096, Cores=16
[2025.06.05-05.34.58:491][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.05-05.34.59:322][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.06.05-05.34.59:322][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.05-05.34.59:322][  0]LogMemory: Process Physical Memory: 693.04 MB used, 728.41 MB peak
[2025.06.05-05.34.59:322][  0]LogMemory: Process Virtual Memory: 769.80 MB used, 773.54 MB peak
[2025.06.05-05.34.59:322][  0]LogMemory: Physical Memory: 25390.12 MB used,  40060.68 MB free, 65450.80 MB total
[2025.06.05-05.34.59:322][  0]LogMemory: Virtual Memory: 31531.02 MB used,  38015.78 MB free, 69546.80 MB total
[2025.06.05-05.34.59:322][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.05-05.34.59:326][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.05-05.34.59:334][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.05-05.34.59:334][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.05-05.34.59:334][  0]LogInit: Using OS detected language (en-GB).
[2025.06.05-05.34.59:334][  0]LogInit: Using OS detected locale (en-IN).
[2025.06.05-05.34.59:337][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.05-05.34.59:337][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.05-05.34.59:600][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.05-05.34.59:600][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.06.05-05.34.59:600][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.05-05.34.59:615][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.05-05.34.59:615][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.05-05.34.59:732][  0]LogRHI: Using Default RHI: D3D12
[2025.06.05-05.34.59:732][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.05-05.34.59:732][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.05-05.34.59:732][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.05-05.34.59:732][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.05-05.34.59:732][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.05-05.34.59:732][  0]LogWindows: Attached monitors:
[2025.06.05-05.34.59:732][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.06.05-05.34.59:732][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.06.05-05.34.59:732][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.06.05-05.34.59:732][  0]LogWindows: Found 3 attached monitors.
[2025.06.05-05.34.59:733][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.05-05.34.59:733][  0]LogRHI: RHI Adapter Info:
[2025.06.05-05.34.59:733][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.06.05-05.34.59:733][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.05-05.34.59:733][  0]LogRHI:      Driver Date: 4-25-2025
[2025.06.05-05.34.59:733][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.06.05-05.34.59:761][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.05-05.34.59:856][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.05-05.34.59:856][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.05-05.34.59:946][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: Raster order views are supported
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.05-05.34.59:946][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.05-05.34.59:970][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000083BE7365300)
[2025.06.05-05.34.59:972][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000083BE7365580)
[2025.06.05-05.34.59:972][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000083BE7365800)
[2025.06.05-05.34.59:972][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.05-05.34.59:972][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.05-05.34.59:972][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.06.05-05.34.59:972][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.06.05-05.34.59:972][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.05-05.34.59:972][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.05-05.34.59:983][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.05-05.34.59:987][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.05-05.34.59:994][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all'
[2025.06.05-05.34.59:994][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all" ]
[2025.06.05-05.35.00:018][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.05-05.35.00:018][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.05-05.35.00:018][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.05-05.35.00:018][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.05-05.35.00:018][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.05-05.35.00:018][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.06.05-05.35.00:018][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.05-05.35.00:018][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.05-05.35.00:019][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.05-05.35.00:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.05-05.35.00:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.05-05.35.00:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.05-05.35.00:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.05-05.35.00:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.05-05.35.00:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.05-05.35.00:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.05-05.35.00:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.05-05.35.00:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.05-05.35.00:043][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.05-05.35.00:057][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.05-05.35.00:057][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.05-05.35.00:073][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.05-05.35.00:073][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.05-05.35.00:073][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.05-05.35.00:073][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.05-05.35.00:087][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.06.05-05.35.00:087][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.06.05-05.35.00:087][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.06.05-05.35.00:101][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.05-05.35.00:101][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.05-05.35.00:101][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.05-05.35.00:101][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.05-05.35.00:116][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.05-05.35.00:116][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.05-05.35.00:135][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.05-05.35.00:135][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.05-05.35.00:135][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.05-05.35.00:135][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.05-05.35.00:135][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.05-05.35.00:178][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.06.05-05.35.00:180][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.05-05.35.00:180][  0]LogTargetPlatformManager:   SF_METAL
[2025.06.05-05.35.00:180][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.06.05-05.35.00:180][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.06.05-05.35.00:180][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.05-05.35.00:181][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.05-05.35.00:181][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.06.05-05.35.00:181][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.06.05-05.35.00:183][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.05-05.35.00:183][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/P4/dev/Baoli/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.05-05.35.00:183][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.05-05.35.00:183][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/P4/dev/Baoli/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.05-05.35.00:183][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.05-05.35.00:246][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.06.05-05.35.00:246][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.05-05.35.00:246][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.05-05.35.00:246][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.05-05.35.00:247][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.05-05.35.00:248][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.05-05.35.00:248][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.06.05-05.35.00:248][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 20524 --child-id Zen_20524_Startup'
[2025.06.05-05.35.00:329][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.05-05.35.00:329][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.082 seconds
[2025.06.05-05.35.00:330][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.05-05.35.00:335][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.06.05-05.35.00:335][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.04ms. RandomReadSpeed=1336.33MBs, RandomWriteSpeed=268.35MBs. Assigned SpeedClass 'Local'
[2025.06.05-05.35.00:336][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.05-05.35.00:336][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.05-05.35.00:336][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.05-05.35.00:336][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.05-05.35.00:336][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.05-05.35.00:336][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.05-05.35.00:336][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.05-05.35.00:337][  0]LogShaderCompilers: Guid format shader working directory is 33 characters bigger than the processId version (H:/P4/dev/Baoli/Intermediate/Shaders/WorkingDirectory/20524/).
[2025.06.05-05.35.00:337][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/2102966B4708902313656CA9CDD03400/'.
[2025.06.05-05.35.00:337][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.06.05-05.35.00:337][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.06.05-05.35.00:338][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/P4/dev/Baoli/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.05-05.35.00:339][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.05-05.35.00:844][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.05-05.35.01:378][  0]LogSlate: Using FreeType 2.10.0
[2025.06.05-05.35.01:379][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.05-05.35.01:379][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-05.35.01:379][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-05.35.01:381][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-05.35.01:381][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-05.35.01:381][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-05.35.01:381][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-05.35.01:406][  0]LogAssetRegistry: FAssetRegistry took 0.0028 seconds to start up
[2025.06.05-05.35.01:408][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.05-05.35.01:413][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.05-05.35.01:576][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-05.35.01:578][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.05-05.35.01:578][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.05-05.35.01:578][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.05-05.35.01:588][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.05-05.35.01:588][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.05-05.35.01:612][  0]LogDeviceProfileManager: Active device profile: [0000083C00EC9E00][0000083BF45CC000 66] WindowsEditor
[2025.06.05-05.35.01:612][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.05-05.35.01:613][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.05-05.35.01:616][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.06.05-05.35.01:616][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-05.35.01:645][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:646][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:646][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.06.05-05.35.01:646][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-05.35.01:646][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:646][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.06.05-05.35.01:646][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-05.35.01:647][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:647][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:647][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.06.05-05.35.01:647][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-05.35.01:647][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:647][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.06.05-05.35.01:647][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-05.35.01:647][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.06.05-05.35.01:648][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-05.35.01:649][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.01:649][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.06.05-05.35.01:649][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-05.35.01:808][  0]LogMeshReduction: Display: Mesh reduction module (r.MeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-05.35.01:808][  0]LogMeshReduction: Display: Skeletal mesh reduction module (r.SkeletalMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-05.35.01:808][  0]LogMeshReduction: Display: HLOD mesh reduction module (r.ProxyLODMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-05.35.01:819][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.05-05.35.01:819][  0]LogMeshReduction: Display: Using InstaLODMeshReduction for automatic skeletal mesh reduction
[2025.06.05-05.35.01:819][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.05-05.35.01:819][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.05-05.35.01:819][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.05-05.35.01:945][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.49ms
[2025.06.05-05.35.01:966][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.49ms
[2025.06.05-05.35.01:978][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.48ms
[2025.06.05-05.35.01:980][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.49ms
[2025.06.05-05.35.02:156][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.05-05.35.02:156][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.05-05.35.02:161][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.05-05.35.02:161][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.05-05.35.02:161][  0]LogLiveCoding: Display: First instance in process group "UE_Baoli_0x736adef1", spawning console
[2025.06.05-05.35.02:164][  0]LogLiveCoding: Display: Waiting for server
[2025.06.05-05.35.02:179][  0]LogSlate: Border
[2025.06.05-05.35.02:179][  0]LogSlate: BreadcrumbButton
[2025.06.05-05.35.02:179][  0]LogSlate: Brushes.Title
[2025.06.05-05.35.02:179][  0]LogSlate: Default
[2025.06.05-05.35.02:179][  0]LogSlate: Icons.Save
[2025.06.05-05.35.02:179][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.05-05.35.02:179][  0]LogSlate: ListView
[2025.06.05-05.35.02:179][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.05-05.35.02:179][  0]LogSlate: SoftwareCursor_Grab
[2025.06.05-05.35.02:179][  0]LogSlate: TableView.DarkRow
[2025.06.05-05.35.02:179][  0]LogSlate: TableView.Row
[2025.06.05-05.35.02:179][  0]LogSlate: TreeView
[2025.06.05-05.35.02:265][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.05-05.35.02:266][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.681 ms
[2025.06.05-05.35.02:268][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.05-05.35.02:275][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.05-05.35.02:293][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.05-05.35.02:293][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.05-05.35.02:293][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.05-05.35.02:293][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.06.05-05.35.02:358][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.05-05.35.02:662][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.05-05.35.02:662][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.06.05-05.35.02:662][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.06.05-05.35.02:662][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.05-05.35.02:662][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.05-05.35.02:788][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: D9135308D8C94FE9800000000000EF00 | Instance: 0154B3B0421F7C08CA5380A91276429C (DESKTOP-E41IK6R-20524).
[2025.06.05-05.35.02:967][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.05-05.35.02:971][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.05-05.35.02:971][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.05-05.35.02:971][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:62543'.
[2025.06.05-05.35.02:974][  0]LogUdpMessaging: Display: Added local interface '192.168.1.5' to multicast group '230.0.0.1:6666'
[2025.06.05-05.35.02:974][  0]LogUdpMessaging: Display: Added local interface '172.24.112.1' to multicast group '230.0.0.1:6666'
[2025.06.05-05.35.02:974][  0]LogUdpMessaging: Display: Added local interface '172.29.240.1' to multicast group '230.0.0.1:6666'
[2025.06.05-05.35.03:125][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.05-05.35.03:125][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.05-05.35.03:138][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.05-05.35.03:267][  0]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.06.05-05.35.03:269][  0]LogCEFBrowser: CEF GPU acceleration enabled
[2025.06.05-05.35.03:353][  0]LogTemp: Warning: ✓ AI Perception system enabled and events bound
[2025.06.05-05.35.03:416][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.06.05-05.35.03:483][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.48ms
[2025.06.05-05.35.03:491][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.05-05.35.03:542][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.05-05.35.03:748][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.05-05.35.03:783][  0]LogTimingProfiler: Initialize
[2025.06.05-05.35.03:783][  0]LogTimingProfiler: OnSessionChanged
[2025.06.05-05.35.03:783][  0]LoadingProfiler: Initialize
[2025.06.05-05.35.03:783][  0]LoadingProfiler: OnSessionChanged
[2025.06.05-05.35.03:784][  0]LogNetworkingProfiler: Initialize
[2025.06.05-05.35.03:784][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.05-05.35.03:784][  0]LogMemoryProfiler: Initialize
[2025.06.05-05.35.03:784][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.05-05.35.03:920][  0]LogUsd: Attempted to call a static function from UnrealUSDWrapper before the module is actually loaded! The module will be loaded now, but not all static functions have this check. In general, please ensure an Unreal module is loaded before calling any of its static functions, for example by calling 'FModuleManager::LoadModuleChecked<IUnrealUSDWrapperModule>("UnrealUSDWrapper");' beforehand.
[2025.06.05-05.35.03:921][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.06.05-05.35.03:921][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.06.05-05.35.03:922][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.06.05-05.35.03:922][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.06.05-05.35.03:922][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.06.05-05.35.03:922][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.06.05-05.35.03:922][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.06.05-05.35.03:922][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.06.05-05.35.03:923][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.06.05-05.35.03:924][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.06.05-05.35.03:924][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.06.05-05.35.03:924][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.06.05-05.35.03:925][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.06.05-05.35.03:925][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.06.05-05.35.03:925][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.06.05-05.35.03:925][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.06.05-05.35.03:925][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.06.05-05.35.03:926][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.06.05-05.35.03:926][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.06.05-05.35.03:927][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.06.05-05.35.03:927][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.06.05-05.35.03:927][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.06.05-05.35.03:928][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.06.05-05.35.03:928][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.06.05-05.35.03:928][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.06.05-05.35.03:928][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.06.05-05.35.03:928][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.06.05-05.35.03:928][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.06.05-05.35.03:929][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.06.05-05.35.03:929][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.06.05-05.35.03:929][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.06.05-05.35.03:929][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.06.05-05.35.03:930][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.06.05-05.35.03:930][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.06.05-05.35.03:931][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.06.05-05.35.03:990][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-05.35.03:990][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-05.35.04:041][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.05-05.35.04:056][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.05-05.35.04:056][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.05-05.35.04:141][  0]LogCollectionManager: Loaded 1 collections in 0.000675 seconds
[2025.06.05-05.35.04:143][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Saved/Collections/' took 0.00s
[2025.06.05-05.35.04:145][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Developers/Shashank/Collections/' took 0.00s
[2025.06.05-05.35.04:147][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Collections/' took 0.00s
[2025.06.05-05.35.04:200][  0]LogConfig: Branch 'Plugins' had been unloaded. Reloading on-demand took 0.48ms
[2025.06.05-05.35.04:202][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-05.35.04:202][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-05.35.04:203][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-05.35.04:203][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-05.35.04:203][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-05.35.04:203][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-05.35.04:232][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-05.35.04:232][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-05.35.04:266][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-05.35.04:266][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-05.35.04:267][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-05.35.04:267][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-05.35.04:267][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-05.35.04:267][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-05.35.04:294][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-05.35.04:294][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-05.35.04:309][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-06-05T05:35:04.309Z using C
[2025.06.05-05.35.04:310][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=Baoli, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.05-05.35.04:310][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.05-05.35.04:311][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.05-05.35.04:315][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.05-05.35.04:315][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.05-05.35.04:315][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.05-05.35.04:315][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000045
[2025.06.05-05.35.04:315][  0]LogFab: Display: Logging in using persist
[2025.06.05-05.35.04:315][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.06.05-05.35.04:345][  0]LogUObjectArray: 47651 objects as part of root set at end of initial load.
[2025.06.05-05.35.04:345][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.05-05.35.04:357][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 40254 public script object entries (1077.02 KB)
[2025.06.05-05.35.04:357][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.06.05-05.35.04:468][  0]LogEngine: Initializing Engine...
[2025.06.05-05.35.04:469][  0]LogGameFeatures: Initializing game features subsystem
[2025.06.05-05.35.04:469][  0]InkPlusPlus: FStory::FStory 0000083C1E09FC10
[2025.06.05-05.35.04:469][  0]InkPlusPlus: Warning: WARNING: Version of ink used to build story doesn't match current version of engine. Non-critical, but recommend synchronising.
[2025.06.05-05.35.04:472][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.05-05.35.04:472][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.05-05.35.04:561][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.05-05.35.04:571][  0]LogGameFeatures: Scanning for built-in game feature plugins
[2025.06.05-05.35.04:571][  0]LogGameFeatures: Loading 233 builtins
[2025.06.05-05.35.04:572][  0]LogGameFeatures: Display: Total built in plugin load time 0.0008s
[2025.06.05-05.35.04:572][  0]LogStats: BuiltInGameFeaturePlugins loaded. -  0.001 s
[2025.06.05-05.35.04:572][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.05-05.35.04:588][  0]LogNetVersion: Set ProjectVersion to Alpha. Version Checksum will be recalculated on next use.
[2025.06.05-05.35.04:588][  0]LogInit: Texture streaming: Enabled
[2025.06.05-05.35.04:601][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.06.05-05.35.04:606][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.05-05.35.04:612][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.05-05.35.04:613][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.05-05.35.04:613][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.05-05.35.04:613][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.05-05.35.04:613][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-05.35.04:613][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-05.35.04:613][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-05.35.04:613][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-05.35.04:613][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-05.35.04:613][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-05.35.04:613][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-05.35.04:613][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-05.35.04:613][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-05.35.04:613][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-05.35.04:613][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-05.35.04:617][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-05.35.04:674][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-05.35.04:675][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-05.35.04:675][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-05.35.04:675][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-05.35.04:676][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.05-05.35.04:676][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.05-05.35.04:678][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.05-05.35.04:678][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.05-05.35.04:678][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.05-05.35.04:678][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.05-05.35.04:679][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.05-05.35.04:684][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.05-05.35.04:687][  0]LogInit: Undo buffer set to 256 MB
[2025.06.05-05.35.04:687][  0]LogInit: Transaction tracking system initialized
[2025.06.05-05.35.04:745][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.49ms
[2025.06.05-05.35.04:746][  0]LocalizationService: Localization service is disabled
[2025.06.05-05.35.04:947][  0]LogPython: Using Python 3.11.8
[2025.06.05-05.35.05:175][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/' took 0.28s
[2025.06.05-05.35.05:981][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.06.05-05.35.05:991][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (0 permutations).
[2025.06.05-05.35.06:004][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.05-05.35.06:004][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.05-05.35.06:016][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.05-05.35.06:111][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-05.35.06:111][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-05.35.06:112][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-05.35.06:112][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-05.35.06:112][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-05.35.06:112][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-05.35.06:138][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-05.35.06:138][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-05.35.06:142][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.05-05.35.06:142][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.05-05.35.06:166][  0]LogEditorDataStorage: Initializing
[2025.06.05-05.35.06:167][  0]LogEditorDataStorage: Initialized
[2025.06.05-05.35.06:174][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.06.05-05.35.06:196][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.05-05.35.06:222][  0]LogUnrealEdMisc: Loading editor; pre map load, took 8.916
[2025.06.05-05.35.06:223][  0]Cmd: MAP LOAD FILE="H:/P4/dev/Baoli/Content/Levels/DefaultLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.05-05.35.06:225][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.05-05.35.06:225][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-05.35.06:235][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-05.35.06:235][  0]InkPlusPlus: FStory::~FStory 0000083C1E09FC10
[2025.06.05-05.35.06:236][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.52ms
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/MHI.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performances/MHP_Scene1_01.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performance/MHP_Baoli.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-05.35.06:277][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.05-05.35.06:292][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.05-05.35.06:292][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Glossy (0x2CCA389A36D3E860)
[2025.06.05-05.35.06:292][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/material (0x38FB08B605AA9364)
[2025.06.05-05.35.06:292][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.05-05.35.06:292][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Dark (0xC267FEC07D768F2)
[2025.06.05-05.35.06:330][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.05-05.35.06:330][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVback (0x8B181E584DB8A471) /Game/Assets/TV/TVback (0x8B181E584DB8A471) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.05-05.35.06:544][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS (0xF21345B7066A3DF7)
[2025.06.05-05.35.07:028][  0]LogLinker: Warning: [AssetLog] H:\P4\dev\Baoli\Content\BaoliAssets\BrickInstances\Brick_low_001.uasset: VerifyImport: Failed to find script package for import object 'Package /Script/rdInst'
[2025.06.05-05.35.07:442][  0]LogAssetRegistry: Display: Asset registry cache written as 106.3 MiB to H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin
[2025.06.05-05.35.08:809][  0]LogEditorDomain: Display: Class /Script/rdInst.rdInstAssetUserData is imported by a package but does not exist in memory. EditorDomain keys for packages using it will be invalid if it still exists.
	To clear this message, resave packages that use the deleted class, or load its module earlier than the packages that use it are referenced.
[2025.06.05-05.35.10:005][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: WaitingForIo) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.10:008][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 334 to allow recursive sync load to finish
[2025.06.05-05.35.10:008][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.05-05.35.10:008][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: ExportsDone) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.10:008][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 335 to allow recursive sync load to finish
[2025.06.05-05.35.10:008][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.05-05.35.10:610][  0]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\CBP_SandboxCharacter.uasset: [Compiler] Input pin  Debug Session Unique Identifier  specifying non-default value no longer exists on node  Motion Match . Please refresh node or reset pin to default value to remove pin.
[2025.06.05-05.35.12:433][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: WaitingForIo) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.12:433][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 336 to allow recursive sync load to finish
[2025.06.05-05.35.12:433][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.05-05.35.12:433][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: ExportsDone) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-05.35.12:433][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 337 to allow recursive sync load to finish
[2025.06.05-05.35.12:433][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.05-05.35.12:561][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:563][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Handplant' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Handplant.MSS_FoleySound_Handplant' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:564][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Jump' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Jump.MSS_FoleySound_Jump' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:565][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Land' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Land.MSS_FoleySound_Land' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:566][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Run' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Run.MSS_FoleySound_Run' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:567][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunBackwards.MSS_FoleySound_RunBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:568][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunStrafe' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunStrafe.MSS_FoleySound_RunStrafe' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:570][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Scuff' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Scuff.MSS_FoleySound_Scuff' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:571][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffPivot' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffPivot.MSS_FoleySound_ScuffPivot' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:572][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffWall' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffWall.MSS_FoleySound_ScuffWall' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:573][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Tumble' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Tumble.MSS_FoleySound_Tumble' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:574][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Walk' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Walk.MSS_FoleySound_Walk' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.12:575][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_WalkBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_WalkBackwards.MSS_FoleySound_WalkBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-05.35.14:979][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.06.05-05.35.15:067][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.06.05-05.35.15:080][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_High...
[2025.06.05-05.35.15:083][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_High...
[2025.06.05-05.35.15:542][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.15:548][  0]LogSkeletalMesh: Built Skeletal Mesh [0.47s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High
[2025.06.05-05.35.15:562][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.06.05-05.35.16:240][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants_High...
[2025.06.05-05.35.16:661][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.16:663][  0]LogSkeletalMesh: Built Skeletal Mesh [0.43s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High
[2025.06.05-05.35.16:749][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.16:753][  0]LogSkeletalMesh: Built Skeletal Mesh [1.67s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High
[2025.06.05-05.35.17:049][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_High...
[2025.06.05-05.35.17:069][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_Cap_01_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-05.35.17:069][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelNut_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-05.35.17:070][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelLeaf_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-05.35.17:445][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.17:447][  0]LogSkeletalMesh: Built Skeletal Mesh [0.40s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High
[2025.06.05-05.35.18:901][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_hoodie_nrm_High...
[2025.06.05-05.35.19:337][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.19:341][  0]LogSkeletalMesh: Built Skeletal Mesh [0.44s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High
[2025.06.05-05.35.19:416][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Foley.SM_Foley.
[2025.06.05-05.35.19:417][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.05-05.35.19:417][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Ambient.SM_Ambient.
[2025.06.05-05.35.19:417][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.05-05.35.19:417][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Reverb.SM_Reverb.
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Jumps
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Stops
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.05-05.35.21:239][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.05-05.35.21:240][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.05-05.35.21:240][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.05-05.35.21:240][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.05-05.35.21:241][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.05-05.35.21:241][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.05-05.35.21:241][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.05-05.35.21:241][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.05-05.35.21:241][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.05-05.35.21:241][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.05-05.35.21:387][  0]LogPoseSearch: 25abbe71a7361c623bd3755ff5e1fbe7a56e7f94 - PSD_Dense_Jumps BeginCache
[2025.06.05-05.35.21:388][  0]LogPoseSearch: 25abbe71a7361c623bd3755ff5e1fbe7a56e7f94 - PSD_Dense_Jumps BuildIndex From Cache
[2025.06.05-05.35.21:399][  0]LogPoseSearch: 52bcfc861296fe46e902c3e1977b2a893663552c - PSD_Dense_Jumps_Far BeginCache
[2025.06.05-05.35.21:400][  0]LogPoseSearch: 52bcfc861296fe46e902c3e1977b2a893663552c - PSD_Dense_Jumps_Far BuildIndex From Cache
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Stops
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.05-05.35.21:410][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.05-05.35.21:411][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.05-05.35.21:411][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.05-05.35.21:411][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.05-05.35.21:464][  0]LogPoseSearch: c903d208ce6ce6d6d57c16e8029907a718a6c7e9 - PSD_Dense_Stand_Idle_Lands_Heavy BeginCache
[2025.06.05-05.35.21:465][  0]LogPoseSearch: c903d208ce6ce6d6d57c16e8029907a718a6c7e9 - PSD_Dense_Stand_Idle_Lands_Heavy BuildIndex From Cache
[2025.06.05-05.35.21:485][  0]LogPoseSearch: 2c155ec5c459344cf515e7737ccfee7b0c910810 - PSD_Dense_Stand_Run_Lands_Heavy BeginCache
[2025.06.05-05.35.21:486][  0]LogPoseSearch: 2c155ec5c459344cf515e7737ccfee7b0c910810 - PSD_Dense_Stand_Run_Lands_Heavy BuildIndex From Cache
[2025.06.05-05.35.21:514][  0]LogPoseSearch: 0647c6013483ddb2053168a7d66d3f931f221b6f - PSD_Dense_Stand_Walk_Lands_Heavy BeginCache
[2025.06.05-05.35.21:515][  0]LogPoseSearch: 0647c6013483ddb2053168a7d66d3f931f221b6f - PSD_Dense_Stand_Walk_Lands_Heavy BuildIndex From Cache
[2025.06.05-05.35.21:626][  0]LogPoseSearch: b9ad6da2b4015614aa861a663ab0dc2c7376fcdd - PSD_Traversal BeginCache
[2025.06.05-05.35.21:628][  0]LogPoseSearch: b9ad6da2b4015614aa861a663ab0dc2c7376fcdd - PSD_Traversal BuildIndex From Cache
[2025.06.05-05.35.21:637][  0]LogPoseSearch: 1f816991d0c81fa165c9258a64c0e110cb545466 - PSD_Dense_Jumps_FromTraversal BeginCache
[2025.06.05-05.35.21:638][  0]LogPoseSearch: 1f816991d0c81fa165c9258a64c0e110cb545466 - PSD_Dense_Jumps_FromTraversal BuildIndex From Cache
[2025.06.05-05.35.21:647][  0]LogPoseSearch: 44dfcd0439dfacb33784494a07f81e5c4107264f - PSD_Dense_Stand_Walk_FromTraversal BeginCache
[2025.06.05-05.35.21:647][  0]LogPoseSearch: 44dfcd0439dfacb33784494a07f81e5c4107264f - PSD_Dense_Stand_Walk_FromTraversal BuildIndex From Cache
[2025.06.05-05.35.21:648][  0]LogPoseSearch: 2ef3b5bccd181d28d29d656af18a9552f7025b60 - PSD_Dense_Stand_Run_SpinTransition BeginCache
[2025.06.05-05.35.21:648][  0]LogPoseSearch: 2ef3b5bccd181d28d29d656af18a9552f7025b60 - PSD_Dense_Stand_Run_SpinTransition BuildIndex From Cache
[2025.06.05-05.35.21:648][  0]LogPoseSearch: c3b27113a33ed0195cb86def79fac28ef88b955a - PSD_Dense_Stand_Walk_SpinTransition BeginCache
[2025.06.05-05.35.21:648][  0]LogPoseSearch: c3b27113a33ed0195cb86def79fac28ef88b955a - PSD_Dense_Stand_Walk_SpinTransition BuildIndex From Cache
[2025.06.05-05.35.21:661][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_btm_shorts_nrm...
[2025.06.05-05.35.21:663][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_top_crewneckt_nrm...
[2025.06.05-05.35.21:664][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.06.05-05.35.21:664][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm...
[2025.06.05-05.35.21:664][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants...
[2025.06.05-05.35.21:669][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.06.05-05.35.21:670][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_shs_flipflops...
[2025.06.05-05.35.21:835][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.21:837][  0]LogSkeletalMesh: Built Skeletal Mesh [0.17s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops
[2025.06.05-05.35.21:959][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-05.35.21:993][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.21:995][  0]LogSkeletalMesh: Built Skeletal Mesh [0.34s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm
[2025.06.05-05.35.21:996][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_casualsneakers...
[2025.06.05-05.35.22:008][  0]LogPoseSearch: 2fb236583b053e85334fffc7f28ad671a7ba6fcd - PSD_Dense_Crouch_Idle BeginCache
[2025.06.05-05.35.22:009][  0]LogPoseSearch: 2fb236583b053e85334fffc7f28ad671a7ba6fcd - PSD_Dense_Crouch_Idle BuildIndex From Cache
[2025.06.05-05.35.22:046][  0]LogPoseSearch: 3eaff9342445c24b7281b43f7e167cf00b79697d - PSD_Dense_Crouch_Loop BeginCache
[2025.06.05-05.35.22:047][  0]LogPoseSearch: 3eaff9342445c24b7281b43f7e167cf00b79697d - PSD_Dense_Crouch_Loop BuildIndex From Cache
[2025.06.05-05.35.22:092][  0]LogPoseSearch: e5bb22fa3f0832f6b1de4740e665f8b8a0cc4a40 - PSD_Dense_Crouch_Pivot BeginCache
[2025.06.05-05.35.22:094][  0]LogPoseSearch: e5bb22fa3f0832f6b1de4740e665f8b8a0cc4a40 - PSD_Dense_Crouch_Pivot BuildIndex From Cache
[2025.06.05-05.35.22:139][  0]LogPoseSearch: 10463b9b5d9a2019be2ed12e5f4f9c3378a2c9df - PSD_Dense_Crouch_Start BeginCache
[2025.06.05-05.35.22:140][  0]LogPoseSearch: 10463b9b5d9a2019be2ed12e5f4f9c3378a2c9df - PSD_Dense_Crouch_Start BuildIndex From Cache
[2025.06.05-05.35.22:169][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.22:172][  0]LogSkeletalMesh: Built Skeletal Mesh [0.51s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants
[2025.06.05-05.35.22:173][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_MC_FaceMesh...
[2025.06.05-05.35.22:190][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.22:196][  0]LogSkeletalMesh: Built Skeletal Mesh [0.53s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.06.05-05.35.22:197][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_Child2_FaceMesh...
[2025.06.05-05.35.22:202][  0]LogPoseSearch: fb459193d12be90ae89d82deeb58b955bd5ded69 - PSD_Dense_Crouch_Stops BeginCache
[2025.06.05-05.35.22:207][  0]LogPoseSearch: fb459193d12be90ae89d82deeb58b955bd5ded69 - PSD_Dense_Crouch_Stops BuildIndex From Cache
[2025.06.05-05.35.22:311][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.22:326][  0]LogSkeletalMesh: Built Skeletal Mesh [0.35s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-05.35.22:339][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.06.05-05.35.22:488][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.22:492][  0]LogSkeletalMesh: Built Skeletal Mesh [0.50s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers
[2025.06.05-05.35.22:494][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.06.05-05.35.22:948][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.22:952][  0]LogSkeletalMesh: Built Skeletal Mesh [0.64s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.06.05-05.35.22:952][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-05.35.22:985][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.06.05-05.35.23:126][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.23:128][  0]LogSkeletalMesh: Built Skeletal Mesh [0.18s] /Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-05.35.23:130][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-05.35.23:365][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.23:369][  0]LogSkeletalMesh: Built Skeletal Mesh [0.24s] /Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-05.35.23:655][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.23:658][  0]LogSkeletalMesh: Built Skeletal Mesh [2.00s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm
[2025.06.05-05.35.23:663][  0]LogSkeletalMesh: Building Skeletal Mesh Kellan_FaceMesh...
[2025.06.05-05.35.23:687][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.23:696][  0]LogSkeletalMesh: Built Skeletal Mesh [2.03s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm
[2025.06.05-05.35.23:697][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.23:703][  0]LogSkeletalMesh: Built Skeletal Mesh [2.04s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.06.05-05.35.23:987][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'DefaultLevel'.
[2025.06.05-05.35.23:987][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-05.35.24:039][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.24:043][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh
[2025.06.05-05.35.24:044][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.05-05.35.33:747][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.33:764][  0]LogSkeletalMesh: Built Skeletal Mesh [11.59s] /Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh
[2025.06.05-05.35.33:844][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.33:863][  0]LogSkeletalMesh: Built Skeletal Mesh [11.67s] /Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh
[2025.06.05-05.35.33:925][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-05.35.33:979][  0]LogSkeletalMesh: Built Skeletal Mesh [11.49s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.06.05-05.35.34:073][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.05-05.35.34:285][  0]LogUObjectHash: Compacting FUObjectHashTables data took   2.34ms
[2025.06.05-05.35.34:288][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.05-05.35.34:288][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.09ms to complete.
[2025.06.05-05.35.34:299][  0]LogUnrealEdMisc: Total Editor Startup Time, took 36.992
[2025.06.05-05.35.34:529][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.06.05-05.35.34:634][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-05.35.34:701][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-05.35.34:770][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-05.35.34:831][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-05.35.34:876][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-05.35.34:876][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.05-05.35.34:876][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-05.35.34:876][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.05-05.35.34:877][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-05.35.34:877][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.05-05.35.34:877][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-05.35.34:877][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.05-05.35.34:878][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-05.35.34:878][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.05-05.35.34:878][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-05.35.34:878][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.05-05.35.34:878][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-05.35.34:878][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.05-05.35.34:878][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-05.35.34:878][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.05-05.35.34:879][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-05.35.34:879][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.05-05.35.34:879][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-05.35.34:880][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.05-05.35.35:037][  0]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.35.35:060][  0]LogStall: Startup...
[2025.06.05-05.35.35:063][  0]LogStall: Startup complete.
[2025.06.05-05.35.35:091][  0]LogLoad: (Engine Initialization) Total time: 37.79 seconds
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.35.35:424][  0]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.35.35:424][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.05-05.35.35:424][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.05-05.35.35:435][  0]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Config/DefaultEngine.ini'
[2025.06.05-05.35.35:668][  0]LogSlate: Took 0.000179 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.05-05.35.35:672][  0]LogSlate: Took 0.000168 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.05-05.35.35:674][  0]LogSlate: Took 0.000139 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.05-05.35.35:675][  0]LogSlate: Took 0.000108 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.05-05.35.35:741][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.06.05-05.35.35:741][  0]LogStreaming: Display: FlushAsyncLoading(342): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-05.35.35:745][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.06.05-05.35.35:746][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.06.05-05.35.35:746][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.05-05.35.35:822][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.05-05.35.35:822][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.06.05-05.35.35:823][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.06.05-05.35.35:824][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.06.05-05.35.35:824][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.05-05.35.35:893][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.05-05.35.35:894][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.06.05-05.35.35:941][  0]LogSlate: Took 0.000584 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.05-05.35.36:688][  0]LogD3D12RHI: Creating RTPSO with 24 shaders (0 cached, 24 new) took 19.06 ms. Compile time 14.95 ms, link time 4.07 ms.
[2025.06.05-05.35.36:697][  0]LogD3D12RHI: Creating RTPSO with 197 shaders (0 cached, 197 new) took 130.46 ms. Compile time 119.22 ms, link time 11.09 ms.
[2025.06.05-05.35.36:767][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-05.35.36:792][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.06.05-05.35.36:792][  0]LogFab: Display: Logging in using exchange code
[2025.06.05-05.35.36:792][  0]LogFab: Display: Reading exchange code from commandline
[2025.06.05-05.35.36:792][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.06.05-05.35.36:792][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... started...
[2025.06.05-05.35.36:792][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... took 228 us
[2025.06.05-05.35.36:792][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.05-05.35.36:838][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.05-05.35.36:845][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 52.366 ms (total: 52.595 ms)
[2025.06.05-05.35.36:845][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-05.35.36:864][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-05.35.36:864][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-05.35.36:864][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_Glossy was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Glossy has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-05.35.36:864][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/material which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-05.35.36:864][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/material was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/material has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/material.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-05.35.36:864][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-05.35.36:864][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-05.35.36:864][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-05.35.36:864][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_Dark was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Dark has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-05.35.36:864][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-05.35.36:864][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVfront, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-05.35.36:864][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-05.35.36:864][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVback, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-05.35.36:864][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-05.35.36:864][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Washingmachine/steel, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-05.35.36:874][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BaoliEditor Win64 Development
[2025.06.05-05.35.37:096][  1]LogAssetRegistry: AssetRegistryGather time 0.1753s: AssetDataDiscovery 0.0271s, AssetDataGather 0.0258s, StoreResults 0.1224s. Wall time 35.6920s.
	NumCachedDirectories 0. NumUncachedDirectories 3058. NumCachedFiles 16171. NumUncachedFiles 0.
	BackgroundTickInterruptions 13.
[2025.06.05-05.35.37:201][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.06.05-05.35.37:280][  1]LogCollectionManager: Fixed up redirectors for 1 collections in 0.000125 seconds (updated 1 objects)
[2025.06.05-05.35.37:628][  1]LogSourceControl: Uncontrolled asset enumeration finished in 0.426165 seconds (Found 7976 uncontrolled assets)
[2025.06.05-05.35.37:899][  1]LogMaterial: Display: Material /InterchangeAssets/gltf/M_Default.M_Default needed to have new flag set bUsedWithNanite !
[2025.06.05-05.35.37:934][  1]MapCheck: Warning: M_Default Material /InterchangeAssets/gltf/M_Default.M_Default was missing the usage flag bUsedWithNanite. If the material asset is not re-saved, it may not render correctly when run outside the editor. Fix
[2025.06.05-05.35.38:277][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.06.05-05.35.38:690][  2]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 33.961521
[2025.06.05-05.35.38:691][  2]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.05-05.35.38:696][  2]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 34.355938
[2025.06.05-05.35.38:745][  3]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-05.35.40:198][  6]LogPoseSearch: 50dc2d6fc66385f69c3f4316520b5453bbe11082 - PSD_Sparse_Stand_Walk_Stops BeginCache
[2025.06.05-05.35.40:198][  6]LogPoseSearch: 50dc2d6fc66385f69c3f4316520b5453bbe11082 - PSD_Sparse_Stand_Walk_Stops BuildIndex From Cache
[2025.06.05-05.35.40:199][  6]LogPoseSearch: 7bd24e04e6106f1973227ab2f5dd916a0214b232 - PSD_Sparse_Stand_Walk_Starts BeginCache
[2025.06.05-05.35.40:200][  6]LogPoseSearch: 7bd24e04e6106f1973227ab2f5dd916a0214b232 - PSD_Sparse_Stand_Walk_Starts BuildIndex From Cache
[2025.06.05-05.35.40:201][  6]LogPoseSearch: 6c818e5f72e0650bcaac1c26c2a2af0097681f03 - PSD_Sparse_Stand_Walk_Pivots BeginCache
[2025.06.05-05.35.40:202][  6]LogPoseSearch: 6c818e5f72e0650bcaac1c26c2a2af0097681f03 - PSD_Sparse_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.05-05.35.40:202][  6]LogPoseSearch: a53f5d87ef31e2efb2cffa95e177eb2de30ea68b - PSD_Sparse_Stand_Walk_Loops BeginCache
[2025.06.05-05.35.40:202][  6]LogPoseSearch: a53f5d87ef31e2efb2cffa95e177eb2de30ea68b - PSD_Sparse_Stand_Walk_Loops BuildIndex From Cache
[2025.06.05-05.35.40:203][  6]LogPoseSearch: 64befbf5d961ed5ac476d534110da8895e69df96 - PSD_Sparse_Stand_Run_Stops BeginCache
[2025.06.05-05.35.40:203][  6]LogPoseSearch: 64befbf5d961ed5ac476d534110da8895e69df96 - PSD_Sparse_Stand_Run_Stops BuildIndex From Cache
[2025.06.05-05.35.40:203][  6]LogPoseSearch: 2a285cc36d3969680553c96c4cc33908d8d326c8 - PSD_Sparse_Stand_Run_Starts BeginCache
[2025.06.05-05.35.40:204][  6]LogPoseSearch: 2a285cc36d3969680553c96c4cc33908d8d326c8 - PSD_Sparse_Stand_Run_Starts BuildIndex From Cache
[2025.06.05-05.35.40:205][  6]LogPoseSearch: df8620db2cd0e21552cf91b1d3cd5aa199f8e4ba - PSD_Sparse_Stand_Run_Pivots BeginCache
[2025.06.05-05.35.40:206][  6]LogPoseSearch: 20bcf12fcf57878594cfd98261e8196a850d3275 - PSD_Sparse_Stand_Run_Loops BeginCache
[2025.06.05-05.35.40:207][  6]LogPoseSearch: 093bf032f877970de263de3430d86e0291f0b843 - PSD_Dense_Stand_Walk_Stops BeginCache
[2025.06.05-05.35.40:207][  6]LogPoseSearch: 20bcf12fcf57878594cfd98261e8196a850d3275 - PSD_Sparse_Stand_Run_Loops BuildIndex From Cache
[2025.06.05-05.35.40:208][  6]LogPoseSearch: 58635c6253bdbf281b54ac2883681f632df4ac5a - PSD_Dense_Stand_TurnInPlace BeginCache
[2025.06.05-05.35.40:208][  6]LogPoseSearch: df8620db2cd0e21552cf91b1d3cd5aa199f8e4ba - PSD_Sparse_Stand_Run_Pivots BuildIndex From Cache
[2025.06.05-05.35.40:209][  6]LogPoseSearch: 093bf032f877970de263de3430d86e0291f0b843 - PSD_Dense_Stand_Walk_Stops BuildIndex From Cache
[2025.06.05-05.35.40:209][  6]LogPoseSearch: 58635c6253bdbf281b54ac2883681f632df4ac5a - PSD_Dense_Stand_TurnInPlace BuildIndex From Cache
[2025.06.05-05.35.40:209][  6]LogPoseSearch: aa8f1551ead5ebb7649a9c0cf3f329785019c495 - PSD_Dense_Stand_Run_FromTraversal BeginCache
[2025.06.05-05.35.40:209][  6]LogPoseSearch: aa8f1551ead5ebb7649a9c0cf3f329785019c495 - PSD_Dense_Stand_Run_FromTraversal BuildIndex From Cache
[2025.06.05-05.35.40:210][  6]LogPoseSearch: 3c18bdf4eafddb521f27fde8b603d69f34423c6e - PSD_Dense_Stand_Walk_Starts BeginCache
[2025.06.05-05.35.40:211][  6]LogPoseSearch: 75620188eb8a82eb61257a3e37062d404180d1eb - PSD_Dense_Stand_Walk_Pivots BeginCache
[2025.06.05-05.35.40:212][  6]LogPoseSearch: 3c18bdf4eafddb521f27fde8b603d69f34423c6e - PSD_Dense_Stand_Walk_Starts BuildIndex From Cache
[2025.06.05-05.35.40:212][  6]LogPoseSearch: ba3a46564c5498cbb68507c1ccbbd585a3fc84d4 - PSD_Dense_Stand_Walk_Loops BeginCache
[2025.06.05-05.35.40:212][  6]LogPoseSearch: 75620188eb8a82eb61257a3e37062d404180d1eb - PSD_Dense_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.05-05.35.40:213][  6]LogPoseSearch: ba3a46564c5498cbb68507c1ccbbd585a3fc84d4 - PSD_Dense_Stand_Walk_Loops BuildIndex From Cache
[2025.06.05-05.35.40:213][  6]LogPoseSearch: 54f0f90cd23005d37fa1e725d1b27c86bd11c9b3 - PSD_Dense_Stand_Walk_Lands_Light BeginCache
[2025.06.05-05.35.40:213][  6]LogPoseSearch: 54f0f90cd23005d37fa1e725d1b27c86bd11c9b3 - PSD_Dense_Stand_Walk_Lands_Light BuildIndex From Cache
[2025.06.05-05.35.40:214][  6]LogPoseSearch: d9aafe8d3e39205b5f2061763c086eddbfb05fac - PSD_Dense_Stand_Run_Stops BeginCache
[2025.06.05-05.35.40:215][  6]LogPoseSearch: d9aafe8d3e39205b5f2061763c086eddbfb05fac - PSD_Dense_Stand_Run_Stops BuildIndex From Cache
[2025.06.05-05.35.40:215][  6]LogPoseSearch: d8d5fe007588aee9a3da79e9f1a75f582c1b9f99 - PSD_Dense_Stand_Run_Starts BeginCache
[2025.06.05-05.35.40:215][  6]LogPoseSearch: d8d5fe007588aee9a3da79e9f1a75f582c1b9f99 - PSD_Dense_Stand_Run_Starts BuildIndex From Cache
[2025.06.05-05.35.40:215][  6]LogPoseSearch: b53a49cb986c56c8a7f2626bb2d2b7429fbf1afb - PSD_Dense_Stand_Run_Pivots BeginCache
[2025.06.05-05.35.40:217][  6]LogPoseSearch: 570ea0e9de87396e5797a333746ddd45125a84d2 - PSD_Dense_Stand_Run_Loops BeginCache
[2025.06.05-05.35.40:217][  6]LogPoseSearch: 570ea0e9de87396e5797a333746ddd45125a84d2 - PSD_Dense_Stand_Run_Loops BuildIndex From Cache
[2025.06.05-05.35.40:218][  6]LogPoseSearch: 360967ed92817bbce2fc65a7f0c26e89ee4b6373 - PSD_Dense_Stand_Run_Lands_Light BeginCache
[2025.06.05-05.35.40:218][  6]LogPoseSearch: 360967ed92817bbce2fc65a7f0c26e89ee4b6373 - PSD_Dense_Stand_Run_Lands_Light BuildIndex From Cache
[2025.06.05-05.35.40:218][  6]LogPoseSearch: 3faefe98948c1fcc929efd51f12856b3b9f3b947 - PSD_Dense_Stand_Idles BeginCache
[2025.06.05-05.35.40:219][  6]LogPoseSearch: 3faefe98948c1fcc929efd51f12856b3b9f3b947 - PSD_Dense_Stand_Idles BuildIndex From Cache
[2025.06.05-05.35.40:219][  6]LogPoseSearch: 914c12febe09d1d5c60f3a968f6f99ac6ee0695d - PSD_Dense_Stand_Idle_Lands_Light BeginCache
[2025.06.05-05.35.40:221][  6]LogPoseSearch: 914c12febe09d1d5c60f3a968f6f99ac6ee0695d - PSD_Dense_Stand_Idle_Lands_Light BuildIndex From Cache
[2025.06.05-05.35.40:223][  6]LogPoseSearch: b53a49cb986c56c8a7f2626bb2d2b7429fbf1afb - PSD_Dense_Stand_Run_Pivots BuildIndex From Cache
[2025.06.05-05.35.40:315][  6]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.05-05.35.40:886][ 10]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 36.519695
[2025.06.05-05.35.40:888][ 10]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 348165457
[2025.06.05-05.35.40:888][ 10]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 36.519695, Update Interval: 351.192352
[2025.06.05-05.35.40:889][ 11]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-05.35.58:925][933]LogAssetEditorSubsystem: Opening Asset editor for BlendSpace /Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.BS_Neutral_AO_Stand
[2025.06.05-05.35.59:103][933]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.05-05.35.59:111][933]LogStreaming: Display: FlushAsyncLoading(356): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-05.35.59:413][933]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_0:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-05.35.59:632][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.00:940][933]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.05-05.36.01:056][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.01:236][933]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/Characters/Animations/CasRetarget/Cas_Skeleton.Cas_Skeleton
[2025.06.05-05.36.01:278][933]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.05-05.36.01:304][933]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_2:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-05.36.01:372][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.01:737][933]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.05-05.36.01:793][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.01:864][933]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter
[2025.06.05-05.36.01:878][933]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.05-05.36.01:953][933]LogTemp: Display: rdBPTools: Failed to load rdBPTools config ini file
[2025.06.05-05.36.01:953][933]LogTemp: Display: Handle AssetOpenedInEditor - BlueprintEditor...
[2025.06.05-05.36.01:954][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.05:878][933]LogBlueprintEditor: Perf: 3.9 total seconds to load all 13 blueprint libraries in project. Avoid references to content in blueprint libraries to shorten this time.
[2025.06.05-05.36.05:878][933]LogBlueprintEditor: Perf: 3.6 seconds loading: /Game/UltraDynamicSky/Blueprints/Functions/UltraDynamicWeather_Functions
[2025.06.05-05.36.07:805][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.07:998][933]LogAssetEditorSubsystem: Opening Asset editor for ControlRigBlueprint /Game/Characters/BaoliMC/CR_HeadManager.CR_HeadManager
[2025.06.05-05.36.07:998][933]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.05-05.36.08:064][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.08:162][933]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.06.05-05.36.08:319][933]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_6:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.05-05.36.08:327][933]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_6:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.05-05.36.08:735][933]LogUObjectHash: Compacting FUObjectHashTables data took   2.57ms
[2025.06.05-05.36.09:220][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.09:475][933]LogAssetEditorSubsystem: Opening Asset editor for InputMappingContext /Game/Input/IMC_GDCMotionMatching.IMC_GDCMotionMatching
[2025.06.05-05.36.09:506][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.09:848][933]LogAssetEditorSubsystem: Opening Asset editor for ChooserTable /Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Dense.CHT_PoseSearchDatabases_Dense
[2025.06.05-05.36.09:909][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.09:951][933]LogAssetEditorSubsystem: Opening Asset editor for AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter
[2025.06.05-05.36.09:951][933]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.06.05-05.36.09:966][933]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.06.05-05.36.09:988][933]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-05.36.10:025][933]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.10:030][933]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.10:204][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.10:675][933]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.06.05-05.36.10:718][933]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-05.36.11:529][933]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.06.05-05.36.11:546][933]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.11:547][933]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.11:656][933]LogD3D12RHI: Creating RTPSO with 21 shaders (13 cached, 8 new) took 42.88 ms. Compile time 39.80 ms, link time 3.05 ms.
[2025.06.05-05.36.11:669][933]LogD3D12RHI: Creating RTPSO with 232 shaders (228 cached, 4 new) took 57.13 ms. Compile time 40.80 ms, link time 16.25 ms.
[2025.06.05-05.36.11:801][933]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.11:888][933]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.11:889][933]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.11:957][933]LogSlate: Took 0.024048 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.06.05-05.36.12:700][934]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.12:701][934]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.12:834][935]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.12:835][935]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.12:838][935]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.12:855][935]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.13:812][936]LogD3D12RHI: Creating RTPSO with 234 shaders (0 cached, 2 new) took 975.07 ms. Compile time 1.90 ms, link time 973.10 ms.
[2025.06.05-05.36.13:845][936]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.13:856][936]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.13:873][937]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.13:907][937]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.13:930][938]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.06.05-05.36.13:940][938]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.13:943][938]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.13:956][939]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.13:959][939]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.13:972][940]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.13:975][940]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.13:988][941]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.13:990][941]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.13:990][941]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.13:990][941]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.13:990][941]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.13:990][941]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.13:991][941]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.13:991][941]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.13:991][941]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.13:991][941]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.13:991][941]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.13:991][941]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.13:991][941]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.13:991][941]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.13:991][941]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.13:991][941]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.13:991][941]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.14:002][942]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.14:005][942]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.14:018][943]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.14:021][943]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.14:021][943]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.14:022][943]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.14:035][944]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.14:037][944]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-05.36.14:037][944]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 11:00:58
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-05.36.14:038][944]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-05.36.14:053][945]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.14:071][946]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.14:088][947]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.14:106][948]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.36.37:867][490]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.36.38:862][560]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.37.00:339][269]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.05-05.37.42:278][451]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.37.42:278][451]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.37.42:295][452]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.06.05-05.37.42:304][452]LogActor: Warning: BP_Almirah_C /Engine/Transient.World_15:PersistentLevel.BP_Almirah_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.05-05.37.42:322][453]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.06.05-05.37.42:367][454]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.06.05-05.37.42:376][454]LogActor: Warning: BP_Bed_C /Engine/Transient.World_17:PersistentLevel.BP_Bed_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.05-05.37.42:395][455]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.06.05-05.37.42:420][456]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.06.05-05.37.42:471][458]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.37.42:471][458]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.37.43:009][460]LogD3D12RHI: Creating RTPSO with 237 shaders (0 cached, 1 new) took 514.53 ms. Compile time 1.18 ms, link time 513.31 ms.
[2025.06.05-05.37.43:036][461]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.06.05-05.37.43:068][462]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.37.43:068][462]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.37.44:741][556]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_Almirah.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bed.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bulb.uasset'
[2025.06.05-05.37.48:440][786]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_ChestActorPawn.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Fan.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_FlashLight.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameMinimal.uasset H:/P4/dev/Baoli/Content/Blueprints/CBP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/GM_Sandbox.uasset H:/P4/dev/Baoli/Content/Blueprints/Kettle_BP.uasset'
[2025.06.05-05.37.49:500][859]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/QTE.uasset'
[2025.06.05-05.38.08:025][121]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.06.05-05.38.08:192][124]LogD3D12RHI: Creating RTPSO with 238 shaders (0 cached, 1 new) took 69.29 ms. Compile time 1.54 ms, link time 67.70 ms.
[2025.06.05-05.38.08:278][126]LogD3D12RHI: Creating RTPSO with 239 shaders (0 cached, 1 new) took 70.49 ms. Compile time 1.55 ms, link time 68.89 ms.
[2025.06.05-05.38.08:408][130]LogD3D12RHI: Creating RTPSO with 240 shaders (0 cached, 1 new) took 68.32 ms. Compile time 1.73 ms, link time 66.55 ms.
[2025.06.05-05.38.08:652][132]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_22
[2025.06.05-05.38.08:717][133]LogD3D12RHI: Creating RTPSO with 241 shaders (0 cached, 1 new) took 67.14 ms. Compile time 1.55 ms, link time 65.54 ms.
[2025.06.05-05.38.08:728][133]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.38.08:728][133]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.38.08:804][137]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.38.08:804][137]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-05.38.11:380][330]LogSlate: Took 0.016056 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.06.05-05.38.17:914][847]LogUObjectHash: Compacting FUObjectHashTables data took   1.82ms
[2025.06.05-05.38.19:484][847]LogSlate: Window 'Save Content' being destroyed
[2025.06.05-05.38.19:569][847]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.20:915][847]LogSlate: Window 'Check Out Assets' being destroyed
[2025.06.05-05.38.21:002][847]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.21:074][847]LogSourceControl: Attempting 'p4 edit H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.21:100][847]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.05-05.38.21:146][847]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-05.38.21:146][847]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-05.38.21:146][847]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset" SILENT=true
[2025.06.05-05.38.21:316][847]LogSavePackage: Moving output files for package: /Game/Blueprints/ABP_SandboxCharacter
[2025.06.05-05.38.21:316][847]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacterFE1A3558481908D9578BA18992EBECA4.tmp' to 'H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.21:346][847]LogFileHelpers: InternalPromptForCheckoutAndSave took 245.413 ms
[2025.06.05-05.38.21:387][847]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.05-05.38.21:387][847]LogContentValidation: Enabled validators:
[2025.06.05-05.38.21:387][847]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.05-05.38.21:387][847]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.05-05.38.21:387][847]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.05-05.38.21:387][847]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.05-05.38.21:387][847]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.05-05.38.21:387][847]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.05-05.38.21:387][847]LogContentValidation: 	/Script/AssetReferenceRestrictions.EditorValidator_PluginAssetReferences
[2025.06.05-05.38.21:387][847]LogContentValidation: 	/Script/AssetReferenceRestrictions.AssetValidator_AssetReferenceRestrictions
[2025.06.05-05.38.21:387][847]LogContentValidation: 	/Script/GameFeaturesEditor.IllegalPluginDependenciesValidator
[2025.06.05-05.38.21:387][847]AssetCheck: /Game/Blueprints/ABP_SandboxCharacter Validating asset
[2025.06.05-05.38.24:281][ 57]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset'
[2025.06.05-05.38.26:459][ 57]LogSlate: Window 'Check Out Assets' being destroyed
[2025.06.05-05.38.26:534][ 57]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset'
[2025.06.05-05.38.26:608][ 57]LogSourceControl: Attempting 'p4 edit H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset'
[2025.06.05-05.38.26:641][ 57]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.05-05.38.26:678][ 57]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/BP_BaoliCharacter] ([2] browsable assets)...
[2025.06.05-05.38.26:678][ 57]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_BaoliCharacter]
[2025.06.05-05.38.26:678][ 57]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_BaoliCharacter" FILE="H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset" SILENT=true
[2025.06.05-05.38.26:748][ 57]LogSavePackage: Moving output files for package: /Game/Blueprints/BP_BaoliCharacter
[2025.06.05-05.38.26:748][ 57]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/BP_BaoliCharacterAE0F335E460B77AB1B7651BE473EB907.tmp' to 'H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset'
[2025.06.05-05.38.26:755][ 57]LogFileHelpers: InternalPromptForCheckoutAndSave took 115.048 ms (total: 360.461 ms)
[2025.06.05-05.38.26:794][ 57]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.05-05.38.26:794][ 57]LogContentValidation: Enabled validators:
[2025.06.05-05.38.26:794][ 57]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.05-05.38.26:794][ 57]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.05-05.38.26:794][ 57]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.05-05.38.26:794][ 57]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.05-05.38.26:794][ 57]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.05-05.38.26:794][ 57]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.05-05.38.26:794][ 57]LogContentValidation: 	/Script/AssetReferenceRestrictions.EditorValidator_PluginAssetReferences
[2025.06.05-05.38.26:794][ 57]LogContentValidation: 	/Script/AssetReferenceRestrictions.AssetValidator_AssetReferenceRestrictions
[2025.06.05-05.38.26:794][ 57]LogContentValidation: 	/Script/GameFeaturesEditor.IllegalPluginDependenciesValidator
[2025.06.05-05.38.26:795][ 57]AssetCheck: /Game/Blueprints/BP_BaoliCharacter Validating asset
[2025.06.05-05.38.29:399][247]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.29:424][247]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.05-05.38.29:460][247]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-05.38.29:460][247]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-05.38.29:460][247]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset" SILENT=true
[2025.06.05-05.38.29:591][247]LogSavePackage: Moving output files for package: /Game/Blueprints/ABP_SandboxCharacter
[2025.06.05-05.38.29:591][247]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter09AC7582421F11DC1AE10BB008A32222.tmp' to 'H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.29:598][247]LogFileHelpers: InternalPromptForCheckoutAndSave took 175.100 ms (total: 535.562 ms)
[2025.06.05-05.38.29:637][247]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.05-05.38.29:637][247]LogContentValidation: Enabled validators:
[2025.06.05-05.38.29:637][247]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.05-05.38.29:637][247]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.05-05.38.29:637][247]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.05-05.38.29:637][247]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.05-05.38.29:637][247]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.05-05.38.29:637][247]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.05-05.38.29:637][247]LogContentValidation: 	/Script/AssetReferenceRestrictions.EditorValidator_PluginAssetReferences
[2025.06.05-05.38.29:637][247]LogContentValidation: 	/Script/AssetReferenceRestrictions.AssetValidator_AssetReferenceRestrictions
[2025.06.05-05.38.29:637][247]LogContentValidation: 	/Script/GameFeaturesEditor.IllegalPluginDependenciesValidator
[2025.06.05-05.38.29:638][247]AssetCheck: /Game/Blueprints/ABP_SandboxCharacter Validating asset
[2025.06.05-05.38.33:319][487]LogSelectionDetails: Warning: NavigateToFunctionSource:  Unable to find source file and line number for 'UKismetMathLibrary::BreakTransform' [Element not found.]
[2025.06.05-05.38.35:958][663]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.36:979][731]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.38:104][806]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.38:127][806]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.05-05.38.38:161][806]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-05.38.38:161][806]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-05.38.38:161][806]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset" SILENT=true
[2025.06.05-05.38.38:293][806]LogSavePackage: Moving output files for package: /Game/Blueprints/ABP_SandboxCharacter
[2025.06.05-05.38.38:293][806]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacterA457DCAA47C63BBBBE8DD0ADD6D7CC95.tmp' to 'H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.38:302][806]LogFileHelpers: InternalPromptForCheckoutAndSave took 175.516 ms (total: 711.078 ms)
[2025.06.05-05.38.38:343][806]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.05-05.38.38:343][806]LogContentValidation: Enabled validators:
[2025.06.05-05.38.38:343][806]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.05-05.38.38:343][806]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.05-05.38.38:343][806]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.05-05.38.38:343][806]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.05-05.38.38:343][806]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.05-05.38.38:343][806]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.05-05.38.38:344][806]LogContentValidation: 	/Script/AssetReferenceRestrictions.EditorValidator_PluginAssetReferences
[2025.06.05-05.38.38:344][806]LogContentValidation: 	/Script/AssetReferenceRestrictions.AssetValidator_AssetReferenceRestrictions
[2025.06.05-05.38.38:344][806]LogContentValidation: 	/Script/GameFeaturesEditor.IllegalPluginDependenciesValidator
[2025.06.05-05.38.38:344][806]AssetCheck: /Game/Blueprints/ABP_SandboxCharacter Validating asset
[2025.06.05-05.38.42:098][ 70]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.43:019][133]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.43:633][171]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.43:650][171]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.05-05.38.43:686][171]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-05.38.43:686][171]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-05.38.43:686][171]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset" SILENT=true
[2025.06.05-05.38.43:820][171]LogSavePackage: Moving output files for package: /Game/Blueprints/ABP_SandboxCharacter
[2025.06.05-05.38.43:820][171]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter9E5C01FD4DCBE7A6CAEFA39F3AC6B1E6.tmp' to 'H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.38.43:829][171]LogFileHelpers: InternalPromptForCheckoutAndSave took 178.390 ms (total: 889.469 ms)
[2025.06.05-05.38.43:869][171]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.05-05.38.43:870][171]LogContentValidation: Enabled validators:
[2025.06.05-05.38.43:870][171]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.05-05.38.43:870][171]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.05-05.38.43:870][171]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.05-05.38.43:870][171]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.05-05.38.43:870][171]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.05-05.38.43:870][171]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.05-05.38.43:870][171]LogContentValidation: 	/Script/AssetReferenceRestrictions.EditorValidator_PluginAssetReferences
[2025.06.05-05.38.43:870][171]LogContentValidation: 	/Script/AssetReferenceRestrictions.AssetValidator_AssetReferenceRestrictions
[2025.06.05-05.38.43:870][171]LogContentValidation: 	/Script/GameFeaturesEditor.IllegalPluginDependenciesValidator
[2025.06.05-05.38.43:870][171]AssetCheck: /Game/Blueprints/ABP_SandboxCharacter Validating asset
[2025.06.05-05.39.29:135][714]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.39.30:141][793]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.41.32:041][864]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 387.725800
[2025.06.05-05.41.32:334][887]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-05.41.32:334][887]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 388.005554, Update Interval: 341.472839
[2025.06.05-05.44.30:162][960]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.46.42:633][680]LogUObjectHash: Compacting FUObjectHashTables data took   0.86ms
[2025.06.05-05.46.42:637][680]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Levels/DefaultLevel' took 0.034
[2025.06.05-05.46.42:638][680]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-05.46.42:638][680]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-05.46.42:638][680]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto1.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-05.46.42:773][680]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto1
[2025.06.05-05.46.42:773][680]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter_Auto1F99EE48C44AC4BC6163648A609E48144.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto1.uasset'
[2025.06.05-05.46.42:773][680]LogFileHelpers: Auto-saving content packages took 0.137
[2025.06.05-05.46.43:797][759]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.47.16:615][ 45]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 732.294800
[2025.06.05-05.47.16:969][ 62]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-05.47.16:969][ 62]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 732.636719, Update Interval: 342.666718
[2025.06.05-05.51.43:830][348]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.53.06:001][157]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1081.674316
[2025.06.05-05.53.06:287][177]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-05.53.06:287][177]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1081.946167, Update Interval: 350.450745
[2025.06.05-05.56.42:877][268]LogUObjectHash: Compacting FUObjectHashTables data took   0.86ms
[2025.06.05-05.56.42:880][268]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Levels/DefaultLevel' took 0.035
[2025.06.05-05.56.42:881][268]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-05.56.42:881][268]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-05.56.42:881][268]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto2.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-05.56.43:019][268]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto2
[2025.06.05-05.56.43:019][268]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter_Auto265404FDF4B80F2E85501679A2E7EE970.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto2.uasset'
[2025.06.05-05.56.43:020][268]LogFileHelpers: Auto-saving content packages took 0.140
[2025.06.05-05.56.44:047][347]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-05.59.11:725][989]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1447.384644
[2025.06.05-05.59.12:003][ 11]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-05.59.12:003][ 11]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1447.650513, Update Interval: 312.277588
[2025.06.05-06.01.44:098][881]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-06.04.44:396][942]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1780.042847
[2025.06.05-06.04.44:682][964]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-06.04.44:682][964]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1780.316650, Update Interval: 300.855133
[2025.06.05-06.06.43:132][922]LogUObjectHash: Compacting FUObjectHashTables data took   1.33ms
[2025.06.05-06.06.43:136][922]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Levels/DefaultLevel' took 0.040
[2025.06.05-06.06.43:138][922]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-06.06.43:145][922]OBJ SavePackage:     Rendered thumbnail for [AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter]
[2025.06.05-06.06.43:145][922]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-06.06.43:145][922]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto3.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-06.06.43:309][922]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto3
[2025.06.05-06.06.43:309][922]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter_Auto388459D7C46D14117173BEDB4774D668D.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto3.uasset'
[2025.06.05-06.06.43:310][922]LogFileHelpers: Auto-saving content packages took 0.174
[2025.06.05-06.06.44:345][  2]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-06.10.19:584][444]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2115.229248
[2025.06.05-06.10.19:874][466]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-06.10.19:875][467]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2115.506836, Update Interval: 355.074310
[2025.06.05-06.17.13:425][976]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2529.048584
[2025.06.05-06.17.13:710][998]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-06.17.13:710][998]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2529.320801, Update Interval: 348.480469
[2025.06.05-06.22.15:892][ 85]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-06.23.39:824][360]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2915.443115
[2025.06.05-06.23.40:093][379]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-06.23.40:094][379]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2915.698242, Update Interval: 325.684998
[2025.06.05-06.27.15:928][942]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-06.29.49:318][963]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3284.953613
[2025.06.05-06.29.49:627][986]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-06.29.49:627][986]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3285.235107, Update Interval: 308.913849
[2025.06.05-06.32.15:947][557]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-06.35.51:553][491]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3647.187012
[2025.06.05-06.35.51:835][513]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-06.35.51:835][513]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3647.456055, Update Interval: 331.288177
[2025.06.05-06.37.00:339][838]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.05-06.38.07:432][992]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-06.39.19:804][821]LogUObjectHash: Compacting FUObjectHashTables data took   0.90ms
[2025.06.05-06.39.19:807][821]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Levels/DefaultLevel' took 0.036
[2025.06.05-06.39.19:807][821]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-06.39.19:807][821]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-06.39.19:807][821]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto4.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-06.39.19:957][821]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto4
[2025.06.05-06.39.19:957][821]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter_Auto451FDA9784192CB46645044A5B8CAACC5.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto4.uasset'
[2025.06.05-06.39.19:959][821]LogFileHelpers: Auto-saving content packages took 0.152
[2025.06.05-06.39.20:979][895]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-06.42.20:961][539]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4036.584229
[2025.06.05-06.42.21:229][564]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-06.42.21:229][564]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4036.841309, Update Interval: 326.992401
[2025.06.05-06.44.21:008][190]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-06.48.37:262][ 47]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4412.747559
[2025.06.05-06.48.37:534][ 73]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-06.48.37:534][ 73]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4413.009277, Update Interval: 315.681641
[2025.06.05-06.53.47:808][193]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-06.54.32:579][409]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4767.937500
[2025.06.05-06.54.32:882][436]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-06.54.32:882][436]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4768.228027, Update Interval: 308.426758
[2025.06.05-07.00.16:870][140]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5112.122070
[2025.06.05-07.00.17:180][170]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-07.00.17:180][170]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5112.420410, Update Interval: 318.318420
[2025.06.05-07.04.43:266][953]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.05.29:959][520]LogUObjectHash: Compacting FUObjectHashTables data took   0.96ms
[2025.06.05-07.05.29:962][520]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Levels/DefaultLevel' took 0.035
[2025.06.05-07.05.29:963][520]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-07.05.29:963][520]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-07.05.29:963][520]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto5.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-07.05.30:097][520]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto5
[2025.06.05-07.05.30:097][520]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter_Auto51C10C00045AEAB77529B439FB6B48C10.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto5.uasset'
[2025.06.05-07.05.30:098][520]LogFileHelpers: Auto-saving content packages took 0.136
[2025.06.05-07.05.31:120][604]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.06.19:456][343]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5474.647461
[2025.06.05-07.06.19:720][370]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-07.06.19:720][370]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5474.901367, Update Interval: 319.631348
[2025.06.05-07.12.28:339][875]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5843.791016
[2025.06.05-07.12.28:598][901]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-07.12.28:598][901]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5844.040039, Update Interval: 356.085083
[2025.06.05-07.17.25:473][438]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.17.26:283][490]LogUObjectHash: Compacting FUObjectHashTables data took   1.96ms
[2025.06.05-07.17.26:312][490]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.17.26:312][490]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.17.26:318][490]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_13
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.17.26:318][490]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_13
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.19.06:125][151]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6241.810059
[2025.06.05-07.19.06:657][203]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-07.19.06:657][203]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6242.333496, Update Interval: 338.286682
[2025.06.05-07.19.53:690][992]LogUObjectHash: Compacting FUObjectHashTables data took   1.85ms
[2025.06.05-07.19.53:694][992]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Levels/DefaultLevel' took 0.042
[2025.06.05-07.19.53:694][992]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-07.19.53:700][992]OBJ SavePackage:     Rendered thumbnail for [AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter]
[2025.06.05-07.19.53:700][992]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-07.19.53:700][992]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto6.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-07.19.53:835][992]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto6
[2025.06.05-07.19.53:835][992]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter_Auto62679587544EFE9D3AFFDE8AEA9DADF86.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto6.uasset'
[2025.06.05-07.19.53:836][992]LogFileHelpers: Auto-saving content packages took 0.142
[2025.06.05-07.19.54:857][ 90]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.23.13:273][724]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-07.23.13:322][724]LogPlayLevel: [PlayLevel] Compiling ABP_SandboxCharacter before play...
[2025.06.05-07.23.13:543][724]LogUObjectHash: Compacting FUObjectHashTables data took   2.04ms
[2025.06.05-07.23.13:547][724]LogPlayLevel: PlayLevel: Blueprint regeneration took 228 ms (1 blueprints)
[2025.06.05-07.23.13:606][724]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.06.05-07.23.13:606][724]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.06.05-07.23.13:606][724]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-07.23.13:687][724]LogPlayLevel: PIE: StaticDuplicateObject took: (0.081067s)
[2025.06.05-07.23.13:687][724]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.081125s)
[2025.06.05-07.23.13:721][724]LogUObjectHash: Compacting FUObjectHashTables data took   1.71ms
[2025.06.05-07.23.13:723][724]r.RayTracing.Culling = "0"
[2025.06.05-07.23.13:723][724]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-07.23.13:743][724]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-07.23.13:745][724]LogPlayLevel: PIE: World Init took: (0.001919s)
[2025.06.05-07.23.13:746][724]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.06.05-07.23.13:746][724]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-07.23.13:746][724]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-07.23.13:746][724]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-07.23.13:746][724]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-07.23.13:746][724]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-07.23.13:746][724]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-07.23.13:746][724]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-07.23.13:746][724]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-07.23.13:746][724]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-07.23.13:746][724]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-07.23.13:746][724]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-07.23.13:748][724]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-07.23.13:786][724]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-07.23.13:786][724]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-07.23.13:787][724]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-07.23.13:787][724]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-07.23.13:788][724]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.06.05-07.23.13:788][724]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.06.05-07.23.13:790][724]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.06.05-07.23.13:790][724]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.06.05-07.23.13:790][724]LogInit: FAudioDevice initialized with ID 2.
[2025.06.05-07.23.13:790][724]LogAudio: Display: Audio Device (ID: 2) registered with world 'DefaultLevel'.
[2025.06.05-07.23.13:790][724]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.06.05-07.23.13:790][724]LogWindows: WindowsPlatformFeatures enabled
[2025.06.05-07.23.13:801][724]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-07.23.13:805][724]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-07.23.13:837][724]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-07.23.13:845][724]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-12.53.13
[2025.06.05-07.23.13:889][724]LogWorld: Bringing up level for play took: 0.084437
[2025.06.05-07.23.13:892][724]LogOnline: OSS: Created online subsystem instance for: :Context_24
[2025.06.05-07.23.14:048][724]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-07.23.14:048][724]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-07.23.14:048][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-07.23.14:048][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-07.23.14:048][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-07.23.14:048][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-07.23.14:048][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-07.23.14:048][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-07.23.14:049][724]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-07.23.14:049][724]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-07.23.14:049][724]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-07.23.14:049][724]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-07.23.14:049][724]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-07.23.14:049][724]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-07.23.14:049][724]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-07.23.14:049][724]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-07.23.14:133][724]PIE: Server logged in
[2025.06.05-07.23.14:135][724]PIE: Play in editor total start time 0.856 seconds.
[2025.06.05-07.23.17:408][725]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-07.23.17:464][725]LogD3D12RHI: Creating RTPSO with 24 shaders (20 cached, 4 new) took 18.05 ms. Compile time 11.61 ms, link time 6.35 ms.
[2025.06.05-07.23.17:564][725]LogAudioMixer: Display: Audio Buffer Underrun (starvation) detected. InstanceID=2
[2025.06.05-07.23.17:565][725]LogD3D12RHI: Creating RTPSO with 238 shaders (0 cached, 1 new) took 118.99 ms. Compile time 8.42 ms, link time 110.38 ms.
[2025.06.05-07.23.18:585][727]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'UE.Wave Player.Stereo (v1.0)': Interface change detected.
[2025.06.05-07.23.18:585][727]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'Array.Random Get.WaveAsset:Array': Newer version 'v1.1' found.
[2025.06.05-07.23.19:687][730]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_19
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.19:688][730]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_19
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.20:208][733]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_20
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.20:208][733]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_20
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.21:375][745]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_21
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.21:376][745]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_21
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.21:588][750]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_22
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.21:589][750]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_22
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.26:040][896]Cmd: LevelEditor.ToggleImmersive
[2025.06.05-07.23.26:151][896]LogSlate: Took 0.000214 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.05-07.23.50:817][747]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-07.23.50:817][747]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-07.23.50:852][747]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-07.23.50:853][747]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-07.23.50:853][747]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-07.23.50:855][747]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-07.23.50:873][747]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-07.23.50:885][747]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-07.23.50:959][747]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-07.23.50:962][747]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.06.05-07.23.50:962][747]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.05-07.23.50:965][747]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.05-07.23.51:071][747]LogUObjectHash: Compacting FUObjectHashTables data took   2.05ms
[2025.06.05-07.23.51:156][748]LogPlayLevel: Display: Destroying online subsystem :Context_24
[2025.06.05-07.23.51:509][753]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_23
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.51:509][753]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_23
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.51:713][758]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_24
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.51:714][758]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_24
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.23.59:703][ 14]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_23
[2025.06.05-07.24.00:635][ 41]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Dense.uasset H:/P4/dev/Baoli/Content/MetaHumans/Baoli_Child/MaleHair/Hair/Eyebrows_M_Dense.uasset H:/P4/dev/Baoli/Content/MetaHumans/Baoli_Child/MaleHair/Hair/Eyebrows_M_Dense_CardMesh_Group0_LOD0.uasset H:/P4/dev/Baoli/Content/MetaHumans/Baoli_Child/MaleHair/Hair/Eyebrows_M_Dense_CardMesh_Group0_LOD1.uasset H:/P4/dev/Baoli/Content/MetaHumans/Baoli_Child/MaleHair/Hair/Eyebrows_M_Dense_CardMesh_Group0_LOD2.uasset H:/P4/dev/Baoli/Content/MetaHumans/Baoli_Child/MaleHair/Hair/Eyebrows_M_Dense_CardMesh_Group0_LOD3.uasset H:/P4/dev/Baoli/Content/MetaHumans/Baoli_Child/MaleHair/Hair/Eyebrows_M_Dense_CardMesh_Group0_LOD4.uasset H:/P4/dev/Baoli/Content/MetaHumans/Common/MaleHair/Textures/Eyebrows_M_Dense_Cards/Compact/Eyebrows_M_Dense_CardsAtlas_Attribute.uasset H:/P4/dev/Baoli/Content/MetaHumans/Common/MaleHair/Textures/Eyebrows_M_Dense_Cards/Compact/Eyebrows_M_Dense_CardsAtlas_Tangent.uasset H:/P4/dev/Baoli/Content/MetaHumans/Baoli_Child/MaleHair/GroomBinding/Eyebrows_M_Dense_m_med_nrw_head_skmesh_Face_Archetype_Binding.uasset H:/P4/dev/Baoli/Content/MetaHumans/Common/Materials/HighlightsTextures/HighlightsMask_dense.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/MotionMatchingData/Databases/Crouch/PSD_Dense_Crouch_Idle.uasset'
[2025.06.05-07.24.06:773][243]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Dense.uasset'
[2025.06.05-07.24.07:728][276]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Dense.uasset'
[2025.06.05-07.24.08:737][308]Cmd: TRANSACTION UNDO
[2025.06.05-07.24.08:737][308]LogEditorTransaction: Undo Move Row(s)
[2025.06.05-07.24.09:385][308]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_0:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-07.24.09:405][308]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_2:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-07.24.09:494][308]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-07.24.14:023][450]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-07.24.14:035][450]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-07.24.14:035][450]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-07.24.14:101][450]LogPlayLevel: PIE: StaticDuplicateObject took: (0.065521s)
[2025.06.05-07.24.14:101][450]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.065618s)
[2025.06.05-07.24.14:136][450]LogUObjectHash: Compacting FUObjectHashTables data took   2.01ms
[2025.06.05-07.24.14:141][450]r.RayTracing.Culling = "0"
[2025.06.05-07.24.14:141][450]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-07.24.14:141][450]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-07.24.14:142][450]LogPlayLevel: PIE: World Init took: (0.001731s)
[2025.06.05-07.24.14:143][450]LogAudio: Display: Creating Audio Device:                 Id: 3, Scope: Unique, Realtime: True
[2025.06.05-07.24.14:143][450]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-07.24.14:143][450]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-07.24.14:143][450]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-07.24.14:143][450]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-07.24.14:143][450]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-07.24.14:143][450]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-07.24.14:143][450]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-07.24.14:143][450]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-07.24.14:143][450]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-07.24.14:143][450]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-07.24.14:143][450]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-07.24.14:146][450]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-07.24.14:181][450]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-07.24.14:181][450]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-07.24.14:181][450]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-07.24.14:181][450]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-07.24.14:183][450]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=3
[2025.06.05-07.24.14:183][450]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=3
[2025.06.05-07.24.14:185][450]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=3
[2025.06.05-07.24.14:185][450]LogInit: FAudioDevice initialized with ID 3.
[2025.06.05-07.24.14:185][450]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=3
[2025.06.05-07.24.14:185][450]LogAudio: Display: Audio Device (ID: 3) registered with world 'DefaultLevel'.
[2025.06.05-07.24.14:185][450]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 3
[2025.06.05-07.24.14:185][450]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-07.24.14:188][450]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-07.24.14:216][450]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-07.24.14:224][450]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-12.54.14
[2025.06.05-07.24.14:231][450]LogWorld: Bringing up level for play took: 0.042294
[2025.06.05-07.24.14:235][450]LogOnline: OSS: Created online subsystem instance for: :Context_26
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-07.24.14:253][450]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-07.24.14:266][450]PIE: Server logged in
[2025.06.05-07.24.14:269][450]PIE: Play in editor total start time 0.239 seconds.
[2025.06.05-07.24.14:418][451]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-07.24.16:057][500]PIE: Error: No database assets provided for motion matching.
[2025.06.05-07.24.54:907][805]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.24.56:910][876]Cmd: LevelEditor.ToggleImmersive
[2025.06.05-07.25.27:945][919]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0A02
[2025.06.05-07.25.27:945][919]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:09E3
[2025.06.05-07.25.27:976][920]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0A02
[2025.06.05-07.25.27:976][920]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:09E3
[2025.06.05-07.25.32:091][ 53]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6627.700195
[2025.06.05-07.25.32:432][ 64]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-07.25.32:432][ 64]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6628.012695, Update Interval: 324.595490
[2025.06.05-07.25.34:629][134]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:053D
[2025.06.05-07.25.34:629][134]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:051E
[2025.06.05-07.25.34:659][135]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:053D
[2025.06.05-07.25.34:659][135]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:051E
[2025.06.05-07.26.17:675][577]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-07.26.17:675][577]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-07.26.17:679][577]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-07.26.17:679][577]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-07.26.17:679][577]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-07.26.17:682][577]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-07.26.17:703][577]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-07.26.17:714][577]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-07.26.17:772][577]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-07.26.17:775][577]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 3
[2025.06.05-07.26.17:775][577]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.05-07.26.17:777][577]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.05-07.26.17:789][577]LogUObjectHash: Compacting FUObjectHashTables data took   2.18ms
[2025.06.05-07.26.17:930][578]LogPlayLevel: Display: Destroying online subsystem :Context_26
[2025.06.05-07.27.15:146][518]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.27.15:273][525]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.27.15:391][532]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.27.15:506][539]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.27.15:506][539]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.27.15:642][546]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.29.54:968][733]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.31.27:300][ 37]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.27:300][ 37]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.27:374][ 40]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.31.27:457][ 44]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.27:530][ 47]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.31.27:613][ 51]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.27:684][ 54]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.31.27:765][ 58]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.27:847][ 61]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.28:004][ 68]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.31.28:153][ 75]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.31.35:965][453]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6991.549316
[2025.06.05-07.31.36:252][467]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-07.31.36:252][467]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6991.815918, Update Interval: 338.625458
[2025.06.05-07.31.39:483][608]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Input/IA_Aim.uasset H:/P4/dev/Baoli/Content/Input/IA_Back.uasset H:/P4/dev/Baoli/Content/Input/IA_Crouch.uasset H:/P4/dev/Baoli/Content/Input/IA_Debug.uasset H:/P4/dev/Baoli/Content/Input/IA_Draw.uasset H:/P4/dev/Baoli/Content/Input/IA_Flashlight.uasset H:/P4/dev/Baoli/Content/Input/IA_Interact.uasset H:/P4/dev/Baoli/Content/Input/IA_Jump.uasset H:/P4/dev/Baoli/Content/Input/IA_Lighter.uasset H:/P4/dev/Baoli/Content/Input/IA_LockPick.uasset H:/P4/dev/Baoli/Content/Input/IA_Look.uasset H:/P4/dev/Baoli/Content/Input/IA_Look_Gamepad.uasset'
[2025.06.05-07.31.40:043][629]LogAssetEditorSubsystem: Opening Asset editor for InputAction /Game/Input/IA_Look.IA_Look
[2025.06.05-07.31.40:066][629]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 12:43:51
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-07.31.40:385][629]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-07.31.43:719][751]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.31.43:805][758]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.31.43:827][759]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.43:906][765]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.31.43:921][766]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.43:959][767]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.44:017][772]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.31.44:065][775]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.44:112][779]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.44:151][782]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.44:197][786]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.31.44:709][814]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.45:212][851]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.31.50:806][381]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.33.31:053][115]LogD3D12RHI: Creating RTPSO with 258 shaders (0 cached, 1 new) took 757.77 ms. Compile time 1.35 ms, link time 756.36 ms.
[2025.06.05-07.33.31:061][115]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_25
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.33.31:061][115]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_25
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.33.31:174][119]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_26
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.33.31:174][119]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_26
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.33.31:364][128]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.33.31:364][128]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.33.31:437][131]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.33.31:510][135]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.33.31:577][138]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.33.31:651][142]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.33.31:721][145]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.33.31:870][152]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.33.32:005][159]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.33.32:144][166]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.34.54:997][540]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.35.09:426][551]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset'
[2025.06.05-07.35.10:230][603]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset'
[2025.06.05-07.35.11:913][664]LogUObjectHash: Compacting FUObjectHashTables data took   2.52ms
[2025.06.05-07.35.12:496][664]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_27
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.35.12:496][664]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_27
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.35.12:915][666]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_28
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.35.12:915][666]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_28
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.35.14:226][724]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-07.35.14:247][724]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-07.35.14:247][724]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-07.35.14:312][724]LogPlayLevel: PIE: StaticDuplicateObject took: (0.064818s)
[2025.06.05-07.35.14:312][724]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.064865s)
[2025.06.05-07.35.14:454][724]LogUObjectHash: Compacting FUObjectHashTables data took   1.77ms
[2025.06.05-07.35.14:459][724]r.RayTracing.Culling = "0"
[2025.06.05-07.35.14:459][724]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-07.35.14:459][724]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-07.35.14:463][724]LogPlayLevel: PIE: World Init took: (0.002863s)
[2025.06.05-07.35.14:463][724]LogAudio: Display: Creating Audio Device:                 Id: 4, Scope: Unique, Realtime: True
[2025.06.05-07.35.14:463][724]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-07.35.14:463][724]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-07.35.14:463][724]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-07.35.14:463][724]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-07.35.14:463][724]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-07.35.14:463][724]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-07.35.14:463][724]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-07.35.14:463][724]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-07.35.14:463][724]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-07.35.14:463][724]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-07.35.14:463][724]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-07.35.14:465][724]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-07.35.14:503][724]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-07.35.14:503][724]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-07.35.14:503][724]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-07.35.14:503][724]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-07.35.14:504][724]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=4
[2025.06.05-07.35.14:504][724]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=4
[2025.06.05-07.35.14:506][724]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=4
[2025.06.05-07.35.14:506][724]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=4
[2025.06.05-07.35.14:506][724]LogInit: FAudioDevice initialized with ID 4.
[2025.06.05-07.35.14:506][724]LogAudio: Display: Audio Device (ID: 4) registered with world 'DefaultLevel'.
[2025.06.05-07.35.14:506][724]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 4
[2025.06.05-07.35.14:506][724]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-07.35.14:510][724]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-07.35.14:541][724]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-07.35.14:549][724]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-13.05.14
[2025.06.05-07.35.14:556][724]LogWorld: Bringing up level for play took: 0.045268
[2025.06.05-07.35.14:559][724]LogOnline: OSS: Created online subsystem instance for: :Context_27
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-07.35.14:576][724]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-07.35.14:589][724]PIE: Server logged in
[2025.06.05-07.35.14:591][724]PIE: Play in editor total start time 0.354 seconds.
[2025.06.05-07.35.14:727][725]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-07.35.30:196][220]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-07.35.30:196][220]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-07.35.30:199][220]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-07.35.30:199][220]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-07.35.30:199][220]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-07.35.30:202][220]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-07.35.30:218][220]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-07.35.30:230][220]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-07.35.30:264][220]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-07.35.30:300][220]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 4
[2025.06.05-07.35.30:300][220]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4
[2025.06.05-07.35.30:302][220]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4
[2025.06.05-07.35.30:307][220]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-07.35.30:316][220]LogUObjectHash: Compacting FUObjectHashTables data took   2.23ms
[2025.06.05-07.35.30:391][221]Cmd: SELECT NONE
[2025.06.05-07.35.30:409][221]LogPlayLevel: Display: Destroying online subsystem :Context_27
[2025.06.05-07.36.13:505][345]LogUObjectHash: Compacting FUObjectHashTables data took   1.86ms
[2025.06.05-07.36.13:513][345]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Levels/DefaultLevel' took 0.043
[2025.06.05-07.36.13:514][345]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/BP_BaoliCharacter] ([2] browsable assets)...
[2025.06.05-07.36.13:519][345]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter]
[2025.06.05-07.36.13:519][345]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_BaoliCharacter]
[2025.06.05-07.36.13:519][345]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_BaoliCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/BP_BaoliCharacter_Auto7.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-07.36.13:563][345]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/BP_BaoliCharacter_Auto7
[2025.06.05-07.36.13:563][345]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/BP_BaoliCharacter_Auto776C8170D49A0E81998EA0D9C8EF9FAEA.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/BP_BaoliCharacter_Auto7.uasset'
[2025.06.05-07.36.13:564][345]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-07.36.13:565][345]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_29
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.36.13:566][345]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_29
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.36.13:572][345]OBJ SavePackage:     Rendered thumbnail for [AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter]
[2025.06.05-07.36.13:572][345]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-07.36.13:572][345]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto7.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-07.36.13:710][345]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto7
[2025.06.05-07.36.13:710][345]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter_Auto7B7C0EE684D2CBECD7B37C9834AF70CC5.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto7.uasset'
[2025.06.05-07.36.13:712][345]LogFileHelpers: Auto-saving content packages took 0.199
[2025.06.05-07.36.14:728][431]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.37.00:340][358]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.05-07.37.59:730][567]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7375.334473
[2025.06.05-07.38.00:730][570]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-07.38.00:730][570]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7376.002441, Update Interval: 331.965698
[2025.06.05-07.41.14:770][287]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.44.18:398][561]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7754.039551
[2025.06.05-07.44.18:682][595]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-07.44.18:682][595]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7754.316895, Update Interval: 343.159271
[2025.06.05-07.46.00:782][711]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-07.46.00:786][711]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-07.46.10:284][634]LogSelectionDetails: Warning: NavigateToFunctionSource:  Unable to find source file and line number for 'UCameraComponent::SetFieldOfView' [The specified module could not be found.]
[2025.06.05-07.46.14:816][119]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.47.25:846][247]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_30
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.47.25:846][247]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_30
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.47.25:923][248]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_31
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.47.25:924][248]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_31
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.47.26:153][265]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_Almirah.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bed.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bulb.uasset'
[2025.06.05-07.47.27:014][346]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/BP_BaoliController.BP_BaoliController
[2025.06.05-07.47.27:015][346]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_24
[2025.06.05-07.47.27:045][346]LogConfig: Branch 'rdBPToolsConfig' had been unloaded. Reloading on-demand took 0.65ms
[2025.06.05-07.47.27:045][346]LogTemp: Display: Handle AssetOpenedInEditor - BlueprintEditor...
[2025.06.05-07.47.27:093][346]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-07.47.27:132][346]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 12:43:51
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-07.47.27:530][346]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 12:43:51
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-07.47.27:622][347]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-07.50.32:747][997]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Input/IA_Aim.uasset H:/P4/dev/Baoli/Content/Input/IA_Back.uasset H:/P4/dev/Baoli/Content/Input/IA_Crouch.uasset H:/P4/dev/Baoli/Content/Input/IA_Debug.uasset H:/P4/dev/Baoli/Content/Input/IA_Draw.uasset H:/P4/dev/Baoli/Content/Input/IA_Flashlight.uasset H:/P4/dev/Baoli/Content/Input/IA_Interact.uasset H:/P4/dev/Baoli/Content/Input/IA_Jump.uasset H:/P4/dev/Baoli/Content/Input/IA_Lighter.uasset H:/P4/dev/Baoli/Content/Input/IA_LockPick.uasset H:/P4/dev/Baoli/Content/Input/IA_Look.uasset H:/P4/dev/Baoli/Content/Input/IA_Look_Gamepad.uasset H:/P4/dev/Baoli/Content/Input/IA_Move.uasset H:/P4/dev/Baoli/Content/Input/IA_Move_WorldSpace.uasset H:/P4/dev/Baoli/Content/Input/IA_OpenInventory.uasset H:/P4/dev/Baoli/Content/Input/IA_Pause.uasset'
[2025.06.05-07.50.33:070][ 17]LogAssetEditorSubsystem: Opening Asset editor for InputAction /Game/Input/IA_Look.IA_Look
[2025.06.05-07.50.33:086][ 17]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 12:43:51
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-07.50.33:389][ 17]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-07.50.37:896][515]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8133.530273
[2025.06.05-07.50.38:160][546]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-07.50.38:160][546]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8133.785645, Update Interval: 327.748657
[2025.06.05-07.51.14:929][979]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.52.08:859][612]LogSlate: Window 'Find in Blueprints' being destroyed
[2025.06.05-07.52.08:869][612]LogSlate: Window 'Find in Blueprints' being destroyed
[2025.06.05-07.52.32:230][136]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-07.52.32:266][136]LogPlayLevel: [PlayLevel] Compiling ABP_Friend before play...
[2025.06.05-07.52.32:267][136]LogPlayLevel: [PlayLevel]   Compiling ABP_SandboxCharacter as a dependent...
[2025.06.05-07.52.32:282][136]LogPlayLevel: [PlayLevel]   Compiling BP_BaoliCharacter as a dependent...
[2025.06.05-07.52.33:350][136]LogUObjectHash: Compacting FUObjectHashTables data took   2.56ms
[2025.06.05-07.52.33:356][136]LogPlayLevel: PlayLevel: Blueprint regeneration took 1109 ms (3 blueprints)
[2025.06.05-07.52.33:356][136]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-07.52.33:463][136]LogPlayLevel: PIE: StaticDuplicateObject took: (0.106628s)
[2025.06.05-07.52.33:463][136]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.106692s)
[2025.06.05-07.52.33:501][136]LogUObjectHash: Compacting FUObjectHashTables data took   2.30ms
[2025.06.05-07.52.33:503][136]r.RayTracing.Culling = "0"
[2025.06.05-07.52.33:504][136]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-07.52.33:504][136]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-07.52.33:505][136]LogPlayLevel: PIE: World Init took: (0.001646s)
[2025.06.05-07.52.33:506][136]LogAudio: Display: Creating Audio Device:                 Id: 5, Scope: Unique, Realtime: True
[2025.06.05-07.52.33:506][136]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-07.52.33:506][136]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-07.52.33:506][136]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-07.52.33:506][136]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-07.52.33:506][136]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-07.52.33:506][136]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-07.52.33:506][136]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-07.52.33:506][136]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-07.52.33:506][136]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-07.52.33:506][136]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-07.52.33:506][136]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-07.52.33:509][136]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-07.52.33:546][136]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-07.52.33:546][136]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-07.52.33:546][136]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-07.52.33:546][136]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-07.52.33:546][136]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=5
[2025.06.05-07.52.33:547][136]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=5
[2025.06.05-07.52.33:550][136]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=5
[2025.06.05-07.52.33:550][136]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=5
[2025.06.05-07.52.33:550][136]LogInit: FAudioDevice initialized with ID 5.
[2025.06.05-07.52.33:550][136]LogAudio: Display: Audio Device (ID: 5) registered with world 'DefaultLevel'.
[2025.06.05-07.52.33:550][136]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 5
[2025.06.05-07.52.33:550][136]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-07.52.33:553][136]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-07.52.33:580][136]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-07.52.33:588][136]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-13.22.33
[2025.06.05-07.52.33:601][136]LogWorld: Bringing up level for play took: 0.047543
[2025.06.05-07.52.33:607][136]LogOnline: OSS: Created online subsystem instance for: :Context_29
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-07.52.33:626][136]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-07.52.33:641][136]PIE: Server logged in
[2025.06.05-07.52.33:642][136]PIE: Play in editor total start time 1.406 seconds.
[2025.06.05-07.52.33:816][137]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-07.52.33:914][137]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_33
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.52.33:915][137]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_33
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.52.36:504][223]PIE: Error: No database assets provided for motion matching.
[2025.06.05-07.52.40:786][366]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-07.52.40:786][366]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-07.52.40:789][366]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-07.52.40:789][366]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-07.52.40:790][366]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-07.52.40:792][366]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-07.52.40:797][366]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-07.52.40:810][366]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-07.52.40:878][366]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-07.52.40:888][366]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 5
[2025.06.05-07.52.40:888][366]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5
[2025.06.05-07.52.40:891][366]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5
[2025.06.05-07.52.40:904][366]LogUObjectHash: Compacting FUObjectHashTables data took   2.28ms
[2025.06.05-07.52.41:024][367]LogPlayLevel: Display: Destroying online subsystem :Context_29
[2025.06.05-07.52.42:217][408]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-07.52.42:254][408]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-07.52.44:206][486]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-07.52.44:259][488]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.52.44:380][495]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.52.44:508][502]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.52.46:366][626]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-07.52.49:956][827]LogUObjectHash: Compacting FUObjectHashTables data took   1.94ms
[2025.06.05-07.52.49:987][827]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_35
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.52.49:988][827]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_35
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.55.38:325][805]LogUObjectHash: Compacting FUObjectHashTables data took   1.85ms
[2025.06.05-07.55.38:334][805]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Levels/DefaultLevel' took 0.046
[2025.06.05-07.55.38:334][805]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/BP_BaoliCharacter] ([2] browsable assets)...
[2025.06.05-07.55.38:342][805]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter]
[2025.06.05-07.55.38:342][805]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_BaoliCharacter]
[2025.06.05-07.55.38:342][805]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_BaoliCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/BP_BaoliCharacter_Auto8.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-07.55.38:381][805]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/BP_BaoliCharacter_Auto8
[2025.06.05-07.55.38:381][805]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/BP_BaoliCharacter_Auto850BED3B842938C89BFD8A598F4822211.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/BP_BaoliCharacter_Auto8.uasset'
[2025.06.05-07.55.38:382][805]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-07.55.38:384][805]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_36
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.55.38:384][805]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_36
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-07.55.38:391][805]OBJ SavePackage:     Rendered thumbnail for [AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter]
[2025.06.05-07.55.38:391][805]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-07.55.38:391][805]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto8.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-07.55.38:531][805]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto8
[2025.06.05-07.55.38:531][805]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter_Auto86F8168DD4131EEC0AAB9869669708786.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto8.uasset'
[2025.06.05-07.55.38:532][805]LogFileHelpers: Auto-saving content packages took 0.197
[2025.06.05-07.55.39:548][873]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-07.57.04:004][704]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8519.589844
[2025.06.05-07.57.04:293][724]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-07.57.04:293][724]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8519.864258, Update Interval: 312.455200
[2025.06.05-08.00.35:721][646]LogEditorClassViewer: Warning: Class /Game/rdBP/BP_RandomAssetBase_rdInst.BP_RandomAssetBase_rdInst_C has parent /Script/rdInst.rdActor, but this parent is not found. The Class will not be shown in ClassViewer.
[2025.06.05-08.00.35:721][646]LogEditorClassViewer: Warning: Class /Game/rdBP/BP_ContainerBase_rdInst.BP_ContainerBase_rdInst_C has parent /Script/rdInst.rdActor, but this parent is not found. The Class will not be shown in ClassViewer.
[2025.06.05-08.00.35:721][646]LogEditorClassViewer: Warning: Class /Game/XV3dGS/gs_Ghar_ke_paas/gs_Ghar_ke_paas1.gs_Ghar_ke_paas1_C has parent /XV3dGS/BPGaussianActor.BPGaussianActor_C, but this parent is not found. The Class will not be shown in ClassViewer.
[2025.06.05-08.00.35:721][646]LogEditorClassViewer: Warning: Class /Game/EnemyAI/BehaviousTree/MyBT_AIController.MyBT_AIController_C has parent /Script/Baoli.BT_AIController, but this parent is not found. The Class will not be shown in ClassViewer.
[2025.06.05-08.00.35:721][646]LogEditorClassViewer: Warning: Class /Game/EnemyAI/BehaviousTree/BP_AIController.BP_AIController_C has parent /Script/Baoli.BT_AIController, but this parent is not found. The Class will not be shown in ClassViewer.
[2025.06.05-08.00.35:721][646]LogEditorClassViewer: Warning: Class /Game/Blueprints/ReflectiveMirror/BP_ReflectionMirrorPawn.BP_ReflectionMirrorPawn_C has parent /Script/Baoli.Mirror, but this parent is not found. The Class will not be shown in ClassViewer.
[2025.06.05-08.00.35:722][646]LogEditorClassViewer: Warning: Class /Script/EditorScriptableToolsFramework.EditorScriptableSingleClickTool has parent /Script/ScriptableToolsFramework.ScriptableSingleClickTool, but this parent is not found. The Class will not be shown in ClassViewer.
[2025.06.05-08.00.39:578][974]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-08.00.42:310][207]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset'
[2025.06.05-08.00.43:301][290]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset'
[2025.06.05-08.00.45:656][459]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Input/IA_phuphu.uasset H:/P4/dev/Baoli/Content/Input/IA_PowerFlash.uasset H:/P4/dev/Baoli/Content/Input/IA_Sprint.uasset H:/P4/dev/Baoli/Content/Input/IA_Strafe.uasset H:/P4/dev/Baoli/Content/Input/IA_Take.uasset H:/P4/dev/Baoli/Content/Input/IA_Tea.uasset H:/P4/dev/Baoli/Content/Input/IA_View.uasset H:/P4/dev/Baoli/Content/Input/IA_Walk.uasset'
[2025.06.05-08.00.46:085][491]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Input/IA_Walk1.uasset H:/P4/dev/Baoli/Content/Input/IMC_GDCMotionMatching.uasset'
[2025.06.05-08.00.47:295][579]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Input/IA_Lighter.uasset H:/P4/dev/Baoli/Content/Input/IA_LockPick.uasset H:/P4/dev/Baoli/Content/Input/IA_Look.uasset H:/P4/dev/Baoli/Content/Input/IA_Look_Gamepad.uasset H:/P4/dev/Baoli/Content/Input/IA_Move.uasset H:/P4/dev/Baoli/Content/Input/IA_Move_WorldSpace.uasset H:/P4/dev/Baoli/Content/Input/IA_OpenInventory.uasset H:/P4/dev/Baoli/Content/Input/IA_Pause.uasset'
[2025.06.05-08.00.47:827][620]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Input/IA_Aim.uasset H:/P4/dev/Baoli/Content/Input/IA_Back.uasset H:/P4/dev/Baoli/Content/Input/IA_Crouch.uasset H:/P4/dev/Baoli/Content/Input/IA_Debug.uasset H:/P4/dev/Baoli/Content/Input/IA_Draw.uasset H:/P4/dev/Baoli/Content/Input/IA_Flashlight.uasset H:/P4/dev/Baoli/Content/Input/IA_Interact.uasset H:/P4/dev/Baoli/Content/Input/IA_Jump.uasset'
[2025.06.05-08.01.05:954][466]LogSlate: Window 'Create New Blueprint' being destroyed
[2025.06.05-08.01.05:989][466]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/BaoliPlayerCameraManager.BaoliPlayerCameraManager
[2025.06.05-08.01.05:989][466]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_25
[2025.06.05-08.01.06:004][466]LogTemp: Display: Handle AssetOpenedInEditor - BlueprintEditor...
[2025.06.05-08.01.06:010][466]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-08.01.06:078][466]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 12:43:51
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-08.01.06:403][466]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-08.01.06:414][466]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset'
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 12:43:51
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-08.01.06:522][467]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-08.01.06:633][473]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.01.06:996][499]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset'
[2025.06.05-08.01.37:254][887]LogUObjectHash: Compacting FUObjectHashTables data took   1.82ms
[2025.06.05-08.01.40:763][142]LogUObjectHash: Compacting FUObjectHashTables data took   2.05ms
[2025.06.05-08.01.40:939][142]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_37
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-08.01.40:940][142]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_37
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-08.01.42:443][225]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-08.01.42:459][225]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-08.01.42:460][225]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.01.42:522][225]LogPlayLevel: PIE: StaticDuplicateObject took: (0.061750s)
[2025.06.05-08.01.42:522][225]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.061800s)
[2025.06.05-08.01.42:561][225]LogUObjectHash: Compacting FUObjectHashTables data took   1.95ms
[2025.06.05-08.01.42:580][225]r.RayTracing.Culling = "0"
[2025.06.05-08.01.42:580][225]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-08.01.42:580][225]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-08.01.42:583][225]LogPlayLevel: PIE: World Init took: (0.002584s)
[2025.06.05-08.01.42:584][225]LogAudio: Display: Creating Audio Device:                 Id: 6, Scope: Unique, Realtime: True
[2025.06.05-08.01.42:584][225]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-08.01.42:584][225]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-08.01.42:584][225]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-08.01.42:584][225]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-08.01.42:584][225]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-08.01.42:584][225]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-08.01.42:584][225]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-08.01.42:584][225]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-08.01.42:584][225]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-08.01.42:584][225]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-08.01.42:584][225]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-08.01.42:586][225]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-08.01.42:622][225]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-08.01.42:622][225]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-08.01.42:622][225]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-08.01.42:622][225]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-08.01.42:623][225]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=6
[2025.06.05-08.01.42:623][225]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=6
[2025.06.05-08.01.42:625][225]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=6
[2025.06.05-08.01.42:625][225]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=6
[2025.06.05-08.01.42:625][225]LogInit: FAudioDevice initialized with ID 6.
[2025.06.05-08.01.42:625][225]LogAudio: Display: Audio Device (ID: 6) registered with world 'DefaultLevel'.
[2025.06.05-08.01.42:625][225]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 6
[2025.06.05-08.01.42:625][225]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-08.01.42:628][225]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-08.01.42:657][225]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-08.01.42:666][225]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-13.31.42
[2025.06.05-08.01.42:672][225]LogWorld: Bringing up level for play took: 0.043738
[2025.06.05-08.01.42:675][225]LogOnline: OSS: Created online subsystem instance for: :Context_31
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-08.01.42:692][225]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-08.01.42:705][225]PIE: Server logged in
[2025.06.05-08.01.42:706][225]PIE: Play in editor total start time 0.256 seconds.
[2025.06.05-08.01.42:843][226]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-08.01.43:320][238]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.01.43:320][238]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.01.43:638][248]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-08.01.52:956][571]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.01.52:956][571]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.01.52:961][571]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-08.01.52:961][571]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-08.01.52:961][571]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.01.52:964][571]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.01.52:971][571]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-08.01.52:983][571]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.01.53:016][571]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-08.01.53:042][571]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 6
[2025.06.05-08.01.53:042][571]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=6
[2025.06.05-08.01.53:045][571]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=6
[2025.06.05-08.01.53:049][571]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.01.53:057][571]LogUObjectHash: Compacting FUObjectHashTables data took   2.29ms
[2025.06.05-08.01.53:101][572]LogPlayLevel: Display: Destroying online subsystem :Context_31
[2025.06.05-08.01.57:476][858]LogUObjectHash: Compacting FUObjectHashTables data took   1.83ms
[2025.06.05-08.01.58:712][914]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-08.01.58:729][914]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-08.01.58:729][914]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.01.58:790][914]LogPlayLevel: PIE: StaticDuplicateObject took: (0.060270s)
[2025.06.05-08.01.58:790][914]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.060317s)
[2025.06.05-08.01.58:822][914]LogUObjectHash: Compacting FUObjectHashTables data took   1.76ms
[2025.06.05-08.01.58:828][914]r.RayTracing.Culling = "0"
[2025.06.05-08.01.58:828][914]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-08.01.58:828][914]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-08.01.58:829][914]LogPlayLevel: PIE: World Init took: (0.001584s)
[2025.06.05-08.01.58:830][914]LogAudio: Display: Creating Audio Device:                 Id: 7, Scope: Unique, Realtime: True
[2025.06.05-08.01.58:830][914]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-08.01.58:830][914]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-08.01.58:830][914]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-08.01.58:830][914]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-08.01.58:830][914]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-08.01.58:830][914]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-08.01.58:830][914]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-08.01.58:830][914]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-08.01.58:830][914]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-08.01.58:830][914]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-08.01.58:830][914]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-08.01.58:834][914]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-08.01.58:869][914]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-08.01.58:870][914]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-08.01.58:870][914]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-08.01.58:870][914]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-08.01.58:871][914]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=7
[2025.06.05-08.01.58:871][914]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=7
[2025.06.05-08.01.58:874][914]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=7
[2025.06.05-08.01.58:874][914]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=7
[2025.06.05-08.01.58:874][914]LogInit: FAudioDevice initialized with ID 7.
[2025.06.05-08.01.58:874][914]LogAudio: Display: Audio Device (ID: 7) registered with world 'DefaultLevel'.
[2025.06.05-08.01.58:874][914]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 7
[2025.06.05-08.01.58:874][914]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-08.01.58:877][914]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-08.01.58:904][914]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-08.01.58:911][914]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-13.31.58
[2025.06.05-08.01.58:918][914]LogWorld: Bringing up level for play took: 0.041415
[2025.06.05-08.01.58:921][914]LogOnline: OSS: Created online subsystem instance for: :Context_32
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-08.01.58:937][914]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-08.01.58:951][914]PIE: Server logged in
[2025.06.05-08.01.58:952][914]PIE: Play in editor total start time 0.232 seconds.
[2025.06.05-08.01.59:088][915]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-08.02.00:840][967]PIE: Error: No database assets provided for motion matching.
[2025.06.05-08.02.02:756][ 31]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.02.02:756][ 31]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.02.02:760][ 31]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-08.02.02:760][ 31]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-08.02.02:760][ 31]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.02.02:763][ 31]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.02.02:775][ 31]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-08.02.02:788][ 31]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.02.02:845][ 31]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.02.02:846][ 31]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 7
[2025.06.05-08.02.02:846][ 31]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=7
[2025.06.05-08.02.02:850][ 31]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=7
[2025.06.05-08.02.02:863][ 31]LogUObjectHash: Compacting FUObjectHashTables data took   2.29ms
[2025.06.05-08.02.02:979][ 32]LogPlayLevel: Display: Destroying online subsystem :Context_32
[2025.06.05-08.02.04:807][129]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.02.04:817][129]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.02.34:355][466]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-08.02.34:371][466]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-08.02.34:372][466]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.02.34:432][466]LogPlayLevel: PIE: StaticDuplicateObject took: (0.059511s)
[2025.06.05-08.02.34:432][466]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.059600s)
[2025.06.05-08.02.34:467][466]LogUObjectHash: Compacting FUObjectHashTables data took   2.51ms
[2025.06.05-08.02.34:470][466]r.RayTracing.Culling = "0"
[2025.06.05-08.02.34:470][466]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-08.02.34:470][466]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-08.02.34:472][466]LogPlayLevel: PIE: World Init took: (0.001791s)
[2025.06.05-08.02.34:473][466]LogAudio: Display: Creating Audio Device:                 Id: 8, Scope: Unique, Realtime: True
[2025.06.05-08.02.34:473][466]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-08.02.34:473][466]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-08.02.34:473][466]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-08.02.34:473][466]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-08.02.34:473][466]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-08.02.34:473][466]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-08.02.34:473][466]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-08.02.34:473][466]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-08.02.34:473][466]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-08.02.34:473][466]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-08.02.34:473][466]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-08.02.34:475][466]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-08.02.34:511][466]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-08.02.34:511][466]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-08.02.34:511][466]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-08.02.34:511][466]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-08.02.34:512][466]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=8
[2025.06.05-08.02.34:512][466]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=8
[2025.06.05-08.02.34:515][466]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=8
[2025.06.05-08.02.34:516][466]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=8
[2025.06.05-08.02.34:516][466]LogInit: FAudioDevice initialized with ID 8.
[2025.06.05-08.02.34:516][466]LogAudio: Display: Audio Device (ID: 8) registered with world 'DefaultLevel'.
[2025.06.05-08.02.34:516][466]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 8
[2025.06.05-08.02.34:516][466]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-08.02.34:519][466]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-08.02.34:547][466]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-08.02.34:555][466]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-13.32.34
[2025.06.05-08.02.34:562][466]LogWorld: Bringing up level for play took: 0.042507
[2025.06.05-08.02.34:564][466]LogOnline: OSS: Created online subsystem instance for: :Context_33
[2025.06.05-08.02.34:579][466]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-08.02.34:580][466]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-08.02.34:593][466]PIE: Server logged in
[2025.06.05-08.02.34:594][466]PIE: Play in editor total start time 0.231 seconds.
[2025.06.05-08.02.34:728][467]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-08.02.36:111][509]PIE: Error: No database assets provided for motion matching.
[2025.06.05-08.02.48:264][923]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:053D
[2025.06.05-08.02.48:264][923]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:051E
[2025.06.05-08.02.48:297][924]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:053D
[2025.06.05-08.02.48:298][924]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:051E
[2025.06.05-08.02.49:906][974]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.02.49:906][974]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.02.49:910][974]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-08.02.49:910][974]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-08.02.49:910][974]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.02.49:913][974]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.02.49:918][974]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-08.02.49:929][974]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.02.49:987][974]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.02.49:989][974]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 8
[2025.06.05-08.02.49:989][974]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=8
[2025.06.05-08.02.49:991][974]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=8
[2025.06.05-08.02.50:004][974]LogUObjectHash: Compacting FUObjectHashTables data took   2.22ms
[2025.06.05-08.02.50:120][975]LogPlayLevel: Display: Destroying online subsystem :Context_33
[2025.06.05-08.02.51:489][ 19]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.02.51:533][ 19]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.02.58:474][531]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8874.271484
[2025.06.05-08.02.58:757][551]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-08.02.58:757][551]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8874.541992, Update Interval: 300.525543
[2025.06.05-08.03.01:438][712]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-08.03.01:456][712]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-08.03.01:456][712]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.03.01:517][712]LogPlayLevel: PIE: StaticDuplicateObject took: (0.060818s)
[2025.06.05-08.03.01:517][712]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.060869s)
[2025.06.05-08.03.01:552][712]LogUObjectHash: Compacting FUObjectHashTables data took   1.89ms
[2025.06.05-08.03.01:555][712]r.RayTracing.Culling = "0"
[2025.06.05-08.03.01:555][712]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-08.03.01:555][712]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-08.03.01:557][712]LogPlayLevel: PIE: World Init took: (0.001612s)
[2025.06.05-08.03.01:558][712]LogAudio: Display: Creating Audio Device:                 Id: 9, Scope: Unique, Realtime: True
[2025.06.05-08.03.01:558][712]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-08.03.01:558][712]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-08.03.01:558][712]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-08.03.01:558][712]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-08.03.01:558][712]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-08.03.01:558][712]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-08.03.01:558][712]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-08.03.01:558][712]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-08.03.01:558][712]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-08.03.01:558][712]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-08.03.01:558][712]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-08.03.01:561][712]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-08.03.01:596][712]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-08.03.01:596][712]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-08.03.01:596][712]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-08.03.01:596][712]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-08.03.01:598][712]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=9
[2025.06.05-08.03.01:598][712]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=9
[2025.06.05-08.03.01:601][712]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=9
[2025.06.05-08.03.01:601][712]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=9
[2025.06.05-08.03.01:601][712]LogInit: FAudioDevice initialized with ID 9.
[2025.06.05-08.03.01:601][712]LogAudio: Display: Audio Device (ID: 9) registered with world 'DefaultLevel'.
[2025.06.05-08.03.01:601][712]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 9
[2025.06.05-08.03.01:601][712]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-08.03.01:604][712]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-08.03.01:632][712]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-08.03.01:641][712]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-13.33.01
[2025.06.05-08.03.01:647][712]LogWorld: Bringing up level for play took: 0.043102
[2025.06.05-08.03.01:650][712]LogOnline: OSS: Created online subsystem instance for: :Context_34
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-08.03.01:667][712]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-08.03.01:680][712]PIE: Server logged in
[2025.06.05-08.03.01:681][712]PIE: Play in editor total start time 0.236 seconds.
[2025.06.05-08.03.01:817][713]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-08.03.03:146][756]PIE: Error: No database assets provided for motion matching.
[2025.06.05-08.03.17:559][232]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0A02
[2025.06.05-08.03.17:559][232]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:09E3
[2025.06.05-08.03.17:589][233]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0A02
[2025.06.05-08.03.17:589][233]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:09E3
[2025.06.05-08.03.22:128][385]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.03.22:128][385]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.03.22:140][385]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-08.03.22:140][385]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-08.03.22:143][385]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.03.22:150][385]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.03.22:189][385]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-08.03.22:202][385]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.03.22:259][385]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.03.22:262][385]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 9
[2025.06.05-08.03.22:262][385]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=9
[2025.06.05-08.03.22:264][385]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=9
[2025.06.05-08.03.22:276][385]LogUObjectHash: Compacting FUObjectHashTables data took   2.18ms
[2025.06.05-08.03.22:416][386]LogPlayLevel: Display: Destroying online subsystem :Context_34
[2025.06.05-08.03.24:499][455]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.03.24:541][455]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.03.25:347][484]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-08.03.25:366][484]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-08.03.25:366][484]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.03.25:425][484]LogPlayLevel: PIE: StaticDuplicateObject took: (0.059333s)
[2025.06.05-08.03.25:425][484]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.059387s)
[2025.06.05-08.03.25:458][484]LogUObjectHash: Compacting FUObjectHashTables data took   1.85ms
[2025.06.05-08.03.25:462][484]r.RayTracing.Culling = "0"
[2025.06.05-08.03.25:462][484]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-08.03.25:462][484]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-08.03.25:463][484]LogPlayLevel: PIE: World Init took: (0.001906s)
[2025.06.05-08.03.25:465][484]LogAudio: Display: Creating Audio Device:                 Id: 10, Scope: Unique, Realtime: True
[2025.06.05-08.03.25:465][484]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-08.03.25:465][484]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-08.03.25:465][484]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-08.03.25:465][484]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-08.03.25:465][484]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-08.03.25:465][484]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-08.03.25:465][484]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-08.03.25:465][484]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-08.03.25:465][484]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-08.03.25:465][484]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-08.03.25:465][484]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-08.03.25:468][484]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-08.03.25:505][484]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-08.03.25:505][484]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-08.03.25:505][484]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-08.03.25:505][484]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-08.03.25:506][484]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=10
[2025.06.05-08.03.25:506][484]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=10
[2025.06.05-08.03.25:509][484]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=10
[2025.06.05-08.03.25:509][484]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=10
[2025.06.05-08.03.25:509][484]LogInit: FAudioDevice initialized with ID 10.
[2025.06.05-08.03.25:509][484]LogAudio: Display: Audio Device (ID: 10) registered with world 'DefaultLevel'.
[2025.06.05-08.03.25:509][484]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 10
[2025.06.05-08.03.25:509][484]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-08.03.25:512][484]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-08.03.25:540][484]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-08.03.25:547][484]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-13.33.25
[2025.06.05-08.03.25:554][484]LogWorld: Bringing up level for play took: 0.041847
[2025.06.05-08.03.25:556][484]LogOnline: OSS: Created online subsystem instance for: :Context_35
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-08.03.25:572][484]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-08.03.25:585][484]PIE: Server logged in
[2025.06.05-08.03.25:585][484]PIE: Play in editor total start time 0.232 seconds.
[2025.06.05-08.03.25:718][485]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-08.03.27:079][529]PIE: Error: No database assets provided for motion matching.
[2025.06.05-08.03.37:316][871]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.03.37:317][871]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.03.37:320][871]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-08.03.37:320][871]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-08.03.37:321][871]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.03.37:323][871]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.03.37:345][871]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-08.03.37:357][871]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.03.37:413][871]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.03.37:417][871]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 10
[2025.06.05-08.03.37:417][871]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=10
[2025.06.05-08.03.37:419][871]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=10
[2025.06.05-08.03.37:430][871]LogUObjectHash: Compacting FUObjectHashTables data took   1.97ms
[2025.06.05-08.03.37:544][872]LogPlayLevel: Display: Destroying online subsystem :Context_35
[2025.06.05-08.03.40:042][985]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.03.40:052][985]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.03.49:575][723]LogUObjectHash: Compacting FUObjectHashTables data took   1.92ms
[2025.06.05-08.05.39:604][242]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-08.05.43:336][519]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset'
[2025.06.05-08.06.07:016][367]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset'
[2025.06.05-08.08.39:958][418]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 9215.864258
[2025.06.05-08.08.40:233][439]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-08.08.40:234][439]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9216.124023, Update Interval: 358.242126
[2025.06.05-08.09.19:948][446]LogSlate: Took 0.016594 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/NotoSansMath-Regular.ttf' (574K)
[2025.06.05-08.10.39:634][571]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-08.10.43:361][846]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset'
[2025.06.05-08.10.59:502][ 56]LogBlueprint: Error: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\BP_BaoliCharacter.uasset: [Compiler] This blueprint (self) is not a PlayerCameraManager, therefore ' Target ' must have a connection.
[2025.06.05-08.10.59:502][ 56]LogBlueprint: Error: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\BP_BaoliCharacter.uasset: [Compiler] This blueprint (self) is not a PlayerCameraManager, therefore ' Target ' must have a connection.
[2025.06.05-08.10.59:515][ 56]LogBlueprint: Error: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\BP_BaoliCharacter.uasset: [Compiler] Variable node  Set ViewYawMin  uses an invalid target.  It may depend on a node that is not connected to the execution chain, and got purged.
[2025.06.05-08.10.59:515][ 56]LogBlueprint: Error: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\BP_BaoliCharacter.uasset: [Compiler] Variable node  Set ViewYawMax  uses an invalid target.  It may depend on a node that is not connected to the execution chain, and got purged.
[2025.06.05-08.10.59:722][ 56]LogUObjectHash: Compacting FUObjectHashTables data took   2.11ms
[2025.06.05-08.10.59:944][ 56]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_38
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-08.10.59:945][ 56]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_38
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-08.11.07:055][577]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset'
[2025.06.05-08.11.10:545][785]LogUObjectHash: Compacting FUObjectHashTables data took   2.12ms
[2025.06.05-08.11.10:835][785]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_39
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-08.11.10:836][785]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_39
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-08.11.15:023][ 41]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-08.11.15:042][ 41]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-08.11.15:042][ 41]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.11.15:104][ 41]LogPlayLevel: PIE: StaticDuplicateObject took: (0.061325s)
[2025.06.05-08.11.15:104][ 41]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.061368s)
[2025.06.05-08.11.15:244][ 41]LogUObjectHash: Compacting FUObjectHashTables data took   1.99ms
[2025.06.05-08.11.15:259][ 41]r.RayTracing.Culling = "0"
[2025.06.05-08.11.15:259][ 41]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-08.11.15:259][ 41]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-08.11.15:261][ 41]LogPlayLevel: PIE: World Init took: (0.001763s)
[2025.06.05-08.11.15:261][ 41]LogAudio: Display: Creating Audio Device:                 Id: 11, Scope: Unique, Realtime: True
[2025.06.05-08.11.15:261][ 41]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-08.11.15:261][ 41]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-08.11.15:261][ 41]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-08.11.15:261][ 41]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-08.11.15:261][ 41]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-08.11.15:261][ 41]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-08.11.15:261][ 41]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-08.11.15:261][ 41]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-08.11.15:261][ 41]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-08.11.15:261][ 41]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-08.11.15:261][ 41]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-08.11.15:264][ 41]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-08.11.15:301][ 41]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-08.11.15:302][ 41]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-08.11.15:302][ 41]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-08.11.15:302][ 41]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-08.11.15:302][ 41]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=11
[2025.06.05-08.11.15:302][ 41]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=11
[2025.06.05-08.11.15:306][ 41]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=11
[2025.06.05-08.11.15:306][ 41]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=11
[2025.06.05-08.11.15:306][ 41]LogInit: FAudioDevice initialized with ID 11.
[2025.06.05-08.11.15:306][ 41]LogAudio: Display: Audio Device (ID: 11) registered with world 'DefaultLevel'.
[2025.06.05-08.11.15:306][ 41]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 11
[2025.06.05-08.11.15:306][ 41]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-08.11.15:309][ 41]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-08.11.15:341][ 41]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-08.11.15:349][ 41]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-13.41.15
[2025.06.05-08.11.15:355][ 41]LogWorld: Bringing up level for play took: 0.046265
[2025.06.05-08.11.15:358][ 41]LogOnline: OSS: Created online subsystem instance for: :Context_36
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-08.11.15:375][ 41]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-08.11.15:388][ 41]PIE: Server logged in
[2025.06.05-08.11.15:389][ 41]PIE: Play in editor total start time 0.36 seconds.
[2025.06.05-08.11.15:562][ 42]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-08.11.17:174][ 86]PIE: Error: No database assets provided for motion matching.
[2025.06.05-08.11.31:035][524]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.11.31:035][524]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.11.31:041][524]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-08.11.31:041][524]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-08.11.31:042][524]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.11.31:044][524]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.11.31:067][524]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-08.11.31:079][524]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.11.31:127][524]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-08.11.31:152][524]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 11
[2025.06.05-08.11.31:152][524]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=11
[2025.06.05-08.11.31:154][524]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=11
[2025.06.05-08.11.31:159][524]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.11.31:168][524]LogUObjectHash: Compacting FUObjectHashTables data took   3.25ms
[2025.06.05-08.11.31:294][525]LogPlayLevel: Display: Destroying online subsystem :Context_36
[2025.06.05-08.11.33:060][580]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.11.33:105][580]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.11.35:723][706]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-08.11.35:775][708]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.11.35:893][715]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.11.36:006][722]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.11.36:127][729]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.11.36:507][755]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-08.11.36:507][755]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-08.11.36:507][755]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-08.11.36:507][755]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-08.11.36:553][757]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.11.36:633][762]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.11.49:495][763]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.11.58:442][403]LogUObjectHash: Compacting FUObjectHashTables data took   2.21ms
[2025.06.05-08.11.58:722][403]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_40
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-08.11.58:722][403]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_40
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-08.12.00:181][461]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-08.12.00:203][461]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-08.12.00:203][461]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.12.00:264][461]LogPlayLevel: PIE: StaticDuplicateObject took: (0.060136s)
[2025.06.05-08.12.00:264][461]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.060180s)
[2025.06.05-08.12.00:407][461]LogUObjectHash: Compacting FUObjectHashTables data took   2.01ms
[2025.06.05-08.12.00:423][461]r.RayTracing.Culling = "0"
[2025.06.05-08.12.00:423][461]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-08.12.00:423][461]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-08.12.00:424][461]LogPlayLevel: PIE: World Init took: (0.001728s)
[2025.06.05-08.12.00:425][461]LogAudio: Display: Creating Audio Device:                 Id: 12, Scope: Unique, Realtime: True
[2025.06.05-08.12.00:425][461]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-08.12.00:425][461]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-08.12.00:425][461]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-08.12.00:425][461]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-08.12.00:425][461]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-08.12.00:425][461]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-08.12.00:425][461]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-08.12.00:425][461]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-08.12.00:425][461]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-08.12.00:425][461]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-08.12.00:425][461]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-08.12.00:427][461]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-08.12.00:465][461]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-08.12.00:465][461]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-08.12.00:466][461]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-08.12.00:466][461]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-08.12.00:467][461]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=12
[2025.06.05-08.12.00:467][461]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=12
[2025.06.05-08.12.00:471][461]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=12
[2025.06.05-08.12.00:471][461]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=12
[2025.06.05-08.12.00:471][461]LogInit: FAudioDevice initialized with ID 12.
[2025.06.05-08.12.00:471][461]LogAudio: Display: Audio Device (ID: 12) registered with world 'DefaultLevel'.
[2025.06.05-08.12.00:471][461]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 12
[2025.06.05-08.12.00:471][461]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-08.12.00:474][461]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-08.12.00:504][461]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-08.12.00:512][461]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-13.42.00
[2025.06.05-08.12.00:519][461]LogWorld: Bringing up level for play took: 0.044426
[2025.06.05-08.12.00:521][461]LogOnline: OSS: Created online subsystem instance for: :Context_37
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-08.12.00:538][461]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-08.12.00:551][461]PIE: Server logged in
[2025.06.05-08.12.00:552][461]PIE: Play in editor total start time 0.364 seconds.
[2025.06.05-08.12.00:719][462]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-08.12.01:114][470]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.12.01:114][470]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.12.01:231][473]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-08.12.01:345][477]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.12.01:443][480]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-08.12.01:652][487]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-08.12.01:879][494]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-08.12.02:078][501]PIE: Error: No database assets provided for motion matching.
[2025.06.05-08.12.02:091][501]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-08.12.02:530][515]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-08.12.40:848][732]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:08E8
[2025.06.05-08.12.40:849][732]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:08C9
[2025.06.05-08.12.40:879][733]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:08E8
[2025.06.05-08.12.40:879][733]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:08C9
[2025.06.05-08.13.04:405][501]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.13.04:405][501]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-08.13.04:412][501]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-08.13.04:413][501]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-08.13.04:414][501]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.04:418][501]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.04:423][501]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-08.13.04:436][501]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.04:472][501]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-08.13.04:507][501]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 12
[2025.06.05-08.13.04:507][501]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=12
[2025.06.05-08.13.04:509][501]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=12
[2025.06.05-08.13.04:514][501]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-08.13.04:522][501]LogUObjectHash: Compacting FUObjectHashTables data took   2.29ms
[2025.06.05-08.13.04:640][502]LogPlayLevel: Display: Destroying online subsystem :Context_37
[2025.06.05-08.13.05:776][541]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.13.05:812][541]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-08.13.07:092][584]LogUObjectHash: Compacting FUObjectHashTables data took   2.01ms
[2025.06.05-08.13.11:142][584]LogSlate: Window 'Save Content' being destroyed
[2025.06.05-08.13.11:168][584]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset'
[2025.06.05-08.13.13:012][584]LogSlate: Window 'Check Out Assets' being destroyed
[2025.06.05-08.13.13:033][584]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset'
[2025.06.05-08.13.13:105][584]LogSourceControl: Attempting 'p4 edit H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset'
[2025.06.05-08.13.13:137][584]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.05-08.13.13:174][584]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-08.13.13:176][584]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_41
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-08.13.13:176][584]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_41
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.05-08.13.13:272][584]OBJ SavePackage:     Rendered thumbnail for [AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter]
[2025.06.05-08.13.13:272][584]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-08.13.13:272][584]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset" SILENT=true
[2025.06.05-08.13.13:410][584]LogSavePackage: Moving output files for package: /Game/Blueprints/ABP_SandboxCharacter
[2025.06.05-08.13.13:411][584]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter8FB8CD7A4C8E2DFCDA033092512D851A.tmp' to 'H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-08.13.13:412][584]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/BaoliPlayerCameraManager] ([2] browsable assets)...
[2025.06.05-08.13.13:412][584]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BaoliPlayerCameraManager]
[2025.06.05-08.13.13:413][584]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BaoliPlayerCameraManager" FILE="H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset" SILENT=true
[2025.06.05-08.13.13:415][584]LogSavePackage: Moving output files for package: /Game/Blueprints/BaoliPlayerCameraManager
[2025.06.05-08.13.13:415][584]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/BaoliPlayerCameraManager05084FC14D3DE801F882C9AE1E48AF92.tmp' to 'H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset'
[2025.06.05-08.13.13:419][584]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/BP_BaoliCharacter] ([2] browsable assets)...
[2025.06.05-08.13.13:424][584]OBJ SavePackage:     Rendered thumbnail for [Blueprint /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter]
[2025.06.05-08.13.13:424][584]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_BaoliCharacter]
[2025.06.05-08.13.13:424][584]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_BaoliCharacter" FILE="H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset" SILENT=true
[2025.06.05-08.13.13:466][584]LogSavePackage: Moving output files for package: /Game/Blueprints/BP_BaoliCharacter
[2025.06.05-08.13.13:466][584]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/BP_BaoliCharacter7C25F86847FD616BF2FCCAB309403F9C.tmp' to 'H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset'
[2025.06.05-08.13.13:467][584]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Blueprints/BP_BaoliController] ([2] browsable assets)...
[2025.06.05-08.13.13:467][584]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/BP_BaoliController]
[2025.06.05-08.13.13:467][584]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/BP_BaoliController" FILE="H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset" SILENT=true
[2025.06.05-08.13.13:525][584]LogSavePackage: Moving output files for package: /Game/Blueprints/BP_BaoliController
[2025.06.05-08.13.13:526][584]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/BP_BaoliControllerFE3D4B9B406483922DB3C4BA60F80DC4.tmp' to 'H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset'
[2025.06.05-08.13.13:566][584]LogSourceControl: Attempting 'p4 add H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset'
[2025.06.05-08.13.13:592][584]LogFileHelpers: InternalPromptForCheckoutAndSave took 454.967 ms (total: 1.34 sec)
[2025.06.05-08.13.13:630][584]LogContentValidation: Display: Starting to validate 4 assets
[2025.06.05-08.13.13:630][584]LogContentValidation: Enabled validators:
[2025.06.05-08.13.13:630][584]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.05-08.13.13:630][584]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.05-08.13.13:630][584]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.05-08.13.13:630][584]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.05-08.13.13:630][584]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.05-08.13.13:630][584]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.05-08.13.13:630][584]LogContentValidation: 	/Script/AssetReferenceRestrictions.EditorValidator_PluginAssetReferences
[2025.06.05-08.13.13:630][584]LogContentValidation: 	/Script/AssetReferenceRestrictions.AssetValidator_AssetReferenceRestrictions
[2025.06.05-08.13.13:630][584]LogContentValidation: 	/Script/GameFeaturesEditor.IllegalPluginDependenciesValidator
[2025.06.05-08.13.13:631][584]AssetCheck: /Game/Blueprints/ABP_SandboxCharacter Validating asset
[2025.06.05-08.13.13:631][584]AssetCheck: /Game/Blueprints/BaoliPlayerCameraManager Validating asset
[2025.06.05-08.13.13:631][584]AssetCheck: /Game/Blueprints/BP_BaoliCharacter Validating asset
[2025.06.05-08.13.13:631][584]AssetCheck: /Game/Blueprints/BP_BaoliController Validating asset
[2025.06.05-08.13.16:577][680]LogUObjectHash: Compacting FUObjectHashTables data took   2.08ms
[2025.06.05-08.13.16:614][680]LogStall: Shutdown...
[2025.06.05-08.13.16:614][680]LogStall: Shutdown complete.
[2025.06.05-08.13.16:643][680]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.05-08.13.16:655][680]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.16:655][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.16:676][680]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.16:676][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.16:719][680]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.05-08.13.16:731][680]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.16:732][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.16:743][680]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.16:743][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.16:784][680]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.16:784][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.16:854][680]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.05-08.13.16:879][680]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.16:879][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.16:888][680]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.16:888][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.16:962][680]LogSlate: Window 'CHT_PoseSearchDatabases_Dense' being destroyed
[2025.06.05-08.13.16:993][680]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.05-08.13.17:005][680]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:005][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:033][680]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:033][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:045][680]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:045][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:079][680]LogActorComponent: UnregisterComponent: (/Engine/Transient.EditorFloorComp) Not registered. Aborting.
[2025.06.05-08.13.17:079][680]LogWorld: UWorld::CleanupWorld for World_24, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:079][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:112][680]LogActorComponent: UnregisterComponent: (/Engine/Transient.EditorFloorComp) Not registered. Aborting.
[2025.06.05-08.13.17:112][680]LogWorld: UWorld::CleanupWorld for World_25, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:112][680]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:159][680]LogSlate: Window 'Baoli - Unreal Editor' being destroyed
[2025.06.05-08.13.17:307][680]LogUObjectHash: Compacting FUObjectHashTables data took   1.85ms
[2025.06.05-08.13.17:364][680]Cmd: QUIT_EDITOR
[2025.06.05-08.13.17:364][681]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.06.05-08.13.17:378][681]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.06.05-08.13.17:378][681]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.06.05-08.13.17:378][681]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.06.05-08.13.17:400][681]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:400][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:449][681]LogStylusInput: Shutting down StylusInput subsystem.
[2025.06.05-08.13.17:495][681]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.06.05-08.13.17:543][681]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:543][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:549][681]LogWorld: UWorld::CleanupWorld for World_18, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:549][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:552][681]LogWorld: UWorld::CleanupWorld for World_17, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:553][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:557][681]LogWorld: UWorld::CleanupWorld for World_16, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:557][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:561][681]LogWorld: UWorld::CleanupWorld for World_20, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:561][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:566][681]LogWorld: UWorld::CleanupWorld for World_19, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:566][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:570][681]LogWorld: UWorld::CleanupWorld for World_21, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:570][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:574][681]LogWorld: UWorld::CleanupWorld for World_23, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:574][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:578][681]LogWorld: UWorld::CleanupWorld for World_22, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:578][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:582][681]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:582][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:587][681]LogWorld: UWorld::CleanupWorld for World_15, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:587][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:597][681]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:597][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:603][681]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:603][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:607][681]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.06.05-08.13.17:607][681]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-08.13.17:628][681]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.06.05-08.13.17:630][681]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.06.05-08.13.17:630][681]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.06.05-08.13.17:630][681]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.06.05-08.13.17:946][681]LogGameFeatures: Shutting down game features subsystem
[2025.06.05-08.13.17:946][681]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.06.05-08.13.17:947][681]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.06.05-08.13.17:947][681]LogAudio: Display: Audio Device unregistered from world 'DefaultLevel'.
[2025.06.05-08.13.17:947][681]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.06.05-08.13.17:947][681]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.05-08.13.17:952][681]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.05-08.13.17:960][681]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.06.05-08.13.17:960][681]LogAudio: Display: Audio Device Manager Shutdown
[2025.06.05-08.13.17:963][681]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.06.05-08.13.17:963][681]LogExit: Preparing to exit.
[2025.06.05-08.13.18:248][681]LogUObjectHash: Compacting FUObjectHashTables data took   2.31ms
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_null_above_selected_PY.add_null_above_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_all' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_x' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_y' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_z' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_scale' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_name_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/set_bone_reference_pose_PY.set_bone_reference_pose' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/ControlRigWorkflows/workflow_fbik_import_ik_rig_PY.import_ik_rig_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:257][681]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.ControlOutputFormat' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-08.13.18:919][681]LogEditorDataStorage: Deinitializing
