# This file is part of the FidelityFX SDK.
# 
# Copyright (C) 2024 Advanced Micro Devices, Inc.
# 
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files(the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and /or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions :
# 
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
# 
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
# THE SOFTWARE.

set(FSR1_BASE_ARGS
    -reflection -deps=gcc -DFFX_GPU=1)

set(FSR1_PERMUTATION_ARGS
    # Reproject can use either reference lanczos or LUT
	-DFFX_FSR1_OPTION_APPLY_RCAS={0,1}
    -DFFX_FSR1_OPTION_RCAS_PASSTHROUGH_ALPHA={0,1}
	-DFFX_FSR1_OPTION_SRGB_CONVERSIONS={0,1})

set(FSR1_INCLUDE_ARGS
	"${FFX_GPU_PATH}"
	"${FFX_GPU_PATH}/fsr1")

if (NOT FSR1_SHADER_EXT)
    set(FSR1_SHADER_EXT *)
endif()

file(GLOB FSR1_SHADERS
    "shaders/fsr1/ffx_fsr1_easu_pass.${FSR1_SHADER_EXT}"
    "shaders/fsr1/ffx_fsr1_rcas_pass.${FSR1_SHADER_EXT}")

compile_shaders_with_depfile(
    "${FFX_SC_EXECUTABLE}"
    "${FSR1_BASE_ARGS}" "${FSR1_API_BASE_ARGS}" "${FSR1_PERMUTATION_ARGS}" "${FSR1_INCLUDE_ARGS}"
    "${FSR1_SHADERS}" "${FFX_PASS_SHADER_OUTPUT_PATH}" FSR1_PERMUTATION_OUTPUTS)

add_shader_output("${FSR1_PERMUTATION_OUTPUTS}")
