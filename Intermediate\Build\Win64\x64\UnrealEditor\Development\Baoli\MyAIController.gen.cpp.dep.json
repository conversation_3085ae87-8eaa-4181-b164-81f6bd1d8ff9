{"Version": "1.2", "Data": {"Source": "h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\unrealeditor\\inc\\baoli\\uht\\myaicontroller.gen.cpp", "ProvidedModule": "", "PCH": "h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\x64\\baolieditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\baoli\\definitions.baoli.h", "h:\\p4\\dev\\baoli\\source\\baoli\\ai\\myaicontroller.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\aicontroller.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\aitypes.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystemtypes.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigationsystemconfig.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationsystemconfig.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aitypes.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptionlistenerinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptionlistenerinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\genericteamagentinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\genericteamagentinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\public\\visuallogger\\visualloggerdebugsnapshotinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\visualloggerdebugsnapshotinterface.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aicontroller.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\navigation\\pathfollowingcomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationdata.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdatainterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationdatainterface.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationdata.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\airesourceinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\airesourceinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\navmovementcomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\navmovementinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navmovementinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\pathfollowingagentinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pathfollowingagentinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\movementcomponent.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\movementcomponent.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navmovementcomponent.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\pathfollowingcomponent.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptioncomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptiontypes.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptiontypes.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisense.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisense.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptionsystem.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\aisubsystem.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\aisystem.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\aisystembase.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\aisystembase.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisystem.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisubsystem.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptionsystem.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptioncomponent.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisenseconfig_sight.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisenseconfig.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisenseconfig.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\aimodule\\classes\\perception\\aisense_sight.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\mttransactionallysafeaccessdetector.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisense_sight.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisenseconfig_sight.generated.h", "h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\unrealeditor\\inc\\baoli\\uht\\myaicontroller.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}