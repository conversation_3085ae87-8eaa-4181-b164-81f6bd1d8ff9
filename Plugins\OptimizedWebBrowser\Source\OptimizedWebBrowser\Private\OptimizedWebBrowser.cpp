// Copyright Epic Games, Inc. All Rights Reserved.
// Modifications Copyright (C) 2025 Anshul <PERSON>

#include "OptimizedWebBrowser.h"
#include "SWebBrowser.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Text/STextBlock.h"
#include "WebBrowserModule.h"
#include "IWebBrowserSingleton.h"
#include "WebBrowserOptimizer.h"
#include "Framework/Application/SlateApplication.h"
#include "Math/UnrealMathUtility.h"
#include "Templates/SharedPointer.h"

#define LOCTEXT_NAMESPACE "WebBrowser"

/////////////////////////////////////////////////////
// UOptimizedWebBrowser

UOptimizedWebBrowser::UOptimizedWebBrowser(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
    , BrowserFrameRate(30)  // Default to 30 fps for better performance
    , bHardwareAccelerationEnabled(true)
    , BackgroundColor(255, 255, 255, 255)  // Default to opaque white
    , bBackgroundTabThrottlingEnabled(true)
{
    bIsVariable = true;
}

void UOptimizedWebBrowser::LoadURL(FString NewURL)
{
    if (WebBrowserWidget.IsValid())
    {
        return WebBrowserWidget->LoadURL(NewURL);
    }
}

void UOptimizedWebBrowser::LoadString(FString Contents, FString DummyURL)
{
    if (WebBrowserWidget.IsValid())
    {
        return WebBrowserWidget->LoadString(Contents, DummyURL);
    }
}

void UOptimizedWebBrowser::ExecuteJavascript(const FString& ScriptText)
{
    if (WebBrowserWidget.IsValid())
    {
        return WebBrowserWidget->ExecuteJavascript(ScriptText);
    }
}

FText UOptimizedWebBrowser::GetTitleText() const
{
    if (WebBrowserWidget.IsValid())
    {
        return WebBrowserWidget->GetTitleText();
    }

    return FText::GetEmpty();
}

FString UOptimizedWebBrowser::GetUrl() const
{
    if (WebBrowserWidget.IsValid())
    {
        return WebBrowserWidget->GetUrl();
    }

    return FString();
}

void UOptimizedWebBrowser::LogToConsole(const FString& Message)
{
    if (WebBrowserWidget.IsValid())
    {
        // Escape any quotes in the message to avoid breaking the JavaScript
        FString EscapedMessage = Message;
        EscapedMessage.ReplaceInline(TEXT("\""), TEXT("\\\""));
        EscapedMessage.ReplaceInline(TEXT("'"), TEXT("\\'"));

        // Create the JavaScript code to log the message to the console
        FString JavaScriptCode = FString::Printf(TEXT("console.log(\"[UE] %s\");"), *EscapedMessage);

        // Execute the JavaScript
        WebBrowserWidget->ExecuteJavascript(JavaScriptCode);
    }
}

void UOptimizedWebBrowser::SetBrowserFrameRate(int32 FrameRate)
{
    BrowserFrameRate = FMath::Clamp(FrameRate, 15, 120);

    // Rebuild the widget to apply the new frame rate
    if (WebBrowserWidget.IsValid())
    {
        RebuildWidget();
    }
}

void UOptimizedWebBrowser::SetHardwareAccelerationEnabled(bool bEnabled)
{
    bHardwareAccelerationEnabled = bEnabled;

    // Hardware acceleration changes require a rebuild
    if (WebBrowserWidget.IsValid())
    {
        RebuildWidget();
    }
}

void UOptimizedWebBrowser::SetBackgroundColor(FColor Color)
{
    BackgroundColor = Color;

    // Rebuild the widget to apply the new background color
    if (WebBrowserWidget.IsValid())
    {
        RebuildWidget();
    }
}

void UOptimizedWebBrowser::SetBackgroundTabThrottlingEnabled(bool bEnabled)
{
    bBackgroundTabThrottlingEnabled = bEnabled;

    // Apply throttling settings via JavaScript
    if (WebBrowserWidget.IsValid())
    {
        if (bEnabled)
        {
            // Enable background tab throttling
            WebBrowserWidget->ExecuteJavascript(TEXT("if (window.requestIdleCallback) { window._throttleTimer = window.requestIdleCallback(() => {}, { timeout: 1000 }); }"));
        }
        else
        {
            // Disable background tab throttling
            WebBrowserWidget->ExecuteJavascript(TEXT("if (window.cancelIdleCallback && window._throttleTimer) { window.cancelIdleCallback(window._throttleTimer); window._throttleTimer = null; }"));
        }
    }
}

void UOptimizedWebBrowser::ReleaseSlateResources(bool bReleaseChildren)
{
    Super::ReleaseSlateResources(bReleaseChildren);

    WebBrowserWidget.Reset();
}

TSharedRef<SWidget> UOptimizedWebBrowser::RebuildWidget()
{
    if (IsDesignTime())
    {
        return SNew(SBox)
            .HAlign(HAlign_Center)
            .VAlign(VAlign_Center)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("Web Browser", "Optimized Web Browser"))
            ];
    }
    else
    {
        // Create the web browser widget with optimized settings
        WebBrowserWidget = SNew(SWebBrowser)
            .InitialURL(InitialURL)
            .ShowControls(false)
            .SupportsTransparency(bSupportsTransparency)
            .BackgroundColor(BackgroundColor)
            .BrowserFrameRate(BrowserFrameRate)
            .OnUrlChanged(BIND_UOBJECT_DELEGATE(FOnTextChanged, HandleOnUrlChanged))
            .OnBeforePopup(BIND_UOBJECT_DELEGATE(FOnBeforePopupDelegate, HandleOnBeforePopup))
            .OnConsoleMessage(BIND_UOBJECT_DELEGATE(FOnConsoleMessageDelegate, HandleOnConsoleMessage));

        // Apply optimizations if the widget is valid
        if (WebBrowserWidget.IsValid())
        {
            // Create optimization settings
            FWebBrowserOptimizerSettings Settings;
            Settings.BrowserFrameRate = BrowserFrameRate;
            Settings.bHardwareAccelerationEnabled = bHardwareAccelerationEnabled;
            Settings.BackgroundColor = BackgroundColor;
            Settings.bBackgroundTabThrottlingEnabled = bBackgroundTabThrottlingEnabled;

            // Apply optimizations
            UWebBrowserOptimizer::ApplyOptimizations(this, Settings);
        }

        return WebBrowserWidget.ToSharedRef();
    }
}

void UOptimizedWebBrowser::SynchronizeProperties()
{
    Super::SynchronizeProperties();
}

void UOptimizedWebBrowser::HandleOnUrlChanged(const FText& InText)
{
    OnUrlChanged.Broadcast(InText);
}

void UOptimizedWebBrowser::HandleOnConsoleMessage(const FString& Message, const FString& Source, int32 Line, EWebBrowserConsoleLogSeverity Severity)
{
    OnConsoleMessage.Broadcast(Message, Source, Line);
}

bool UOptimizedWebBrowser::HandleOnBeforePopup(FString URL, FString Frame)
{
    if (OnBeforePopup.IsBound())
    {
        if (IsInGameThread())
        {
            OnBeforePopup.Broadcast(URL, Frame);
        }
        else
        {
            // Retry on the GameThread.
            TWeakObjectPtr<UOptimizedWebBrowser> WeakThis = this;
            FFunctionGraphTask::CreateAndDispatchWhenReady([WeakThis, URL, Frame]()
            {
                if (WeakThis.IsValid())
                {
                    WeakThis->HandleOnBeforePopup(URL, Frame);
                }
            }, TStatId(), nullptr, ENamedThreads::GameThread);
        }

        return true;
    }

    return false;
}

#if WITH_EDITOR

const FText UOptimizedWebBrowser::GetPaletteCategory()
{
    return LOCTEXT("Experimental", "Experimental");
}

#endif

/////////////////////////////////////////////////////

#undef LOCTEXT_NAMESPACE
