// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Interactable/Cover/Almirah.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class ABaoli_Character;
class UAnimMontage;
struct FBranchingPointNotifyPayload;
#ifdef BAOLI_Almirah_generated_h
#error "Almirah.generated.h already included, missing '#pragma once' in Almirah.h"
#endif
#define BAOLI_Almirah_generated_h

#define FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_14_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execClearGarbage); \
	DECLARE_FUNCTION(execOnNotify); \
	DECLARE_FUNCTION(execOnBlendingOutAnim); \
	DECLARE_FUNCTION(execOnNotifyBeginReceived); \
	DECLARE_FUNCTION(execGetFunctionNameAsString); \
	DECLARE_FUNCTION(execInterpAimAlmirah); \
	DECLARE_FUNCTION(execHideOutAlmirah); \
	DECLARE_FUNCTION(execHideInAlmirah);


#define FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_14_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAlmirah(); \
	friend struct Z_Construct_UClass_AAlmirah_Statics; \
public: \
	DECLARE_CLASS(AAlmirah, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Baoli"), NO_API) \
	DECLARE_SERIALIZER(AAlmirah)


#define FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_14_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AAlmirah(AAlmirah&&); \
	AAlmirah(const AAlmirah&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAlmirah); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAlmirah); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAlmirah) \
	NO_API virtual ~AAlmirah();


#define FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_11_PROLOG
#define FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_14_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_14_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_14_INCLASS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_14_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BAOLI_API UClass* StaticClass<class AAlmirah>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
