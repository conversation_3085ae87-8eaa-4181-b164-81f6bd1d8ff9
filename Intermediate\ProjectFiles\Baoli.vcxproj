<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Invalid|x64">
      <Configuration>Invalid</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame|x64">
      <Configuration>DebugGame</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64_DebugGame|x64">
      <Configuration>Win64_arm64_DebugGame</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64ec_DebugGame|x64">
      <Configuration>Win64_arm64ec_DebugGame</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame_Editor|x64">
      <Configuration>DebugGame_Editor</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64_DebugGame_Editor|x64">
      <Configuration>Win64_arm64_DebugGame_Editor</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64ec_DebugGame_Editor|x64">
      <Configuration>Win64_arm64ec_DebugGame_Editor</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development|x64">
      <Configuration>Development</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64_Development|x64">
      <Configuration>Win64_arm64_Development</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64ec_Development|x64">
      <Configuration>Win64_arm64ec_Development</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development_Editor|x64">
      <Configuration>Development_Editor</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64_Development_Editor|x64">
      <Configuration>Win64_arm64_Development_Editor</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64ec_Development_Editor|x64">
      <Configuration>Win64_arm64ec_Development_Editor</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|x64">
      <Configuration>Shipping</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64_Shipping|x64">
      <Configuration>Win64_arm64_Shipping</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Win64_arm64ec_Shipping|x64">
      <Configuration>Win64_arm64ec_Shipping</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{0CBE672F-5827-37F2-A932-E043E7DB41EF}</ProjectGuid>
    <RootNamespace>Baoli</RootNamespace>
  </PropertyGroup>
  <Import Project="UECommon.props" />
  <ImportGroup Label="ExtensionSettings" />
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <NMakePreprocessorDefinitions>$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <IncludePath>$(IncludePath);D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CorePreciseFP\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CorePreciseFP\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\VerseVMBytecode;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Core\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Core\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\JsonUtilities\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\JsonUtilities\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Json\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Json\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceLog\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceLog\VNI;D:\UE_5.5\Engine\Source;D:\UE_5.5\Engine\Source\Runtime\CorePreciseFP\Internal;D:\UE_5.5\Engine\Source\Runtime\CorePreciseFP\Public;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Internal;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Public;D:\UE_5.5\Engine\Source\Runtime\Core\Internal;D:\UE_5.5\Engine\Source\Runtime\Core\Public;D:\UE_5.5\Engine\Source\Runtime\JsonUtilities\Public;D:\UE_5.5\Engine\Source\Runtime\Json\Public;D:\UE_5.5\Engine\Source\Runtime\TraceLog\Public;D:\UE_5.5\Engine\Source\ThirdParty\GuidelinesSupportLibrary\GSL-1144\include;D:\UE_5.5\Engine\Source\ThirdParty\RapidJSON\1.1.0;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ActorPickerMode\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ActorPickerMode\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AdvancedPreviewScene\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AdvancedPreviewScene\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnalyticsET\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnalyticsET\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Analytics\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Analytics\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationBlueprintEditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationBlueprintEditor\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationDataController\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationDataController\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditMode\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditMode\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditor\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationWidgets\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationWidgets\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ApplicationCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ApplicationCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetDefinition\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetDefinition\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetRegistry\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetRegistry\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTagsEditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTagsEditor\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTools\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTools\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioEditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioEditor\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioExtensions\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioExtensions\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkEngine\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkEngine\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerXAudio2\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerXAudio2\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixer\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixer\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioPlatformConfiguration\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioPlatformConfiguration\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationController\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationController\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationTest\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationTest\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BlueprintGraph\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BlueprintGraph\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosSolverEngine\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosSolverEngine\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosVDRuntime\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosVDRuntime\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Chaos\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Chaos\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClassViewer\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClassViewer\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothSysRuntimeIntrfc\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothSysRuntimeIntrfc\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CollectionManager\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CollectionManager\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CommonMenuExtensions\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CommonMenuExtensions\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Constraints\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Constraints\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowserData\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowserData\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowser\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowser\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CookOnTheFly\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CookOnTheFly\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreOnline\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreOnline\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowEngine\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowEngine\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowSimulation\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowSimulation\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DesktopPlatform\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DesktopPlatform\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DetailCustomizations\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DetailCustomizations\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperSettings\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperSettings\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperToolSettings\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperToolSettings\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DirectoryWatcher\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DirectoryWatcher\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Documentation\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Documentation\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorConfig\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorConfig\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorFramework\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorFramework\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorSubsystem\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorSubsystem\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineMessages\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineMessages\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineSettings\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineSettings\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Engine\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Engine\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldNotification\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldNotification\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldSystemEngine\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldSystemEngine\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FunctionalTesting\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FunctionalTesting\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTags\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTags\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTasks\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTasks\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCollectionEngine\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCollectionEngine\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GraphEditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GraphEditor\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HeadMountedDisplay\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HeadMountedDisplay\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Horde\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Horde\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HTTP\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HTTP\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageWrapper\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageWrapper\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InputCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InputCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InteractiveToolsFramework\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InteractiveToolsFramework\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeEngine\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeEngine\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IrisCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IrisCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\KismetCompiler\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\KismetCompiler\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Kismet\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Kismet\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Landscape\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Landscape\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelEditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelEditor\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Localization\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Localization\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MainFrame\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MainFrame\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialUtilities\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialUtilities\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Merge\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Merge\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshBuilder\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshBuilder\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshDescription\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshDescription\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshMergeUtilities\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshMergeUtilities\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshReductionInterface\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshReductionInterface\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilitiesCommon\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilitiesCommon\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilities\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilities\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MessagingCommon\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MessagingCommon\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Messaging\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Messaging\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneCapture\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneCapture\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneTracks\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneTracks\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieScene\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieScene\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MSQS\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MSQS\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NavigationSystem\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NavigationSystem\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCommon\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCommon\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkFileSystem\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkFileSystem\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Networking\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Networking\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkReplayStreaming\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkReplayStreaming\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\OpenGLDrv\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\OpenGLDrv\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PacketHandler\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PacketHandler\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PakFile\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PakFile\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Persona\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Persona\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsUtilities\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsUtilities\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Projects\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Projects\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyEditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyEditor\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyPath\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyPath\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RawMesh\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RawMesh\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ReliableHComp\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ReliableHComp\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RenderCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RenderCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Renderer\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Renderer\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RHI\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RHI\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RSA\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RSA\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SandboxFile\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SandboxFile\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SceneDepthPickerMode\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SceneDepthPickerMode\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Settings\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Settings\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SignalProcessing\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SignalProcessing\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshDescription\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshDescription\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletonEditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletonEditor\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SlateCore\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SlateCore\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Slate\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Slate\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Sockets\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Sockets\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SourceControl\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SourceControl\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StaticMeshDescription\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StaticMeshDescription\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StatusBar\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StatusBar\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StructViewer\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StructViewer\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectDataInterface\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectDataInterface\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectEditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectEditor\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SynthBenchmark\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SynthBenchmark\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TargetPlatform\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TargetPlatform\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureBuildUtilities\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureBuildUtilities\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormat\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormat\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TimeManagement\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TimeManagement\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenusEditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenusEditor\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenus\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenus\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolWidgets\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolWidgets\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementFramework\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementFramework\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementRuntime\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementRuntime\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UELibSampleRate\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UELibSampleRate\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UMG\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UMG\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UncontrolledChangelists\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UncontrolledChangelists\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UniversalObjectLocator\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UniversalObjectLocator\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEdMessages\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEdMessages\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEd\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEd\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ViewportInteraction\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ViewportInteraction\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Voronoi\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Voronoi\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VREditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VREditor\VNI;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WidgetRegistration\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WidgetRegistration\VNI;D:\UE_5.5\Engine\Shaders\Public;$(DefaultSystemIncludePaths);</IncludePath>
    <NMakeForcedIncludes>$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeAssemblySearchPath>$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <AdditionalOptions>/std:c++20  /DSAL_NO_ATTRIBUTE_DECLARATIONS=1 /permissive- /Zc:strictStrings- /Zc:__cplusplus</AdditionalOptions>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Baoli-Win64-DebugGame.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_DebugGame|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Baoli-Win64-DebugGamearm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_DebugGame|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_DebugGame|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Baoli-Win64-DebugGamearm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_DebugGame|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Baoli Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>D:\UE_5.5\Engine\Binaries\Win64\UnrealEditor-Win64-DebugGame.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_DebugGame_Editor|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>D:\UE_5.5\Engine\Binaries\Win64\UnrealEditor-Win64-DebugGamearm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_DebugGame_Editor|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_DebugGame_Editor|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>D:\UE_5.5\Engine\Binaries\Win64\UnrealEditor-Win64-DebugGamearm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_DebugGame_Editor|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) BaoliEditor Win64 DebugGame -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Baoli.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Development|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Development|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Baoliarm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Development|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Development|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Baoliarm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Development|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Baoli Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>D:\UE_5.5\Engine\Binaries\Win64\UnrealEditor.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Development_Editor|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>D:\UE_5.5\Engine\Binaries\Win64\UnrealEditorarm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Development_Editor|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Development_Editor|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>D:\UE_5.5\Engine\Binaries\Win64\UnrealEditorarm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Development_Editor|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) BaoliEditor Win64 Development -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Baoli-Win64-Shipping.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Shipping|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Baoli-Win64-Shippingarm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64_Shipping|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Shipping|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Baoli-Win64-Shippingarm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Win64_arm64ec_Shipping|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Baoli Win64 Shipping -Project="$(SolutionDir)Baoli.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup>
    <ProjectAdditionalIncludeDirectories>..\..\Plugins\Inkpot\Intermediate\Build\Win64\UnrealEditor\Inc\InkPlusPlus\UHT</ProjectAdditionalIncludeDirectories>
    <ProjectAdditionalIncludeDirectories_1>..\..\Plugins\Inkpot\Intermediate\Build\Win64\UnrealEditor\Inc\InkPlusPlus\VNI</ProjectAdditionalIncludeDirectories_1>
    <ProjectAdditionalIncludeDirectories_2>..\..\Plugins\Inkpot\Intermediate\Build\Win64\UnrealEditor\Inc\Inkpot\UHT</ProjectAdditionalIncludeDirectories_2>
    <ProjectAdditionalIncludeDirectories_3>..\..\Plugins\Inkpot\Intermediate\Build\Win64\UnrealEditor\Inc\Inkpot\VNI</ProjectAdditionalIncludeDirectories_3>
    <ProjectAdditionalIncludeDirectories_4>..\..\Plugins\Inkpot\Source</ProjectAdditionalIncludeDirectories_4>
    <ProjectAdditionalIncludeDirectories_5>..\..\Plugins\Inkpot\Source\InkPlusPlus\Public</ProjectAdditionalIncludeDirectories_5>
    <ProjectAdditionalIncludeDirectories_6>..\..\Plugins\Inkpot\Source\Inkpot\Public</ProjectAdditionalIncludeDirectories_6>
    <ProjectAdditionalIncludeDirectories_7>D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorStyle\UHT</ProjectAdditionalIncludeDirectories_7>
    <ProjectAdditionalIncludeDirectories_8>D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorStyle\VNI</ProjectAdditionalIncludeDirectories_8>
    <ProjectAdditionalIncludeDirectories_9>D:\UE_5.5\Engine\Shaders\Shared</ProjectAdditionalIncludeDirectories_9>
    <ProjectAdditionalIncludeDirectories_10>D:\UE_5.5\Engine\Source\Developer\AnimationDataController\Public</ProjectAdditionalIncludeDirectories_10>
    <ProjectAdditionalIncludeDirectories_11>D:\UE_5.5\Engine\Source\Developer\AnimationWidgets\Public</ProjectAdditionalIncludeDirectories_11>
    <ProjectAdditionalIncludeDirectories_12>D:\UE_5.5\Engine\Source\Developer\AssetTools\Internal</ProjectAdditionalIncludeDirectories_12>
    <ProjectAdditionalIncludeDirectories_13>D:\UE_5.5\Engine\Source\Developer\AssetTools\Public</ProjectAdditionalIncludeDirectories_13>
    <ProjectAdditionalIncludeDirectories_14>D:\UE_5.5\Engine\Source\Developer\AutomationController\Public</ProjectAdditionalIncludeDirectories_14>
    <ProjectAdditionalIncludeDirectories_15>D:\UE_5.5\Engine\Source\Developer\CollectionManager\Public</ProjectAdditionalIncludeDirectories_15>
    <ProjectAdditionalIncludeDirectories_16>D:\UE_5.5\Engine\Source\Developer\DesktopPlatform\Internal</ProjectAdditionalIncludeDirectories_16>
    <ProjectAdditionalIncludeDirectories_17>D:\UE_5.5\Engine\Source\Developer\DesktopPlatform\Public</ProjectAdditionalIncludeDirectories_17>
    <ProjectAdditionalIncludeDirectories_18>D:\UE_5.5\Engine\Source\Developer\DeveloperToolSettings\Classes</ProjectAdditionalIncludeDirectories_18>
    <ProjectAdditionalIncludeDirectories_19>D:\UE_5.5\Engine\Source\Developer\DeveloperToolSettings\Public</ProjectAdditionalIncludeDirectories_19>
    <ProjectAdditionalIncludeDirectories_20>D:\UE_5.5\Engine\Source\Developer\DirectoryWatcher\Public</ProjectAdditionalIncludeDirectories_20>
    <ProjectAdditionalIncludeDirectories_21>D:\UE_5.5\Engine\Source\Developer\FunctionalTesting\Classes</ProjectAdditionalIncludeDirectories_21>
    <ProjectAdditionalIncludeDirectories_22>D:\UE_5.5\Engine\Source\Developer\FunctionalTesting\Public</ProjectAdditionalIncludeDirectories_22>
    <ProjectAdditionalIncludeDirectories_23>D:\UE_5.5\Engine\Source\Developer\Horde\Public</ProjectAdditionalIncludeDirectories_23>
    <ProjectAdditionalIncludeDirectories_24>D:\UE_5.5\Engine\Source\Developer\Localization\Public</ProjectAdditionalIncludeDirectories_24>
    <ProjectAdditionalIncludeDirectories_25>D:\UE_5.5\Engine\Source\Developer\MaterialUtilities\Public</ProjectAdditionalIncludeDirectories_25>
    <ProjectAdditionalIncludeDirectories_26>D:\UE_5.5\Engine\Source\Developer\Merge\Public</ProjectAdditionalIncludeDirectories_26>
    <ProjectAdditionalIncludeDirectories_27>D:\UE_5.5\Engine\Source\Developer\MeshBuilder\Public</ProjectAdditionalIncludeDirectories_27>
    <ProjectAdditionalIncludeDirectories_28>D:\UE_5.5\Engine\Source\Developer\MeshMergeUtilities\Public</ProjectAdditionalIncludeDirectories_28>
    <ProjectAdditionalIncludeDirectories_29>D:\UE_5.5\Engine\Source\Developer\MeshReductionInterface\Public</ProjectAdditionalIncludeDirectories_29>
    <ProjectAdditionalIncludeDirectories_30>D:\UE_5.5\Engine\Source\Developer\MeshUtilities\Public</ProjectAdditionalIncludeDirectories_30>
    <ProjectAdditionalIncludeDirectories_31>D:\UE_5.5\Engine\Source\Developer\PhysicsUtilities\Public</ProjectAdditionalIncludeDirectories_31>
    <ProjectAdditionalIncludeDirectories_32>D:\UE_5.5\Engine\Source\Developer\Settings\Public</ProjectAdditionalIncludeDirectories_32>
    <ProjectAdditionalIncludeDirectories_33>D:\UE_5.5\Engine\Source\Developer\SourceControl\Public</ProjectAdditionalIncludeDirectories_33>
    <ProjectAdditionalIncludeDirectories_34>D:\UE_5.5\Engine\Source\Developer\TargetPlatform\Public</ProjectAdditionalIncludeDirectories_34>
    <ProjectAdditionalIncludeDirectories_35>D:\UE_5.5\Engine\Source\Developer\TextureBuildUtilities\Public</ProjectAdditionalIncludeDirectories_35>
    <ProjectAdditionalIncludeDirectories_36>D:\UE_5.5\Engine\Source\Developer\TextureFormat\Public</ProjectAdditionalIncludeDirectories_36>
    <ProjectAdditionalIncludeDirectories_37>D:\UE_5.5\Engine\Source\Developer\ToolMenus\Public</ProjectAdditionalIncludeDirectories_37>
    <ProjectAdditionalIncludeDirectories_38>D:\UE_5.5\Engine\Source\Developer\ToolWidgets\Public</ProjectAdditionalIncludeDirectories_38>
    <ProjectAdditionalIncludeDirectories_39>D:\UE_5.5\Engine\Source\Developer\UncontrolledChangelists\Public</ProjectAdditionalIncludeDirectories_39>
    <ProjectAdditionalIncludeDirectories_40>D:\UE_5.5\Engine\Source\Developer\WidgetRegistration\Public</ProjectAdditionalIncludeDirectories_40>
    <ProjectAdditionalIncludeDirectories_41>D:\UE_5.5\Engine\Source\Editor\ActorPickerMode\Public</ProjectAdditionalIncludeDirectories_41>
    <ProjectAdditionalIncludeDirectories_42>D:\UE_5.5\Engine\Source\Editor\AdvancedPreviewScene\Public</ProjectAdditionalIncludeDirectories_42>
    <ProjectAdditionalIncludeDirectories_43>D:\UE_5.5\Engine\Source\Editor\AnimationBlueprintEditor\Public</ProjectAdditionalIncludeDirectories_43>
    <ProjectAdditionalIncludeDirectories_44>D:\UE_5.5\Engine\Source\Editor\AnimationEditMode\Public</ProjectAdditionalIncludeDirectories_44>
    <ProjectAdditionalIncludeDirectories_45>D:\UE_5.5\Engine\Source\Editor\AnimationEditor\Public</ProjectAdditionalIncludeDirectories_45>
    <ProjectAdditionalIncludeDirectories_46>D:\UE_5.5\Engine\Source\Editor\AssetDefinition\Public</ProjectAdditionalIncludeDirectories_46>
    <ProjectAdditionalIncludeDirectories_47>D:\UE_5.5\Engine\Source\Editor\AssetTagsEditor\Public</ProjectAdditionalIncludeDirectories_47>
    <ProjectAdditionalIncludeDirectories_48>D:\UE_5.5\Engine\Source\Editor\AudioEditor\Classes</ProjectAdditionalIncludeDirectories_48>
    <ProjectAdditionalIncludeDirectories_49>D:\UE_5.5\Engine\Source\Editor\AudioEditor\Public</ProjectAdditionalIncludeDirectories_49>
    <ProjectAdditionalIncludeDirectories_50>D:\UE_5.5\Engine\Source\Editor\BlueprintGraph\Classes</ProjectAdditionalIncludeDirectories_50>
    <ProjectAdditionalIncludeDirectories_51>D:\UE_5.5\Engine\Source\Editor\BlueprintGraph\Public</ProjectAdditionalIncludeDirectories_51>
    <ProjectAdditionalIncludeDirectories_52>D:\UE_5.5\Engine\Source\Editor\ClassViewer\Public</ProjectAdditionalIncludeDirectories_52>
    <ProjectAdditionalIncludeDirectories_53>D:\UE_5.5\Engine\Source\Editor\CommonMenuExtensions\Public</ProjectAdditionalIncludeDirectories_53>
    <ProjectAdditionalIncludeDirectories_54>D:\UE_5.5\Engine\Source\Editor\ContentBrowserData\Public</ProjectAdditionalIncludeDirectories_54>
    <ProjectAdditionalIncludeDirectories_55>D:\UE_5.5\Engine\Source\Editor\ContentBrowser\Public</ProjectAdditionalIncludeDirectories_55>
    <ProjectAdditionalIncludeDirectories_56>D:\UE_5.5\Engine\Source\Editor\DetailCustomizations\Public</ProjectAdditionalIncludeDirectories_56>
    <ProjectAdditionalIncludeDirectories_57>D:\UE_5.5\Engine\Source\Editor\Documentation\Public</ProjectAdditionalIncludeDirectories_57>
    <ProjectAdditionalIncludeDirectories_58>D:\UE_5.5\Engine\Source\Editor\EditorConfig\Public</ProjectAdditionalIncludeDirectories_58>
    <ProjectAdditionalIncludeDirectories_59>D:\UE_5.5\Engine\Source\Editor\EditorFramework\Public</ProjectAdditionalIncludeDirectories_59>
    <ProjectAdditionalIncludeDirectories_60>D:\UE_5.5\Engine\Source\Editor\EditorStyle\Public</ProjectAdditionalIncludeDirectories_60>
    <ProjectAdditionalIncludeDirectories_61>D:\UE_5.5\Engine\Source\Editor\EditorSubsystem\Public</ProjectAdditionalIncludeDirectories_61>
    <ProjectAdditionalIncludeDirectories_62>D:\UE_5.5\Engine\Source\Editor\GraphEditor\Public</ProjectAdditionalIncludeDirectories_62>
    <ProjectAdditionalIncludeDirectories_63>D:\UE_5.5\Engine\Source\Editor\KismetCompiler\Public</ProjectAdditionalIncludeDirectories_63>
    <ProjectAdditionalIncludeDirectories_64>D:\UE_5.5\Engine\Source\Editor\Kismet\Classes</ProjectAdditionalIncludeDirectories_64>
    <ProjectAdditionalIncludeDirectories_65>D:\UE_5.5\Engine\Source\Editor\Kismet\Internal</ProjectAdditionalIncludeDirectories_65>
    <ProjectAdditionalIncludeDirectories_66>D:\UE_5.5\Engine\Source\Editor\Kismet\Public</ProjectAdditionalIncludeDirectories_66>
    <ProjectAdditionalIncludeDirectories_67>D:\UE_5.5\Engine\Source\Editor\LevelEditor\Public</ProjectAdditionalIncludeDirectories_67>
    <ProjectAdditionalIncludeDirectories_68>D:\UE_5.5\Engine\Source\Editor\MainFrame\Public</ProjectAdditionalIncludeDirectories_68>
    <ProjectAdditionalIncludeDirectories_69>D:\UE_5.5\Engine\Source\Editor\Persona\Public</ProjectAdditionalIncludeDirectories_69>
    <ProjectAdditionalIncludeDirectories_70>D:\UE_5.5\Engine\Source\Editor\PropertyEditor\Public</ProjectAdditionalIncludeDirectories_70>
    <ProjectAdditionalIncludeDirectories_71>D:\UE_5.5\Engine\Source\Editor\SceneDepthPickerMode\Public</ProjectAdditionalIncludeDirectories_71>
    <ProjectAdditionalIncludeDirectories_72>D:\UE_5.5\Engine\Source\Editor\SkeletonEditor\Public</ProjectAdditionalIncludeDirectories_72>
    <ProjectAdditionalIncludeDirectories_73>D:\UE_5.5\Engine\Source\Editor\StatusBar\Public</ProjectAdditionalIncludeDirectories_73>
    <ProjectAdditionalIncludeDirectories_74>D:\UE_5.5\Engine\Source\Editor\StructViewer\Public</ProjectAdditionalIncludeDirectories_74>
    <ProjectAdditionalIncludeDirectories_75>D:\UE_5.5\Engine\Source\Editor\SubobjectDataInterface\Public</ProjectAdditionalIncludeDirectories_75>
    <ProjectAdditionalIncludeDirectories_76>D:\UE_5.5\Engine\Source\Editor\SubobjectEditor\Public</ProjectAdditionalIncludeDirectories_76>
    <ProjectAdditionalIncludeDirectories_77>D:\UE_5.5\Engine\Source\Editor\ToolMenusEditor\Public</ProjectAdditionalIncludeDirectories_77>
    <ProjectAdditionalIncludeDirectories_78>D:\UE_5.5\Engine\Source\Editor\UnrealEdMessages\Classes</ProjectAdditionalIncludeDirectories_78>
    <ProjectAdditionalIncludeDirectories_79>D:\UE_5.5\Engine\Source\Editor\UnrealEdMessages\Public</ProjectAdditionalIncludeDirectories_79>
    <ProjectAdditionalIncludeDirectories_80>D:\UE_5.5\Engine\Source\Editor\UnrealEd\Classes</ProjectAdditionalIncludeDirectories_80>
    <ProjectAdditionalIncludeDirectories_81>D:\UE_5.5\Engine\Source\Editor\UnrealEd\Public</ProjectAdditionalIncludeDirectories_81>
    <ProjectAdditionalIncludeDirectories_82>D:\UE_5.5\Engine\Source\Editor\VREditor\Public</ProjectAdditionalIncludeDirectories_82>
    <ProjectAdditionalIncludeDirectories_83>D:\UE_5.5\Engine\Source\Editor\ViewportInteraction\Public</ProjectAdditionalIncludeDirectories_83>
    <ProjectAdditionalIncludeDirectories_84>D:\UE_5.5\Engine\Source\Programs\UnrealLightmass\Public</ProjectAdditionalIncludeDirectories_84>
    <ProjectAdditionalIncludeDirectories_85>D:\UE_5.5\Engine\Source\Runtime\Analytics\AnalyticsET\Public</ProjectAdditionalIncludeDirectories_85>
    <ProjectAdditionalIncludeDirectories_86>D:\UE_5.5\Engine\Source\Runtime\Analytics\Analytics\Public</ProjectAdditionalIncludeDirectories_86>
    <ProjectAdditionalIncludeDirectories_87>D:\UE_5.5\Engine\Source\Runtime\AnimationCore\Public</ProjectAdditionalIncludeDirectories_87>
    <ProjectAdditionalIncludeDirectories_88>D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Public</ProjectAdditionalIncludeDirectories_88>
    <ProjectAdditionalIncludeDirectories_89>D:\UE_5.5\Engine\Source\Runtime\AssetRegistry\Internal</ProjectAdditionalIncludeDirectories_89>
    <ProjectAdditionalIncludeDirectories_90>D:\UE_5.5\Engine\Source\Runtime\AssetRegistry\Public</ProjectAdditionalIncludeDirectories_90>
    <ProjectAdditionalIncludeDirectories_91>D:\UE_5.5\Engine\Source\Runtime\AudioExtensions\Public</ProjectAdditionalIncludeDirectories_91>
    <ProjectAdditionalIncludeDirectories_92>D:\UE_5.5\Engine\Source\Runtime\AudioLink\AudioLinkCore\Public</ProjectAdditionalIncludeDirectories_92>
    <ProjectAdditionalIncludeDirectories_93>D:\UE_5.5\Engine\Source\Runtime\AudioLink\AudioLinkEngine\Public</ProjectAdditionalIncludeDirectories_93>
    <ProjectAdditionalIncludeDirectories_94>D:\UE_5.5\Engine\Source\Runtime\AudioMixerCore\Public</ProjectAdditionalIncludeDirectories_94>
    <ProjectAdditionalIncludeDirectories_95>D:\UE_5.5\Engine\Source\Runtime\AudioMixer\Classes</ProjectAdditionalIncludeDirectories_95>
    <ProjectAdditionalIncludeDirectories_96>D:\UE_5.5\Engine\Source\Runtime\AudioMixer\Public</ProjectAdditionalIncludeDirectories_96>
    <ProjectAdditionalIncludeDirectories_97>D:\UE_5.5\Engine\Source\Runtime\AudioPlatformConfiguration\Public</ProjectAdditionalIncludeDirectories_97>
    <ProjectAdditionalIncludeDirectories_98>D:\UE_5.5\Engine\Source\Runtime\AutomationTest\Public</ProjectAdditionalIncludeDirectories_98>
    <ProjectAdditionalIncludeDirectories_99>D:\UE_5.5\Engine\Source\Runtime\ClothingSystemRuntimeInterface\Public</ProjectAdditionalIncludeDirectories_99>
    <ProjectAdditionalIncludeDirectories_100>D:\UE_5.5\Engine\Source\Runtime\CookOnTheFly\Internal</ProjectAdditionalIncludeDirectories_100>
    <ProjectAdditionalIncludeDirectories_101>D:\UE_5.5\Engine\Source\Runtime\CoreOnline\Public</ProjectAdditionalIncludeDirectories_101>
    <ProjectAdditionalIncludeDirectories_102>D:\UE_5.5\Engine\Source\Runtime\DeveloperSettings\Public</ProjectAdditionalIncludeDirectories_102>
    <ProjectAdditionalIncludeDirectories_103>D:\UE_5.5\Engine\Source\Runtime\EngineMessages\Public</ProjectAdditionalIncludeDirectories_103>
    <ProjectAdditionalIncludeDirectories_104>D:\UE_5.5\Engine\Source\Runtime\EngineSettings\Classes</ProjectAdditionalIncludeDirectories_104>
    <ProjectAdditionalIncludeDirectories_105>D:\UE_5.5\Engine\Source\Runtime\EngineSettings\Public</ProjectAdditionalIncludeDirectories_105>
    <ProjectAdditionalIncludeDirectories_106>D:\UE_5.5\Engine\Source\Runtime\Engine\Classes</ProjectAdditionalIncludeDirectories_106>
    <ProjectAdditionalIncludeDirectories_107>D:\UE_5.5\Engine\Source\Runtime\Engine\Internal</ProjectAdditionalIncludeDirectories_107>
    <ProjectAdditionalIncludeDirectories_108>D:\UE_5.5\Engine\Source\Runtime\Engine\Public</ProjectAdditionalIncludeDirectories_108>
    <ProjectAdditionalIncludeDirectories_109>D:\UE_5.5\Engine\Source\Runtime\Experimental\Animation\Constraints\Public</ProjectAdditionalIncludeDirectories_109>
    <ProjectAdditionalIncludeDirectories_110>D:\UE_5.5\Engine\Source\Runtime\Experimental\ChaosCore\Public</ProjectAdditionalIncludeDirectories_110>
    <ProjectAdditionalIncludeDirectories_111>D:\UE_5.5\Engine\Source\Runtime\Experimental\ChaosSolverEngine\Public</ProjectAdditionalIncludeDirectories_111>
    <ProjectAdditionalIncludeDirectories_112>D:\UE_5.5\Engine\Source\Runtime\Experimental\ChaosVisualDebugger\Public</ProjectAdditionalIncludeDirectories_112>
    <ProjectAdditionalIncludeDirectories_113>D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Public</ProjectAdditionalIncludeDirectories_113>
    <ProjectAdditionalIncludeDirectories_114>D:\UE_5.5\Engine\Source\Runtime\Experimental\Dataflow\Core\Public</ProjectAdditionalIncludeDirectories_114>
    <ProjectAdditionalIncludeDirectories_115>D:\UE_5.5\Engine\Source\Runtime\Experimental\Dataflow\Engine\Public</ProjectAdditionalIncludeDirectories_115>
    <ProjectAdditionalIncludeDirectories_116>D:\UE_5.5\Engine\Source\Runtime\Experimental\Dataflow\Simulation\Public</ProjectAdditionalIncludeDirectories_116>
    <ProjectAdditionalIncludeDirectories_117>D:\UE_5.5\Engine\Source\Runtime\Experimental\FieldSystem\Source\FieldSystemEngine\Public</ProjectAdditionalIncludeDirectories_117>
    <ProjectAdditionalIncludeDirectories_118>D:\UE_5.5\Engine\Source\Runtime\Experimental\GeometryCollectionEngine\Public</ProjectAdditionalIncludeDirectories_118>
    <ProjectAdditionalIncludeDirectories_119>D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Public</ProjectAdditionalIncludeDirectories_119>
    <ProjectAdditionalIncludeDirectories_120>D:\UE_5.5\Engine\Source\Runtime\Experimental\Voronoi\Public</ProjectAdditionalIncludeDirectories_120>
    <ProjectAdditionalIncludeDirectories_121>D:\UE_5.5\Engine\Source\Runtime\FieldNotification\Public</ProjectAdditionalIncludeDirectories_121>
    <ProjectAdditionalIncludeDirectories_122>D:\UE_5.5\Engine\Source\Runtime\GameplayTags\Classes</ProjectAdditionalIncludeDirectories_122>
    <ProjectAdditionalIncludeDirectories_123>D:\UE_5.5\Engine\Source\Runtime\GameplayTags\Public</ProjectAdditionalIncludeDirectories_123>
    <ProjectAdditionalIncludeDirectories_124>D:\UE_5.5\Engine\Source\Runtime\GameplayTasks\Classes</ProjectAdditionalIncludeDirectories_124>
    <ProjectAdditionalIncludeDirectories_125>D:\UE_5.5\Engine\Source\Runtime\GameplayTasks\Public</ProjectAdditionalIncludeDirectories_125>
    <ProjectAdditionalIncludeDirectories_126>D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Public</ProjectAdditionalIncludeDirectories_126>
    <ProjectAdditionalIncludeDirectories_127>D:\UE_5.5\Engine\Source\Runtime\HeadMountedDisplay\Public</ProjectAdditionalIncludeDirectories_127>
    <ProjectAdditionalIncludeDirectories_128>D:\UE_5.5\Engine\Source\Runtime\ImageCore\Public</ProjectAdditionalIncludeDirectories_128>
    <ProjectAdditionalIncludeDirectories_129>D:\UE_5.5\Engine\Source\Runtime\ImageWrapper\Public</ProjectAdditionalIncludeDirectories_129>
    <ProjectAdditionalIncludeDirectories_130>D:\UE_5.5\Engine\Source\Runtime\InputCore\Classes</ProjectAdditionalIncludeDirectories_130>
    <ProjectAdditionalIncludeDirectories_131>D:\UE_5.5\Engine\Source\Runtime\InputCore\Public</ProjectAdditionalIncludeDirectories_131>
    <ProjectAdditionalIncludeDirectories_132>D:\UE_5.5\Engine\Source\Runtime\InteractiveToolsFramework\Public</ProjectAdditionalIncludeDirectories_132>
    <ProjectAdditionalIncludeDirectories_133>D:\UE_5.5\Engine\Source\Runtime\Interchange\Core\Public</ProjectAdditionalIncludeDirectories_133>
    <ProjectAdditionalIncludeDirectories_134>D:\UE_5.5\Engine\Source\Runtime\Interchange\Engine\Public</ProjectAdditionalIncludeDirectories_134>
    <ProjectAdditionalIncludeDirectories_135>D:\UE_5.5\Engine\Source\Runtime\Landscape\Classes</ProjectAdditionalIncludeDirectories_135>
    <ProjectAdditionalIncludeDirectories_136>D:\UE_5.5\Engine\Source\Runtime\Landscape\Public</ProjectAdditionalIncludeDirectories_136>
    <ProjectAdditionalIncludeDirectories_137>D:\UE_5.5\Engine\Source\Runtime\MaterialShaderQualitySettings\Classes</ProjectAdditionalIncludeDirectories_137>
    <ProjectAdditionalIncludeDirectories_138>D:\UE_5.5\Engine\Source\Runtime\MeshDescription\Public</ProjectAdditionalIncludeDirectories_138>
    <ProjectAdditionalIncludeDirectories_139>D:\UE_5.5\Engine\Source\Runtime\MeshUtilitiesCommon\Public</ProjectAdditionalIncludeDirectories_139>
    <ProjectAdditionalIncludeDirectories_140>D:\UE_5.5\Engine\Source\Runtime\MessagingCommon\Public</ProjectAdditionalIncludeDirectories_140>
    <ProjectAdditionalIncludeDirectories_141>D:\UE_5.5\Engine\Source\Runtime\Messaging\Public</ProjectAdditionalIncludeDirectories_141>
    <ProjectAdditionalIncludeDirectories_142>D:\UE_5.5\Engine\Source\Runtime\MovieSceneCapture\Public</ProjectAdditionalIncludeDirectories_142>
    <ProjectAdditionalIncludeDirectories_143>D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Public</ProjectAdditionalIncludeDirectories_143>
    <ProjectAdditionalIncludeDirectories_144>D:\UE_5.5\Engine\Source\Runtime\MovieScene\Public</ProjectAdditionalIncludeDirectories_144>
    <ProjectAdditionalIncludeDirectories_145>D:\UE_5.5\Engine\Source\Runtime\NavigationSystem\Public</ProjectAdditionalIncludeDirectories_145>
    <ProjectAdditionalIncludeDirectories_146>D:\UE_5.5\Engine\Source\Runtime\Net\Common\Public</ProjectAdditionalIncludeDirectories_146>
    <ProjectAdditionalIncludeDirectories_147>D:\UE_5.5\Engine\Source\Runtime\Net\Core\Classes</ProjectAdditionalIncludeDirectories_147>
    <ProjectAdditionalIncludeDirectories_148>D:\UE_5.5\Engine\Source\Runtime\Net\Core\Public</ProjectAdditionalIncludeDirectories_148>
    <ProjectAdditionalIncludeDirectories_149>D:\UE_5.5\Engine\Source\Runtime\NetworkFileSystem\Public</ProjectAdditionalIncludeDirectories_149>
    <ProjectAdditionalIncludeDirectories_150>D:\UE_5.5\Engine\Source\Runtime\NetworkReplayStreaming\NetworkReplayStreaming\Public</ProjectAdditionalIncludeDirectories_150>
    <ProjectAdditionalIncludeDirectories_151>D:\UE_5.5\Engine\Source\Runtime\Networking\Public</ProjectAdditionalIncludeDirectories_151>
    <ProjectAdditionalIncludeDirectories_152>D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Internal</ProjectAdditionalIncludeDirectories_152>
    <ProjectAdditionalIncludeDirectories_153>D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Public</ProjectAdditionalIncludeDirectories_153>
    <ProjectAdditionalIncludeDirectories_154>D:\UE_5.5\Engine\Source\Runtime\OpenGLDrv\Public</ProjectAdditionalIncludeDirectories_154>
    <ProjectAdditionalIncludeDirectories_155>D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\PacketHandler\Classes</ProjectAdditionalIncludeDirectories_155>
    <ProjectAdditionalIncludeDirectories_156>D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\PacketHandler\Public</ProjectAdditionalIncludeDirectories_156>
    <ProjectAdditionalIncludeDirectories_157>D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\ReliabilityHandlerComponent\Public</ProjectAdditionalIncludeDirectories_157>
    <ProjectAdditionalIncludeDirectories_158>D:\UE_5.5\Engine\Source\Runtime\PakFile\Internal</ProjectAdditionalIncludeDirectories_158>
    <ProjectAdditionalIncludeDirectories_159>D:\UE_5.5\Engine\Source\Runtime\PakFile\Public</ProjectAdditionalIncludeDirectories_159>
    <ProjectAdditionalIncludeDirectories_160>D:\UE_5.5\Engine\Source\Runtime\PhysicsCore\Public</ProjectAdditionalIncludeDirectories_160>
    <ProjectAdditionalIncludeDirectories_161>D:\UE_5.5\Engine\Source\Runtime\Projects\Internal</ProjectAdditionalIncludeDirectories_161>
    <ProjectAdditionalIncludeDirectories_162>D:\UE_5.5\Engine\Source\Runtime\Projects\Public</ProjectAdditionalIncludeDirectories_162>
    <ProjectAdditionalIncludeDirectories_163>D:\UE_5.5\Engine\Source\Runtime\PropertyPath\Public</ProjectAdditionalIncludeDirectories_163>
    <ProjectAdditionalIncludeDirectories_164>D:\UE_5.5\Engine\Source\Runtime\RHI\Public</ProjectAdditionalIncludeDirectories_164>
    <ProjectAdditionalIncludeDirectories_165>D:\UE_5.5\Engine\Source\Runtime\RSA\Public</ProjectAdditionalIncludeDirectories_165>
    <ProjectAdditionalIncludeDirectories_166>D:\UE_5.5\Engine\Source\Runtime\RawMesh\Public</ProjectAdditionalIncludeDirectories_166>
    <ProjectAdditionalIncludeDirectories_167>D:\UE_5.5\Engine\Source\Runtime\RenderCore\Internal</ProjectAdditionalIncludeDirectories_167>
    <ProjectAdditionalIncludeDirectories_168>D:\UE_5.5\Engine\Source\Runtime\RenderCore\Public</ProjectAdditionalIncludeDirectories_168>
    <ProjectAdditionalIncludeDirectories_169>D:\UE_5.5\Engine\Source\Runtime\Renderer\Internal</ProjectAdditionalIncludeDirectories_169>
    <ProjectAdditionalIncludeDirectories_170>D:\UE_5.5\Engine\Source\Runtime\Renderer\Public</ProjectAdditionalIncludeDirectories_170>
    <ProjectAdditionalIncludeDirectories_171>D:\UE_5.5\Engine\Source\Runtime\SandboxFile\Public</ProjectAdditionalIncludeDirectories_171>
    <ProjectAdditionalIncludeDirectories_172>D:\UE_5.5\Engine\Source\Runtime\SignalProcessing\Public</ProjectAdditionalIncludeDirectories_172>
    <ProjectAdditionalIncludeDirectories_173>D:\UE_5.5\Engine\Source\Runtime\SkeletalMeshDescription\Public</ProjectAdditionalIncludeDirectories_173>
    <ProjectAdditionalIncludeDirectories_174>D:\UE_5.5\Engine\Source\Runtime\SlateCore\Public</ProjectAdditionalIncludeDirectories_174>
    <ProjectAdditionalIncludeDirectories_175>D:\UE_5.5\Engine\Source\Runtime\Slate\Public</ProjectAdditionalIncludeDirectories_175>
    <ProjectAdditionalIncludeDirectories_176>D:\UE_5.5\Engine\Source\Runtime\Sockets\Public</ProjectAdditionalIncludeDirectories_176>
    <ProjectAdditionalIncludeDirectories_177>D:\UE_5.5\Engine\Source\Runtime\StaticMeshDescription\Public</ProjectAdditionalIncludeDirectories_177>
    <ProjectAdditionalIncludeDirectories_178>D:\UE_5.5\Engine\Source\Runtime\SynthBenchmark\Public</ProjectAdditionalIncludeDirectories_178>
    <ProjectAdditionalIncludeDirectories_179>D:\UE_5.5\Engine\Source\Runtime\TimeManagement\Public</ProjectAdditionalIncludeDirectories_179>
    <ProjectAdditionalIncludeDirectories_180>D:\UE_5.5\Engine\Source\Runtime\TypedElementFramework\Public</ProjectAdditionalIncludeDirectories_180>
    <ProjectAdditionalIncludeDirectories_181>D:\UE_5.5\Engine\Source\Runtime\TypedElementFramework\Tests</ProjectAdditionalIncludeDirectories_181>
    <ProjectAdditionalIncludeDirectories_182>D:\UE_5.5\Engine\Source\Runtime\TypedElementRuntime\Public</ProjectAdditionalIncludeDirectories_182>
    <ProjectAdditionalIncludeDirectories_183>D:\UE_5.5\Engine\Source\Runtime\UMG\Public</ProjectAdditionalIncludeDirectories_183>
    <ProjectAdditionalIncludeDirectories_184>D:\UE_5.5\Engine\Source\Runtime\UniversalObjectLocator\Public</ProjectAdditionalIncludeDirectories_184>
    <ProjectAdditionalIncludeDirectories_185>D:\UE_5.5\Engine\Source\ThirdParty\LibTiff\Source</ProjectAdditionalIncludeDirectories_185>
    <ProjectAdditionalIncludeDirectories_186>D:\UE_5.5\Engine\Source\ThirdParty\LibTiff\Source\Win64</ProjectAdditionalIncludeDirectories_186>
    <ProjectAdditionalIncludeDirectories_187>D:\UE_5.5\Engine\Source\ThirdParty\OpenGL</ProjectAdditionalIncludeDirectories_187>
    <ProjectAdditionalIncludeDirectories_188>D:\UE_5.5\Engine\Source\ThirdParty\libSampleRate\Public</ProjectAdditionalIncludeDirectories_188>
    <ClCompile_AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);..\..\Plugins\Inkpot\Source\InkPlusPlus\Private;$(ProjectAdditionalIncludeDirectories);$(ProjectAdditionalIncludeDirectories_1);$(ProjectAdditionalIncludeDirectories_4);$(ProjectAdditionalIncludeDirectories_5)</ClCompile_AdditionalIncludeDirectories>
    <ClCompile_AdditionalIncludeDirectories_1>$(NMakeIncludeSearchPath);..\..\Plugins\Inkpot\Source\InkpotEditor\Private;$(ProjectAdditionalIncludeDirectories_84);$(ProjectAdditionalIncludeDirectories_80);$(ProjectAdditionalIncludeDirectories_81);$(ProjectAdditionalIncludeDirectories_46);$(ProjectAdditionalIncludeDirectories_90);$(ProjectAdditionalIncludeDirectories_89);$(ProjectAdditionalIncludeDirectories_106);$(ProjectAdditionalIncludeDirectories_108);$(ProjectAdditionalIncludeDirectories_107);$(ProjectAdditionalIncludeDirectories_101);$(ProjectAdditionalIncludeDirectories_121);$(ProjectAdditionalIncludeDirectories_147);$(ProjectAdditionalIncludeDirectories_148);$(ProjectAdditionalIncludeDirectories_146);$(ProjectAdditionalIncludeDirectories_128);$(ProjectAdditionalIncludeDirectories_174);$(ProjectAdditionalIncludeDirectories_102);$(ProjectAdditionalIncludeDirectories_130);$(ProjectAdditionalIncludeDirectories_131);$(ProjectAdditionalIncludeDirectories_88);$(ProjectAdditionalIncludeDirectories_164);$(ProjectAdditionalIncludeDirectories_175);$(ProjectAdditionalIncludeDirectories_129);$(ProjectAdditionalIncludeDirectories_141);$(ProjectAdditionalIncludeDirectories_140);$(ProjectAdditionalIncludeDirectories_168);$(ProjectAdditionalIncludeDirectories_167);$(ProjectAdditionalIncludeDirectories_154);$(ProjectAdditionalIncludeDirectories_85);$(ProjectAdditionalIncludeDirectories_86);$(ProjectAdditionalIncludeDirectories_176);$(ProjectAdditionalIncludeDirectories_103);$(ProjectAdditionalIncludeDirectories_104);$(ProjectAdditionalIncludeDirectories_105);$(ProjectAdditionalIncludeDirectories_178);$(ProjectAdditionalIncludeDirectories_122);$(ProjectAdditionalIncludeDirectories_123);$(ProjectAdditionalIncludeDirectories_155);$(ProjectAdditionalIncludeDirectories_156);$(ProjectAdditionalIncludeDirectories_157);$(ProjectAdditionalIncludeDirectories_97);$(ProjectAdditionalIncludeDirectories_138);$(ProjectAdditionalIncludeDirectories_177);$(ProjectAdditionalIncludeDirectories_173);$(ProjectAdditionalIncludeDirectories_87);$(ProjectAdditionalIncludeDirectories_159);$(ProjectAdditionalIncludeDirectories_158);$(ProjectAdditionalIncludeDirectories_165);$(ProjectAdditionalIncludeDirectories_150);$(ProjectAdditionalIncludeDirectories_160);$(ProjectAdditionalIncludeDirectories_110);$(ProjectAdditionalIncludeDirectories_113);$(ProjectAdditionalIncludeDirectories_120);$(ProjectAdditionalIncludeDirectories_126);$(ProjectAdditionalIncludeDirectories_112);$(ProjectAdditionalIncludeDirectories_172);$(ProjectAdditionalIncludeDirectories_91);$(ProjectAdditionalIncludeDirectories_94);$(ProjectAdditionalIncludeDirectories_95);$(ProjectAdditionalIncludeDirectories_96);$(ProjectAdditionalIncludeDirectories_34);$(ProjectAdditionalIncludeDirectories_36);$(ProjectAdditionalIncludeDirectories_17);$(ProjectAdditionalIncludeDirectories_16);$(ProjectAdditionalIncludeDirectories_93);$(ProjectAdditionalIncludeDirectories_92);$(ProjectAdditionalIncludeDirectories_100);$(ProjectAdditionalIncludeDirectories_151);$(ProjectAdditionalIncludeDirectories_35);$(ProjectAdditionalIncludeDirectories_23);$(ProjectAdditionalIncludeDirectories_99);$(ProjectAdditionalIncludeDirectories_119);$(ProjectAdditionalIncludeDirectories_142);$(ProjectAdditionalIncludeDirectories_170);$(ProjectAdditionalIncludeDirectories_169);$(ProjectAdditionalIncludeDirectories_9);$(ProjectAdditionalIncludeDirectories_181);$(ProjectAdditionalIncludeDirectories_180);$(ProjectAdditionalIncludeDirectories_182);$(ProjectAdditionalIncludeDirectories_10);$(ProjectAdditionalIncludeDirectories_43);$(ProjectAdditionalIncludeDirectories_64);$(ProjectAdditionalIncludeDirectories_66);$(ProjectAdditionalIncludeDirectories_65);$(ProjectAdditionalIncludeDirectories_69);$(ProjectAdditionalIncludeDirectories_72);$(ProjectAdditionalIncludeDirectories_11);$(ProjectAdditionalIncludeDirectories_38);$(ProjectAdditionalIncludeDirectories_37);$(ProjectAdditionalIncludeDirectories_45);$(ProjectAdditionalIncludeDirectories_42);$(ProjectAdditionalIncludeDirectories_70);$(ProjectAdditionalIncludeDirectories_58);$(ProjectAdditionalIncludeDirectories_59);$(ProjectAdditionalIncludeDirectories_61);$(ProjectAdditionalIncludeDirectories_132);$(ProjectAdditionalIncludeDirectories_41);$(ProjectAdditionalIncludeDirectories_71);$(ProjectAdditionalIncludeDirectories_44);$(ProjectAdditionalIncludeDirectories_133);$(ProjectAdditionalIncludeDirectories_20);$(ProjectAdditionalIncludeDirectories_57);$(ProjectAdditionalIncludeDirectories_68);$(ProjectAdditionalIncludeDirectories_162);$(ProjectAdditionalIncludeDirectories_161);$(ProjectAdditionalIncludeDirectories_171);$(ProjectAdditionalIncludeDirectories_33);$(ProjectAdditionalIncludeDirectories_39);$(ProjectAdditionalIncludeDirectories_78);$(ProjectAdditionalIncludeDirectories_79);$(ProjectAdditionalIncludeDirectories_50);$(ProjectAdditionalIncludeDirectories_51);$(ProjectAdditionalIncludeDirectories_153);$(ProjectAdditionalIncludeDirectories_152);$(ProjectAdditionalIncludeDirectories_21);$(ProjectAdditionalIncludeDirectories_22);$(ProjectAdditionalIncludeDirectories_14);$(ProjectAdditionalIncludeDirectories_98);$(ProjectAdditionalIncludeDirectories_24);$(ProjectAdditionalIncludeDirectories_48);$(ProjectAdditionalIncludeDirectories_49);$(ProjectAdditionalIncludeDirectories_67);$(ProjectAdditionalIncludeDirectories_53);$(ProjectAdditionalIncludeDirectories_32);$(ProjectAdditionalIncludeDirectories_82);$(ProjectAdditionalIncludeDirectories_83);$(ProjectAdditionalIncludeDirectories_127);$(ProjectAdditionalIncludeDirectories_135);$(ProjectAdditionalIncludeDirectories_136);$(ProjectAdditionalIncludeDirectories_56);$(ProjectAdditionalIncludeDirectories_52);$(ProjectAdditionalIncludeDirectories_62);$(ProjectAdditionalIncludeDirectories_74);$(ProjectAdditionalIncludeDirectories_55);$(ProjectAdditionalIncludeDirectories_13);$(ProjectAdditionalIncludeDirectories_12);$(ProjectAdditionalIncludeDirectories_26);$(ProjectAdditionalIncludeDirectories_15);$(ProjectAdditionalIncludeDirectories_54);$(ProjectAdditionalIncludeDirectories_188);$(ProjectAdditionalIncludeDirectories_149);$(ProjectAdditionalIncludeDirectories_183);$(ProjectAdditionalIncludeDirectories_144);$(ProjectAdditionalIncludeDirectories_179);$(ProjectAdditionalIncludeDirectories_184);$(ProjectAdditionalIncludeDirectories_143);$(ProjectAdditionalIncludeDirectories_109);$(ProjectAdditionalIncludeDirectories_163);$(ProjectAdditionalIncludeDirectories_145);$(ProjectAdditionalIncludeDirectories_118);$(ProjectAdditionalIncludeDirectories_117);$(ProjectAdditionalIncludeDirectories_111);$(ProjectAdditionalIncludeDirectories_114);$(ProjectAdditionalIncludeDirectories_115);$(ProjectAdditionalIncludeDirectories_116);$(ProjectAdditionalIncludeDirectories_27);$(ProjectAdditionalIncludeDirectories_139);$(ProjectAdditionalIncludeDirectories_137);$(ProjectAdditionalIncludeDirectories_77);$(ProjectAdditionalIncludeDirectories_73);$(ProjectAdditionalIncludeDirectories_134);$(ProjectAdditionalIncludeDirectories_18);$(ProjectAdditionalIncludeDirectories_19);$(ProjectAdditionalIncludeDirectories_75);$(ProjectAdditionalIncludeDirectories_76);$(ProjectAdditionalIncludeDirectories_31);$(ProjectAdditionalIncludeDirectories_40);$(ProjectAdditionalIncludeDirectories_124);$(ProjectAdditionalIncludeDirectories_125);$(ProjectAdditionalIncludeDirectories_47);$(ProjectAdditionalIncludeDirectories_30);$(ProjectAdditionalIncludeDirectories_28);$(ProjectAdditionalIncludeDirectories_29);$(ProjectAdditionalIncludeDirectories_166);$(ProjectAdditionalIncludeDirectories_25);$(ProjectAdditionalIncludeDirectories_63);$(ProjectAdditionalIncludeDirectories);$(ProjectAdditionalIncludeDirectories_1);$(ProjectAdditionalIncludeDirectories_4);$(ProjectAdditionalIncludeDirectories_5);$(ProjectAdditionalIncludeDirectories_2);$(ProjectAdditionalIncludeDirectories_3);$(ProjectAdditionalIncludeDirectories_6);..\..\Plugins\Inkpot\Intermediate\Build\Win64\UnrealEditor\Inc\InkpotEditor\UHT;..\..\Plugins\Inkpot\Intermediate\Build\Win64\UnrealEditor\Inc\InkpotEditor\VNI;..\..\Plugins\Inkpot\Source\InkpotEditor\Public;$(ProjectAdditionalIncludeDirectories_186);$(ProjectAdditionalIncludeDirectories_185);$(ProjectAdditionalIncludeDirectories_187)</ClCompile_AdditionalIncludeDirectories_1>
    <ClCompile_AdditionalIncludeDirectories_2>$(NMakeIncludeSearchPath);..\..\Plugins\Inkpot\Source\Inkpot\Private;$(ProjectAdditionalIncludeDirectories_84);$(ProjectAdditionalIncludeDirectories_80);$(ProjectAdditionalIncludeDirectories_81);$(ProjectAdditionalIncludeDirectories_46);$(ProjectAdditionalIncludeDirectories_90);$(ProjectAdditionalIncludeDirectories_89);$(ProjectAdditionalIncludeDirectories_106);$(ProjectAdditionalIncludeDirectories_108);$(ProjectAdditionalIncludeDirectories_107);$(ProjectAdditionalIncludeDirectories_101);$(ProjectAdditionalIncludeDirectories_121);$(ProjectAdditionalIncludeDirectories_147);$(ProjectAdditionalIncludeDirectories_148);$(ProjectAdditionalIncludeDirectories_146);$(ProjectAdditionalIncludeDirectories_128);$(ProjectAdditionalIncludeDirectories_174);$(ProjectAdditionalIncludeDirectories_102);$(ProjectAdditionalIncludeDirectories_130);$(ProjectAdditionalIncludeDirectories_131);$(ProjectAdditionalIncludeDirectories_88);$(ProjectAdditionalIncludeDirectories_164);$(ProjectAdditionalIncludeDirectories_175);$(ProjectAdditionalIncludeDirectories_129);$(ProjectAdditionalIncludeDirectories_141);$(ProjectAdditionalIncludeDirectories_140);$(ProjectAdditionalIncludeDirectories_168);$(ProjectAdditionalIncludeDirectories_167);$(ProjectAdditionalIncludeDirectories_154);$(ProjectAdditionalIncludeDirectories_85);$(ProjectAdditionalIncludeDirectories_86);$(ProjectAdditionalIncludeDirectories_176);$(ProjectAdditionalIncludeDirectories_103);$(ProjectAdditionalIncludeDirectories_104);$(ProjectAdditionalIncludeDirectories_105);$(ProjectAdditionalIncludeDirectories_178);$(ProjectAdditionalIncludeDirectories_122);$(ProjectAdditionalIncludeDirectories_123);$(ProjectAdditionalIncludeDirectories_155);$(ProjectAdditionalIncludeDirectories_156);$(ProjectAdditionalIncludeDirectories_157);$(ProjectAdditionalIncludeDirectories_97);$(ProjectAdditionalIncludeDirectories_138);$(ProjectAdditionalIncludeDirectories_177);$(ProjectAdditionalIncludeDirectories_173);$(ProjectAdditionalIncludeDirectories_87);$(ProjectAdditionalIncludeDirectories_159);$(ProjectAdditionalIncludeDirectories_158);$(ProjectAdditionalIncludeDirectories_165);$(ProjectAdditionalIncludeDirectories_150);$(ProjectAdditionalIncludeDirectories_160);$(ProjectAdditionalIncludeDirectories_110);$(ProjectAdditionalIncludeDirectories_113);$(ProjectAdditionalIncludeDirectories_120);$(ProjectAdditionalIncludeDirectories_126);$(ProjectAdditionalIncludeDirectories_112);$(ProjectAdditionalIncludeDirectories_172);$(ProjectAdditionalIncludeDirectories_91);$(ProjectAdditionalIncludeDirectories_94);$(ProjectAdditionalIncludeDirectories_95);$(ProjectAdditionalIncludeDirectories_96);$(ProjectAdditionalIncludeDirectories_34);$(ProjectAdditionalIncludeDirectories_36);$(ProjectAdditionalIncludeDirectories_17);$(ProjectAdditionalIncludeDirectories_16);$(ProjectAdditionalIncludeDirectories_93);$(ProjectAdditionalIncludeDirectories_92);$(ProjectAdditionalIncludeDirectories_100);$(ProjectAdditionalIncludeDirectories_151);$(ProjectAdditionalIncludeDirectories_35);$(ProjectAdditionalIncludeDirectories_23);$(ProjectAdditionalIncludeDirectories_99);$(ProjectAdditionalIncludeDirectories_119);$(ProjectAdditionalIncludeDirectories_142);$(ProjectAdditionalIncludeDirectories_170);$(ProjectAdditionalIncludeDirectories_169);$(ProjectAdditionalIncludeDirectories_9);$(ProjectAdditionalIncludeDirectories_181);$(ProjectAdditionalIncludeDirectories_180);$(ProjectAdditionalIncludeDirectories_182);$(ProjectAdditionalIncludeDirectories_10);$(ProjectAdditionalIncludeDirectories_43);$(ProjectAdditionalIncludeDirectories_64);$(ProjectAdditionalIncludeDirectories_66);$(ProjectAdditionalIncludeDirectories_65);$(ProjectAdditionalIncludeDirectories_69);$(ProjectAdditionalIncludeDirectories_72);$(ProjectAdditionalIncludeDirectories_11);$(ProjectAdditionalIncludeDirectories_38);$(ProjectAdditionalIncludeDirectories_37);$(ProjectAdditionalIncludeDirectories_45);$(ProjectAdditionalIncludeDirectories_42);$(ProjectAdditionalIncludeDirectories_70);$(ProjectAdditionalIncludeDirectories_58);$(ProjectAdditionalIncludeDirectories_59);$(ProjectAdditionalIncludeDirectories_61);$(ProjectAdditionalIncludeDirectories_132);$(ProjectAdditionalIncludeDirectories_41);$(ProjectAdditionalIncludeDirectories_71);$(ProjectAdditionalIncludeDirectories_44);$(ProjectAdditionalIncludeDirectories_133);$(ProjectAdditionalIncludeDirectories_20);$(ProjectAdditionalIncludeDirectories_57);$(ProjectAdditionalIncludeDirectories_68);$(ProjectAdditionalIncludeDirectories_162);$(ProjectAdditionalIncludeDirectories_161);$(ProjectAdditionalIncludeDirectories_171);$(ProjectAdditionalIncludeDirectories_33);$(ProjectAdditionalIncludeDirectories_39);$(ProjectAdditionalIncludeDirectories_78);$(ProjectAdditionalIncludeDirectories_79);$(ProjectAdditionalIncludeDirectories_50);$(ProjectAdditionalIncludeDirectories_51);$(ProjectAdditionalIncludeDirectories_153);$(ProjectAdditionalIncludeDirectories_152);$(ProjectAdditionalIncludeDirectories_21);$(ProjectAdditionalIncludeDirectories_22);$(ProjectAdditionalIncludeDirectories_14);$(ProjectAdditionalIncludeDirectories_98);$(ProjectAdditionalIncludeDirectories_24);$(ProjectAdditionalIncludeDirectories_48);$(ProjectAdditionalIncludeDirectories_49);$(ProjectAdditionalIncludeDirectories_67);$(ProjectAdditionalIncludeDirectories_53);$(ProjectAdditionalIncludeDirectories_32);$(ProjectAdditionalIncludeDirectories_82);$(ProjectAdditionalIncludeDirectories_83);$(ProjectAdditionalIncludeDirectories_127);$(ProjectAdditionalIncludeDirectories_135);$(ProjectAdditionalIncludeDirectories_136);$(ProjectAdditionalIncludeDirectories_56);$(ProjectAdditionalIncludeDirectories_52);$(ProjectAdditionalIncludeDirectories_62);$(ProjectAdditionalIncludeDirectories_74);$(ProjectAdditionalIncludeDirectories_55);$(ProjectAdditionalIncludeDirectories_13);$(ProjectAdditionalIncludeDirectories_12);$(ProjectAdditionalIncludeDirectories_26);$(ProjectAdditionalIncludeDirectories_15);$(ProjectAdditionalIncludeDirectories_54);$(ProjectAdditionalIncludeDirectories_188);$(ProjectAdditionalIncludeDirectories_149);$(ProjectAdditionalIncludeDirectories_183);$(ProjectAdditionalIncludeDirectories_144);$(ProjectAdditionalIncludeDirectories_179);$(ProjectAdditionalIncludeDirectories_184);$(ProjectAdditionalIncludeDirectories_143);$(ProjectAdditionalIncludeDirectories_109);$(ProjectAdditionalIncludeDirectories_163);$(ProjectAdditionalIncludeDirectories_145);$(ProjectAdditionalIncludeDirectories_118);$(ProjectAdditionalIncludeDirectories_117);$(ProjectAdditionalIncludeDirectories_111);$(ProjectAdditionalIncludeDirectories_114);$(ProjectAdditionalIncludeDirectories_115);$(ProjectAdditionalIncludeDirectories_116);$(ProjectAdditionalIncludeDirectories_27);$(ProjectAdditionalIncludeDirectories_139);$(ProjectAdditionalIncludeDirectories_137);$(ProjectAdditionalIncludeDirectories_77);$(ProjectAdditionalIncludeDirectories_73);$(ProjectAdditionalIncludeDirectories_134);$(ProjectAdditionalIncludeDirectories_18);$(ProjectAdditionalIncludeDirectories_19);$(ProjectAdditionalIncludeDirectories_75);$(ProjectAdditionalIncludeDirectories_76);$(ProjectAdditionalIncludeDirectories_31);$(ProjectAdditionalIncludeDirectories_40);$(ProjectAdditionalIncludeDirectories_124);$(ProjectAdditionalIncludeDirectories_125);$(ProjectAdditionalIncludeDirectories_47);$(ProjectAdditionalIncludeDirectories_30);$(ProjectAdditionalIncludeDirectories_28);$(ProjectAdditionalIncludeDirectories_29);$(ProjectAdditionalIncludeDirectories_166);$(ProjectAdditionalIncludeDirectories_25);$(ProjectAdditionalIncludeDirectories_63);$(ProjectAdditionalIncludeDirectories_2);$(ProjectAdditionalIncludeDirectories_3);$(ProjectAdditionalIncludeDirectories_4);$(ProjectAdditionalIncludeDirectories_6);$(ProjectAdditionalIncludeDirectories);$(ProjectAdditionalIncludeDirectories_1);$(ProjectAdditionalIncludeDirectories_5);$(ProjectAdditionalIncludeDirectories_186);$(ProjectAdditionalIncludeDirectories_185);$(ProjectAdditionalIncludeDirectories_187)</ClCompile_AdditionalIncludeDirectories_2>
    <ClCompile_AdditionalIncludeDirectories_3>$(NMakeIncludeSearchPath);..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Private;$(ProjectAdditionalIncludeDirectories_59);$(ProjectAdditionalIncludeDirectories_61);$(ProjectAdditionalIncludeDirectories_132);$(ProjectAdditionalIncludeDirectories_130);$(ProjectAdditionalIncludeDirectories_131);$(ProjectAdditionalIncludeDirectories_88);$(ProjectAdditionalIncludeDirectories_164);$(ProjectAdditionalIncludeDirectories_138);$(ProjectAdditionalIncludeDirectories_181);$(ProjectAdditionalIncludeDirectories_180);$(ProjectAdditionalIncludeDirectories_182);$(ProjectAdditionalIncludeDirectories_84);$(ProjectAdditionalIncludeDirectories_80);$(ProjectAdditionalIncludeDirectories_81);$(ProjectAdditionalIncludeDirectories_46);$(ProjectAdditionalIncludeDirectories_90);$(ProjectAdditionalIncludeDirectories_89);$(ProjectAdditionalIncludeDirectories_106);$(ProjectAdditionalIncludeDirectories_108);$(ProjectAdditionalIncludeDirectories_107);$(ProjectAdditionalIncludeDirectories_101);$(ProjectAdditionalIncludeDirectories_121);$(ProjectAdditionalIncludeDirectories_147);$(ProjectAdditionalIncludeDirectories_148);$(ProjectAdditionalIncludeDirectories_146);$(ProjectAdditionalIncludeDirectories_128);$(ProjectAdditionalIncludeDirectories_174);$(ProjectAdditionalIncludeDirectories_102);$(ProjectAdditionalIncludeDirectories_175);$(ProjectAdditionalIncludeDirectories_129);$(ProjectAdditionalIncludeDirectories_141);$(ProjectAdditionalIncludeDirectories_140);$(ProjectAdditionalIncludeDirectories_168);$(ProjectAdditionalIncludeDirectories_167);$(ProjectAdditionalIncludeDirectories_154);$(ProjectAdditionalIncludeDirectories_85);$(ProjectAdditionalIncludeDirectories_86);$(ProjectAdditionalIncludeDirectories_176);$(ProjectAdditionalIncludeDirectories_103);$(ProjectAdditionalIncludeDirectories_104);$(ProjectAdditionalIncludeDirectories_105);$(ProjectAdditionalIncludeDirectories_178);$(ProjectAdditionalIncludeDirectories_122);$(ProjectAdditionalIncludeDirectories_123);$(ProjectAdditionalIncludeDirectories_155);$(ProjectAdditionalIncludeDirectories_156);$(ProjectAdditionalIncludeDirectories_157);$(ProjectAdditionalIncludeDirectories_97);$(ProjectAdditionalIncludeDirectories_177);$(ProjectAdditionalIncludeDirectories_173);$(ProjectAdditionalIncludeDirectories_87);$(ProjectAdditionalIncludeDirectories_159);$(ProjectAdditionalIncludeDirectories_158);$(ProjectAdditionalIncludeDirectories_165);$(ProjectAdditionalIncludeDirectories_150);$(ProjectAdditionalIncludeDirectories_160);$(ProjectAdditionalIncludeDirectories_110);$(ProjectAdditionalIncludeDirectories_113);$(ProjectAdditionalIncludeDirectories_120);$(ProjectAdditionalIncludeDirectories_126);$(ProjectAdditionalIncludeDirectories_112);$(ProjectAdditionalIncludeDirectories_172);$(ProjectAdditionalIncludeDirectories_91);$(ProjectAdditionalIncludeDirectories_94);$(ProjectAdditionalIncludeDirectories_95);$(ProjectAdditionalIncludeDirectories_96);$(ProjectAdditionalIncludeDirectories_34);$(ProjectAdditionalIncludeDirectories_36);$(ProjectAdditionalIncludeDirectories_17);$(ProjectAdditionalIncludeDirectories_16);$(ProjectAdditionalIncludeDirectories_93);$(ProjectAdditionalIncludeDirectories_92);$(ProjectAdditionalIncludeDirectories_100);$(ProjectAdditionalIncludeDirectories_151);$(ProjectAdditionalIncludeDirectories_35);$(ProjectAdditionalIncludeDirectories_23);$(ProjectAdditionalIncludeDirectories_99);$(ProjectAdditionalIncludeDirectories_119);$(ProjectAdditionalIncludeDirectories_142);$(ProjectAdditionalIncludeDirectories_170);$(ProjectAdditionalIncludeDirectories_169);$(ProjectAdditionalIncludeDirectories_9);$(ProjectAdditionalIncludeDirectories_10);$(ProjectAdditionalIncludeDirectories_43);$(ProjectAdditionalIncludeDirectories_64);$(ProjectAdditionalIncludeDirectories_66);$(ProjectAdditionalIncludeDirectories_65);$(ProjectAdditionalIncludeDirectories_69);$(ProjectAdditionalIncludeDirectories_72);$(ProjectAdditionalIncludeDirectories_11);$(ProjectAdditionalIncludeDirectories_38);$(ProjectAdditionalIncludeDirectories_37);$(ProjectAdditionalIncludeDirectories_45);$(ProjectAdditionalIncludeDirectories_42);$(ProjectAdditionalIncludeDirectories_70);$(ProjectAdditionalIncludeDirectories_58);$(ProjectAdditionalIncludeDirectories_41);$(ProjectAdditionalIncludeDirectories_71);$(ProjectAdditionalIncludeDirectories_44);$(ProjectAdditionalIncludeDirectories_133);$(ProjectAdditionalIncludeDirectories_20);$(ProjectAdditionalIncludeDirectories_57);$(ProjectAdditionalIncludeDirectories_68);$(ProjectAdditionalIncludeDirectories_162);$(ProjectAdditionalIncludeDirectories_161);$(ProjectAdditionalIncludeDirectories_171);$(ProjectAdditionalIncludeDirectories_33);$(ProjectAdditionalIncludeDirectories_39);$(ProjectAdditionalIncludeDirectories_78);$(ProjectAdditionalIncludeDirectories_79);$(ProjectAdditionalIncludeDirectories_50);$(ProjectAdditionalIncludeDirectories_51);$(ProjectAdditionalIncludeDirectories_153);$(ProjectAdditionalIncludeDirectories_152);$(ProjectAdditionalIncludeDirectories_21);$(ProjectAdditionalIncludeDirectories_22);$(ProjectAdditionalIncludeDirectories_14);$(ProjectAdditionalIncludeDirectories_98);$(ProjectAdditionalIncludeDirectories_24);$(ProjectAdditionalIncludeDirectories_48);$(ProjectAdditionalIncludeDirectories_49);$(ProjectAdditionalIncludeDirectories_67);$(ProjectAdditionalIncludeDirectories_53);$(ProjectAdditionalIncludeDirectories_32);$(ProjectAdditionalIncludeDirectories_82);$(ProjectAdditionalIncludeDirectories_83);$(ProjectAdditionalIncludeDirectories_127);$(ProjectAdditionalIncludeDirectories_135);$(ProjectAdditionalIncludeDirectories_136);$(ProjectAdditionalIncludeDirectories_56);$(ProjectAdditionalIncludeDirectories_52);$(ProjectAdditionalIncludeDirectories_62);$(ProjectAdditionalIncludeDirectories_74);$(ProjectAdditionalIncludeDirectories_55);$(ProjectAdditionalIncludeDirectories_13);$(ProjectAdditionalIncludeDirectories_12);$(ProjectAdditionalIncludeDirectories_26);$(ProjectAdditionalIncludeDirectories_15);$(ProjectAdditionalIncludeDirectories_54);$(ProjectAdditionalIncludeDirectories_188);$(ProjectAdditionalIncludeDirectories_149);$(ProjectAdditionalIncludeDirectories_183);$(ProjectAdditionalIncludeDirectories_144);$(ProjectAdditionalIncludeDirectories_179);$(ProjectAdditionalIncludeDirectories_184);$(ProjectAdditionalIncludeDirectories_143);$(ProjectAdditionalIncludeDirectories_109);$(ProjectAdditionalIncludeDirectories_163);$(ProjectAdditionalIncludeDirectories_145);$(ProjectAdditionalIncludeDirectories_118);$(ProjectAdditionalIncludeDirectories_117);$(ProjectAdditionalIncludeDirectories_111);$(ProjectAdditionalIncludeDirectories_114);$(ProjectAdditionalIncludeDirectories_115);$(ProjectAdditionalIncludeDirectories_116);$(ProjectAdditionalIncludeDirectories_27);$(ProjectAdditionalIncludeDirectories_139);$(ProjectAdditionalIncludeDirectories_137);$(ProjectAdditionalIncludeDirectories_77);$(ProjectAdditionalIncludeDirectories_73);$(ProjectAdditionalIncludeDirectories_134);$(ProjectAdditionalIncludeDirectories_18);$(ProjectAdditionalIncludeDirectories_19);$(ProjectAdditionalIncludeDirectories_75);$(ProjectAdditionalIncludeDirectories_76);$(ProjectAdditionalIncludeDirectories_31);$(ProjectAdditionalIncludeDirectories_40);$(ProjectAdditionalIncludeDirectories_124);$(ProjectAdditionalIncludeDirectories_125);$(ProjectAdditionalIncludeDirectories_47);$(ProjectAdditionalIncludeDirectories_30);$(ProjectAdditionalIncludeDirectories_28);$(ProjectAdditionalIncludeDirectories_29);$(ProjectAdditionalIncludeDirectories_166);$(ProjectAdditionalIncludeDirectories_25);$(ProjectAdditionalIncludeDirectories_63);D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WebBrowserTexture\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WebBrowserTexture\VNI;D:\UE_5.5\Engine\Source\Runtime\WebBrowserTexture\Public;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MediaUtils\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MediaUtils\VNI;D:\UE_5.5\Engine\Source\Runtime\MediaUtils\Public;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Media\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Media\VNI;D:\UE_5.5\Engine\Source\Runtime\Media\Public;..\..\Plugins\OptimizedWebBrowser\Intermediate\Build\Win64\UnrealEditor\Inc\OptimizedWebBrowser\UHT;..\..\Plugins\OptimizedWebBrowser\Intermediate\Build\Win64\UnrealEditor\Inc\OptimizedWebBrowser\VNI;..\..\Plugins\OptimizedWebBrowser\Source;..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Public;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WebBrowser\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WebBrowser\VNI;D:\UE_5.5\Engine\Source\Runtime\WebBrowser\Public;D:\UE_5.5\Engine\Plugins\Runtime\WebBrowserWidget\Intermediate\Build\Win64\UnrealEditor\Inc\WebBrowserWidget\UHT;D:\UE_5.5\Engine\Plugins\Runtime\WebBrowserWidget\Intermediate\Build\Win64\UnrealEditor\Inc\WebBrowserWidget\VNI;D:\UE_5.5\Engine\Plugins\Runtime\WebBrowserWidget\Source;D:\UE_5.5\Engine\Plugins\Runtime\WebBrowserWidget\Source\WebBrowserWidget\Public;$(ProjectAdditionalIncludeDirectories_186);$(ProjectAdditionalIncludeDirectories_185);$(ProjectAdditionalIncludeDirectories_187)</ClCompile_AdditionalIncludeDirectories_3>
    <ClCompile_AdditionalIncludeDirectories_4>$(NMakeIncludeSearchPath);..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Private;$(ProjectAdditionalIncludeDirectories_106);$(ProjectAdditionalIncludeDirectories_108);$(ProjectAdditionalIncludeDirectories_107);$(ProjectAdditionalIncludeDirectories_101);$(ProjectAdditionalIncludeDirectories_121);$(ProjectAdditionalIncludeDirectories_147);$(ProjectAdditionalIncludeDirectories_148);$(ProjectAdditionalIncludeDirectories_146);$(ProjectAdditionalIncludeDirectories_128);$(ProjectAdditionalIncludeDirectories_174);$(ProjectAdditionalIncludeDirectories_102);$(ProjectAdditionalIncludeDirectories_130);$(ProjectAdditionalIncludeDirectories_131);$(ProjectAdditionalIncludeDirectories_88);$(ProjectAdditionalIncludeDirectories_164);$(ProjectAdditionalIncludeDirectories_175);$(ProjectAdditionalIncludeDirectories_129);$(ProjectAdditionalIncludeDirectories_141);$(ProjectAdditionalIncludeDirectories_140);$(ProjectAdditionalIncludeDirectories_168);$(ProjectAdditionalIncludeDirectories_167);$(ProjectAdditionalIncludeDirectories_154);$(ProjectAdditionalIncludeDirectories_85);$(ProjectAdditionalIncludeDirectories_86);$(ProjectAdditionalIncludeDirectories_176);$(ProjectAdditionalIncludeDirectories_90);$(ProjectAdditionalIncludeDirectories_89);$(ProjectAdditionalIncludeDirectories_103);$(ProjectAdditionalIncludeDirectories_104);$(ProjectAdditionalIncludeDirectories_105);$(ProjectAdditionalIncludeDirectories_178);$(ProjectAdditionalIncludeDirectories_122);$(ProjectAdditionalIncludeDirectories_123);$(ProjectAdditionalIncludeDirectories_155);$(ProjectAdditionalIncludeDirectories_156);$(ProjectAdditionalIncludeDirectories_157);$(ProjectAdditionalIncludeDirectories_97);$(ProjectAdditionalIncludeDirectories_138);$(ProjectAdditionalIncludeDirectories_177);$(ProjectAdditionalIncludeDirectories_173);$(ProjectAdditionalIncludeDirectories_87);$(ProjectAdditionalIncludeDirectories_159);$(ProjectAdditionalIncludeDirectories_158);$(ProjectAdditionalIncludeDirectories_165);$(ProjectAdditionalIncludeDirectories_150);$(ProjectAdditionalIncludeDirectories_160);$(ProjectAdditionalIncludeDirectories_110);$(ProjectAdditionalIncludeDirectories_113);$(ProjectAdditionalIncludeDirectories_120);$(ProjectAdditionalIncludeDirectories_126);$(ProjectAdditionalIncludeDirectories_112);$(ProjectAdditionalIncludeDirectories_172);$(ProjectAdditionalIncludeDirectories_91);$(ProjectAdditionalIncludeDirectories_94);$(ProjectAdditionalIncludeDirectories_95);$(ProjectAdditionalIncludeDirectories_96);$(ProjectAdditionalIncludeDirectories_34);$(ProjectAdditionalIncludeDirectories_36);$(ProjectAdditionalIncludeDirectories_17);$(ProjectAdditionalIncludeDirectories_16);$(ProjectAdditionalIncludeDirectories_93);$(ProjectAdditionalIncludeDirectories_92);$(ProjectAdditionalIncludeDirectories_100);$(ProjectAdditionalIncludeDirectories_151);$(ProjectAdditionalIncludeDirectories_35);$(ProjectAdditionalIncludeDirectories_23);$(ProjectAdditionalIncludeDirectories_99);$(ProjectAdditionalIncludeDirectories_119);$(ProjectAdditionalIncludeDirectories_142);$(ProjectAdditionalIncludeDirectories_170);$(ProjectAdditionalIncludeDirectories_169);$(ProjectAdditionalIncludeDirectories_9);$(ProjectAdditionalIncludeDirectories_181);$(ProjectAdditionalIncludeDirectories_180);$(ProjectAdditionalIncludeDirectories_182);$(ProjectAdditionalIncludeDirectories_10);$(ProjectAdditionalIncludeDirectories_43);$(ProjectAdditionalIncludeDirectories_64);$(ProjectAdditionalIncludeDirectories_66);$(ProjectAdditionalIncludeDirectories_65);$(ProjectAdditionalIncludeDirectories_69);$(ProjectAdditionalIncludeDirectories_72);$(ProjectAdditionalIncludeDirectories_11);$(ProjectAdditionalIncludeDirectories_38);$(ProjectAdditionalIncludeDirectories_37);$(ProjectAdditionalIncludeDirectories_45);$(ProjectAdditionalIncludeDirectories_42);$(ProjectAdditionalIncludeDirectories_70);$(ProjectAdditionalIncludeDirectories_58);$(ProjectAdditionalIncludeDirectories_59);$(ProjectAdditionalIncludeDirectories_61);$(ProjectAdditionalIncludeDirectories_132);$(ProjectAdditionalIncludeDirectories_84);$(ProjectAdditionalIncludeDirectories_80);$(ProjectAdditionalIncludeDirectories_81);$(ProjectAdditionalIncludeDirectories_47);$(ProjectAdditionalIncludeDirectories_15);$(ProjectAdditionalIncludeDirectories_55);$(ProjectAdditionalIncludeDirectories_13);$(ProjectAdditionalIncludeDirectories_12);$(ProjectAdditionalIncludeDirectories_46);$(ProjectAdditionalIncludeDirectories_26);$(ProjectAdditionalIncludeDirectories_54);$(ProjectAdditionalIncludeDirectories_162);$(ProjectAdditionalIncludeDirectories_161);$(ProjectAdditionalIncludeDirectories_30);$(ProjectAdditionalIncludeDirectories_28);$(ProjectAdditionalIncludeDirectories_29);$(ProjectAdditionalIncludeDirectories_166);$(ProjectAdditionalIncludeDirectories_25);$(ProjectAdditionalIncludeDirectories_63);$(ProjectAdditionalIncludeDirectories_124);$(ProjectAdditionalIncludeDirectories_125);$(ProjectAdditionalIncludeDirectories_52);$(ProjectAdditionalIncludeDirectories_20);$(ProjectAdditionalIncludeDirectories_57);$(ProjectAdditionalIncludeDirectories_68);$(ProjectAdditionalIncludeDirectories_171);$(ProjectAdditionalIncludeDirectories_33);$(ProjectAdditionalIncludeDirectories_39);$(ProjectAdditionalIncludeDirectories_78);$(ProjectAdditionalIncludeDirectories_79);$(ProjectAdditionalIncludeDirectories_50);$(ProjectAdditionalIncludeDirectories_51);$(ProjectAdditionalIncludeDirectories_153);$(ProjectAdditionalIncludeDirectories_152);$(ProjectAdditionalIncludeDirectories_21);$(ProjectAdditionalIncludeDirectories_22);$(ProjectAdditionalIncludeDirectories_14);$(ProjectAdditionalIncludeDirectories_98);$(ProjectAdditionalIncludeDirectories_24);$(ProjectAdditionalIncludeDirectories_48);$(ProjectAdditionalIncludeDirectories_49);$(ProjectAdditionalIncludeDirectories_188);$(ProjectAdditionalIncludeDirectories_67);$(ProjectAdditionalIncludeDirectories_53);$(ProjectAdditionalIncludeDirectories_32);$(ProjectAdditionalIncludeDirectories_82);$(ProjectAdditionalIncludeDirectories_83);$(ProjectAdditionalIncludeDirectories_127);$(ProjectAdditionalIncludeDirectories_135);$(ProjectAdditionalIncludeDirectories_136);$(ProjectAdditionalIncludeDirectories_56);$(ProjectAdditionalIncludeDirectories_62);$(ProjectAdditionalIncludeDirectories_74);$(ProjectAdditionalIncludeDirectories_149);$(ProjectAdditionalIncludeDirectories_183);$(ProjectAdditionalIncludeDirectories_144);$(ProjectAdditionalIncludeDirectories_179);$(ProjectAdditionalIncludeDirectories_184);$(ProjectAdditionalIncludeDirectories_143);$(ProjectAdditionalIncludeDirectories_109);$(ProjectAdditionalIncludeDirectories_163);$(ProjectAdditionalIncludeDirectories_145);$(ProjectAdditionalIncludeDirectories_118);$(ProjectAdditionalIncludeDirectories_117);$(ProjectAdditionalIncludeDirectories_111);$(ProjectAdditionalIncludeDirectories_114);$(ProjectAdditionalIncludeDirectories_115);$(ProjectAdditionalIncludeDirectories_116);$(ProjectAdditionalIncludeDirectories_27);$(ProjectAdditionalIncludeDirectories_139);$(ProjectAdditionalIncludeDirectories_137);$(ProjectAdditionalIncludeDirectories_77);$(ProjectAdditionalIncludeDirectories_73);$(ProjectAdditionalIncludeDirectories_133);$(ProjectAdditionalIncludeDirectories_134);$(ProjectAdditionalIncludeDirectories_18);$(ProjectAdditionalIncludeDirectories_19);$(ProjectAdditionalIncludeDirectories_75);$(ProjectAdditionalIncludeDirectories_76);$(ProjectAdditionalIncludeDirectories_31);$(ProjectAdditionalIncludeDirectories_40);$(ProjectAdditionalIncludeDirectories_41);$(ProjectAdditionalIncludeDirectories_71);$(ProjectAdditionalIncludeDirectories_44);..\..\Plugins\PlatformFunctionsPlugin_5.4\Intermediate\Build\Win64\UnrealEditor\Inc\PlatformFunctions\UHT;..\..\Plugins\PlatformFunctionsPlugin_5.4\Intermediate\Build\Win64\UnrealEditor\Inc\PlatformFunctions\VNI;..\..\Plugins\PlatformFunctionsPlugin_5.4\Source;..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Public;$(ProjectAdditionalIncludeDirectories_186);$(ProjectAdditionalIncludeDirectories_185);$(ProjectAdditionalIncludeDirectories_187)</ClCompile_AdditionalIncludeDirectories_4>
    <ClCompile_AdditionalIncludeDirectories_5>$(NMakeIncludeSearchPath);..\..\Plugins\rdBPtools\Source\rdBPtools\Private;$(ProjectAdditionalIncludeDirectories_162);$(ProjectAdditionalIncludeDirectories_161);$(ProjectAdditionalIncludeDirectories_130);$(ProjectAdditionalIncludeDirectories_131);$(ProjectAdditionalIncludeDirectories_174);$(ProjectAdditionalIncludeDirectories_102);$(ProjectAdditionalIncludeDirectories_88);$(ProjectAdditionalIncludeDirectories_164);$(ProjectAdditionalIncludeDirectories_175);$(ProjectAdditionalIncludeDirectories_129);$(ProjectAdditionalIncludeDirectories_128);$(ProjectAdditionalIncludeDirectories_84);$(ProjectAdditionalIncludeDirectories_80);$(ProjectAdditionalIncludeDirectories_81);$(ProjectAdditionalIncludeDirectories_46);$(ProjectAdditionalIncludeDirectories_90);$(ProjectAdditionalIncludeDirectories_89);$(ProjectAdditionalIncludeDirectories_106);$(ProjectAdditionalIncludeDirectories_108);$(ProjectAdditionalIncludeDirectories_107);$(ProjectAdditionalIncludeDirectories_101);$(ProjectAdditionalIncludeDirectories_121);$(ProjectAdditionalIncludeDirectories_147);$(ProjectAdditionalIncludeDirectories_148);$(ProjectAdditionalIncludeDirectories_146);$(ProjectAdditionalIncludeDirectories_141);$(ProjectAdditionalIncludeDirectories_140);$(ProjectAdditionalIncludeDirectories_168);$(ProjectAdditionalIncludeDirectories_167);$(ProjectAdditionalIncludeDirectories_154);$(ProjectAdditionalIncludeDirectories_85);$(ProjectAdditionalIncludeDirectories_86);$(ProjectAdditionalIncludeDirectories_176);$(ProjectAdditionalIncludeDirectories_103);$(ProjectAdditionalIncludeDirectories_104);$(ProjectAdditionalIncludeDirectories_105);$(ProjectAdditionalIncludeDirectories_178);$(ProjectAdditionalIncludeDirectories_122);$(ProjectAdditionalIncludeDirectories_123);$(ProjectAdditionalIncludeDirectories_155);$(ProjectAdditionalIncludeDirectories_156);$(ProjectAdditionalIncludeDirectories_157);$(ProjectAdditionalIncludeDirectories_97);$(ProjectAdditionalIncludeDirectories_138);$(ProjectAdditionalIncludeDirectories_177);$(ProjectAdditionalIncludeDirectories_173);$(ProjectAdditionalIncludeDirectories_87);$(ProjectAdditionalIncludeDirectories_159);$(ProjectAdditionalIncludeDirectories_158);$(ProjectAdditionalIncludeDirectories_165);$(ProjectAdditionalIncludeDirectories_150);$(ProjectAdditionalIncludeDirectories_160);$(ProjectAdditionalIncludeDirectories_110);$(ProjectAdditionalIncludeDirectories_113);$(ProjectAdditionalIncludeDirectories_120);$(ProjectAdditionalIncludeDirectories_126);$(ProjectAdditionalIncludeDirectories_112);$(ProjectAdditionalIncludeDirectories_172);$(ProjectAdditionalIncludeDirectories_91);$(ProjectAdditionalIncludeDirectories_94);$(ProjectAdditionalIncludeDirectories_95);$(ProjectAdditionalIncludeDirectories_96);$(ProjectAdditionalIncludeDirectories_34);$(ProjectAdditionalIncludeDirectories_36);$(ProjectAdditionalIncludeDirectories_17);$(ProjectAdditionalIncludeDirectories_16);$(ProjectAdditionalIncludeDirectories_93);$(ProjectAdditionalIncludeDirectories_92);$(ProjectAdditionalIncludeDirectories_100);$(ProjectAdditionalIncludeDirectories_151);$(ProjectAdditionalIncludeDirectories_35);$(ProjectAdditionalIncludeDirectories_23);$(ProjectAdditionalIncludeDirectories_99);$(ProjectAdditionalIncludeDirectories_119);$(ProjectAdditionalIncludeDirectories_142);$(ProjectAdditionalIncludeDirectories_170);$(ProjectAdditionalIncludeDirectories_169);$(ProjectAdditionalIncludeDirectories_9);$(ProjectAdditionalIncludeDirectories_181);$(ProjectAdditionalIncludeDirectories_180);$(ProjectAdditionalIncludeDirectories_182);$(ProjectAdditionalIncludeDirectories_10);$(ProjectAdditionalIncludeDirectories_43);$(ProjectAdditionalIncludeDirectories_64);$(ProjectAdditionalIncludeDirectories_66);$(ProjectAdditionalIncludeDirectories_65);$(ProjectAdditionalIncludeDirectories_69);$(ProjectAdditionalIncludeDirectories_72);$(ProjectAdditionalIncludeDirectories_11);$(ProjectAdditionalIncludeDirectories_38);$(ProjectAdditionalIncludeDirectories_37);$(ProjectAdditionalIncludeDirectories_45);$(ProjectAdditionalIncludeDirectories_42);$(ProjectAdditionalIncludeDirectories_70);$(ProjectAdditionalIncludeDirectories_58);$(ProjectAdditionalIncludeDirectories_59);$(ProjectAdditionalIncludeDirectories_61);$(ProjectAdditionalIncludeDirectories_132);$(ProjectAdditionalIncludeDirectories_41);$(ProjectAdditionalIncludeDirectories_71);$(ProjectAdditionalIncludeDirectories_44);$(ProjectAdditionalIncludeDirectories_133);$(ProjectAdditionalIncludeDirectories_20);$(ProjectAdditionalIncludeDirectories_57);$(ProjectAdditionalIncludeDirectories_68);$(ProjectAdditionalIncludeDirectories_171);$(ProjectAdditionalIncludeDirectories_33);$(ProjectAdditionalIncludeDirectories_39);$(ProjectAdditionalIncludeDirectories_78);$(ProjectAdditionalIncludeDirectories_79);$(ProjectAdditionalIncludeDirectories_50);$(ProjectAdditionalIncludeDirectories_51);$(ProjectAdditionalIncludeDirectories_153);$(ProjectAdditionalIncludeDirectories_152);$(ProjectAdditionalIncludeDirectories_21);$(ProjectAdditionalIncludeDirectories_22);$(ProjectAdditionalIncludeDirectories_14);$(ProjectAdditionalIncludeDirectories_98);$(ProjectAdditionalIncludeDirectories_24);$(ProjectAdditionalIncludeDirectories_48);$(ProjectAdditionalIncludeDirectories_49);$(ProjectAdditionalIncludeDirectories_67);$(ProjectAdditionalIncludeDirectories_53);$(ProjectAdditionalIncludeDirectories_32);$(ProjectAdditionalIncludeDirectories_82);$(ProjectAdditionalIncludeDirectories_83);$(ProjectAdditionalIncludeDirectories_127);$(ProjectAdditionalIncludeDirectories_135);$(ProjectAdditionalIncludeDirectories_136);$(ProjectAdditionalIncludeDirectories_56);$(ProjectAdditionalIncludeDirectories_52);$(ProjectAdditionalIncludeDirectories_62);$(ProjectAdditionalIncludeDirectories_74);$(ProjectAdditionalIncludeDirectories_55);$(ProjectAdditionalIncludeDirectories_13);$(ProjectAdditionalIncludeDirectories_12);$(ProjectAdditionalIncludeDirectories_26);$(ProjectAdditionalIncludeDirectories_15);$(ProjectAdditionalIncludeDirectories_54);$(ProjectAdditionalIncludeDirectories_188);$(ProjectAdditionalIncludeDirectories_149);$(ProjectAdditionalIncludeDirectories_183);$(ProjectAdditionalIncludeDirectories_144);$(ProjectAdditionalIncludeDirectories_179);$(ProjectAdditionalIncludeDirectories_184);$(ProjectAdditionalIncludeDirectories_143);$(ProjectAdditionalIncludeDirectories_109);$(ProjectAdditionalIncludeDirectories_163);$(ProjectAdditionalIncludeDirectories_145);$(ProjectAdditionalIncludeDirectories_118);$(ProjectAdditionalIncludeDirectories_117);$(ProjectAdditionalIncludeDirectories_111);$(ProjectAdditionalIncludeDirectories_114);$(ProjectAdditionalIncludeDirectories_115);$(ProjectAdditionalIncludeDirectories_116);$(ProjectAdditionalIncludeDirectories_27);$(ProjectAdditionalIncludeDirectories_139);$(ProjectAdditionalIncludeDirectories_137);$(ProjectAdditionalIncludeDirectories_77);$(ProjectAdditionalIncludeDirectories_73);$(ProjectAdditionalIncludeDirectories_134);$(ProjectAdditionalIncludeDirectories_18);$(ProjectAdditionalIncludeDirectories_19);$(ProjectAdditionalIncludeDirectories_75);$(ProjectAdditionalIncludeDirectories_76);$(ProjectAdditionalIncludeDirectories_31);$(ProjectAdditionalIncludeDirectories_40);$(ProjectAdditionalIncludeDirectories_124);$(ProjectAdditionalIncludeDirectories_125);$(ProjectAdditionalIncludeDirectories_47);$(ProjectAdditionalIncludeDirectories_30);$(ProjectAdditionalIncludeDirectories_28);$(ProjectAdditionalIncludeDirectories_29);$(ProjectAdditionalIncludeDirectories_166);$(ProjectAdditionalIncludeDirectories_25);$(ProjectAdditionalIncludeDirectories_63);D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SceneOutliner\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SceneOutliner\VNI;D:\UE_5.5\Engine\Source\Editor\SceneOutliner\Public;$(ProjectAdditionalIncludeDirectories_7);$(ProjectAdditionalIncludeDirectories_8);$(ProjectAdditionalIncludeDirectories_60);D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelInstanceEditor\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelInstanceEditor\VNI;D:\UE_5.5\Engine\Source\Editor\LevelInstanceEditor\Public;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Foliage\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Foliage\VNI;D:\UE_5.5\Engine\Source\Runtime\Foliage\Public;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FoliageEdit\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FoliageEdit\VNI;D:\UE_5.5\Engine\Source\Editor\FoliageEdit\Public;D:\UE_5.5\Engine\Plugins\Enterprise\DatasmithContent\Intermediate\Build\Win64\UnrealEditor\Inc\DatasmithContent\UHT;D:\UE_5.5\Engine\Plugins\Enterprise\DatasmithContent\Intermediate\Build\Win64\UnrealEditor\Inc\DatasmithContent\VNI;D:\UE_5.5\Engine\Plugins\Enterprise\DatasmithContent\Source;D:\UE_5.5\Engine\Plugins\Enterprise\DatasmithContent\Source\DatasmithContent\Public;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CinematicCamera\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CinematicCamera\VNI;D:\UE_5.5\Engine\Source\Runtime\CinematicCamera\Public;..\..\Plugins\rdBPtools\Intermediate\Build\Win64\UnrealEditor\Inc\rdBPtools\UHT;..\..\Plugins\rdBPtools\Intermediate\Build\Win64\UnrealEditor\Inc\rdBPtools\VNI;..\..\Plugins\rdBPtools\Source;..\..\Plugins\rdBPtools\Source\rdBPtools\Public;$(ProjectAdditionalIncludeDirectories_186);$(ProjectAdditionalIncludeDirectories_185);$(ProjectAdditionalIncludeDirectories_187)</ClCompile_AdditionalIncludeDirectories_5>
    <ClCompile_AdditionalIncludeDirectories_6>$(NMakeIncludeSearchPath);..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\Helpers;..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\Widgets;..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private;$(ProjectAdditionalIncludeDirectories_106);$(ProjectAdditionalIncludeDirectories_108);$(ProjectAdditionalIncludeDirectories_107);$(ProjectAdditionalIncludeDirectories_101);$(ProjectAdditionalIncludeDirectories_121);$(ProjectAdditionalIncludeDirectories_147);$(ProjectAdditionalIncludeDirectories_148);$(ProjectAdditionalIncludeDirectories_146);$(ProjectAdditionalIncludeDirectories_128);$(ProjectAdditionalIncludeDirectories_174);$(ProjectAdditionalIncludeDirectories_102);$(ProjectAdditionalIncludeDirectories_130);$(ProjectAdditionalIncludeDirectories_131);$(ProjectAdditionalIncludeDirectories_88);$(ProjectAdditionalIncludeDirectories_164);$(ProjectAdditionalIncludeDirectories_175);$(ProjectAdditionalIncludeDirectories_129);$(ProjectAdditionalIncludeDirectories_141);$(ProjectAdditionalIncludeDirectories_140);$(ProjectAdditionalIncludeDirectories_168);$(ProjectAdditionalIncludeDirectories_167);$(ProjectAdditionalIncludeDirectories_154);$(ProjectAdditionalIncludeDirectories_85);$(ProjectAdditionalIncludeDirectories_86);$(ProjectAdditionalIncludeDirectories_176);$(ProjectAdditionalIncludeDirectories_90);$(ProjectAdditionalIncludeDirectories_89);$(ProjectAdditionalIncludeDirectories_103);$(ProjectAdditionalIncludeDirectories_104);$(ProjectAdditionalIncludeDirectories_105);$(ProjectAdditionalIncludeDirectories_178);$(ProjectAdditionalIncludeDirectories_122);$(ProjectAdditionalIncludeDirectories_123);$(ProjectAdditionalIncludeDirectories_155);$(ProjectAdditionalIncludeDirectories_156);$(ProjectAdditionalIncludeDirectories_157);$(ProjectAdditionalIncludeDirectories_97);$(ProjectAdditionalIncludeDirectories_138);$(ProjectAdditionalIncludeDirectories_177);$(ProjectAdditionalIncludeDirectories_173);$(ProjectAdditionalIncludeDirectories_87);$(ProjectAdditionalIncludeDirectories_159);$(ProjectAdditionalIncludeDirectories_158);$(ProjectAdditionalIncludeDirectories_165);$(ProjectAdditionalIncludeDirectories_150);$(ProjectAdditionalIncludeDirectories_160);$(ProjectAdditionalIncludeDirectories_110);$(ProjectAdditionalIncludeDirectories_113);$(ProjectAdditionalIncludeDirectories_120);$(ProjectAdditionalIncludeDirectories_126);$(ProjectAdditionalIncludeDirectories_112);$(ProjectAdditionalIncludeDirectories_172);$(ProjectAdditionalIncludeDirectories_91);$(ProjectAdditionalIncludeDirectories_94);$(ProjectAdditionalIncludeDirectories_95);$(ProjectAdditionalIncludeDirectories_96);$(ProjectAdditionalIncludeDirectories_34);$(ProjectAdditionalIncludeDirectories_36);$(ProjectAdditionalIncludeDirectories_17);$(ProjectAdditionalIncludeDirectories_16);$(ProjectAdditionalIncludeDirectories_93);$(ProjectAdditionalIncludeDirectories_92);$(ProjectAdditionalIncludeDirectories_100);$(ProjectAdditionalIncludeDirectories_151);$(ProjectAdditionalIncludeDirectories_35);$(ProjectAdditionalIncludeDirectories_23);$(ProjectAdditionalIncludeDirectories_99);$(ProjectAdditionalIncludeDirectories_119);$(ProjectAdditionalIncludeDirectories_142);$(ProjectAdditionalIncludeDirectories_170);$(ProjectAdditionalIncludeDirectories_169);$(ProjectAdditionalIncludeDirectories_9);$(ProjectAdditionalIncludeDirectories_181);$(ProjectAdditionalIncludeDirectories_180);$(ProjectAdditionalIncludeDirectories_182);$(ProjectAdditionalIncludeDirectories_10);$(ProjectAdditionalIncludeDirectories_43);$(ProjectAdditionalIncludeDirectories_64);$(ProjectAdditionalIncludeDirectories_66);$(ProjectAdditionalIncludeDirectories_65);$(ProjectAdditionalIncludeDirectories_69);$(ProjectAdditionalIncludeDirectories_72);$(ProjectAdditionalIncludeDirectories_11);$(ProjectAdditionalIncludeDirectories_38);$(ProjectAdditionalIncludeDirectories_37);$(ProjectAdditionalIncludeDirectories_45);$(ProjectAdditionalIncludeDirectories_42);$(ProjectAdditionalIncludeDirectories_70);$(ProjectAdditionalIncludeDirectories_58);$(ProjectAdditionalIncludeDirectories_59);$(ProjectAdditionalIncludeDirectories_61);$(ProjectAdditionalIncludeDirectories_132);$(ProjectAdditionalIncludeDirectories_84);$(ProjectAdditionalIncludeDirectories_80);$(ProjectAdditionalIncludeDirectories_81);$(ProjectAdditionalIncludeDirectories_47);$(ProjectAdditionalIncludeDirectories_15);$(ProjectAdditionalIncludeDirectories_55);$(ProjectAdditionalIncludeDirectories_13);$(ProjectAdditionalIncludeDirectories_12);$(ProjectAdditionalIncludeDirectories_46);$(ProjectAdditionalIncludeDirectories_26);$(ProjectAdditionalIncludeDirectories_54);$(ProjectAdditionalIncludeDirectories_162);$(ProjectAdditionalIncludeDirectories_161);$(ProjectAdditionalIncludeDirectories_30);$(ProjectAdditionalIncludeDirectories_28);$(ProjectAdditionalIncludeDirectories_29);$(ProjectAdditionalIncludeDirectories_166);$(ProjectAdditionalIncludeDirectories_25);$(ProjectAdditionalIncludeDirectories_63);$(ProjectAdditionalIncludeDirectories_124);$(ProjectAdditionalIncludeDirectories_125);$(ProjectAdditionalIncludeDirectories_52);$(ProjectAdditionalIncludeDirectories_20);$(ProjectAdditionalIncludeDirectories_57);$(ProjectAdditionalIncludeDirectories_68);$(ProjectAdditionalIncludeDirectories_171);$(ProjectAdditionalIncludeDirectories_33);$(ProjectAdditionalIncludeDirectories_39);$(ProjectAdditionalIncludeDirectories_78);$(ProjectAdditionalIncludeDirectories_79);$(ProjectAdditionalIncludeDirectories_50);$(ProjectAdditionalIncludeDirectories_51);$(ProjectAdditionalIncludeDirectories_153);$(ProjectAdditionalIncludeDirectories_152);$(ProjectAdditionalIncludeDirectories_21);$(ProjectAdditionalIncludeDirectories_22);$(ProjectAdditionalIncludeDirectories_14);$(ProjectAdditionalIncludeDirectories_98);$(ProjectAdditionalIncludeDirectories_24);$(ProjectAdditionalIncludeDirectories_48);$(ProjectAdditionalIncludeDirectories_49);$(ProjectAdditionalIncludeDirectories_188);$(ProjectAdditionalIncludeDirectories_67);$(ProjectAdditionalIncludeDirectories_53);$(ProjectAdditionalIncludeDirectories_32);$(ProjectAdditionalIncludeDirectories_82);$(ProjectAdditionalIncludeDirectories_83);$(ProjectAdditionalIncludeDirectories_127);$(ProjectAdditionalIncludeDirectories_135);$(ProjectAdditionalIncludeDirectories_136);$(ProjectAdditionalIncludeDirectories_56);$(ProjectAdditionalIncludeDirectories_62);$(ProjectAdditionalIncludeDirectories_74);$(ProjectAdditionalIncludeDirectories_149);$(ProjectAdditionalIncludeDirectories_183);$(ProjectAdditionalIncludeDirectories_144);$(ProjectAdditionalIncludeDirectories_179);$(ProjectAdditionalIncludeDirectories_184);$(ProjectAdditionalIncludeDirectories_143);$(ProjectAdditionalIncludeDirectories_109);$(ProjectAdditionalIncludeDirectories_163);$(ProjectAdditionalIncludeDirectories_145);$(ProjectAdditionalIncludeDirectories_118);$(ProjectAdditionalIncludeDirectories_117);$(ProjectAdditionalIncludeDirectories_111);$(ProjectAdditionalIncludeDirectories_114);$(ProjectAdditionalIncludeDirectories_115);$(ProjectAdditionalIncludeDirectories_116);$(ProjectAdditionalIncludeDirectories_27);$(ProjectAdditionalIncludeDirectories_139);$(ProjectAdditionalIncludeDirectories_137);$(ProjectAdditionalIncludeDirectories_77);$(ProjectAdditionalIncludeDirectories_73);$(ProjectAdditionalIncludeDirectories_133);$(ProjectAdditionalIncludeDirectories_134);$(ProjectAdditionalIncludeDirectories_18);$(ProjectAdditionalIncludeDirectories_19);$(ProjectAdditionalIncludeDirectories_75);$(ProjectAdditionalIncludeDirectories_76);$(ProjectAdditionalIncludeDirectories_31);$(ProjectAdditionalIncludeDirectories_40);$(ProjectAdditionalIncludeDirectories_41);$(ProjectAdditionalIncludeDirectories_71);$(ProjectAdditionalIncludeDirectories_44);$(ProjectAdditionalIncludeDirectories_7);$(ProjectAdditionalIncludeDirectories_8);$(ProjectAdditionalIncludeDirectories_60);..\..\Plugins\SnappingHelper\Intermediate\Build\Win64\UnrealEditor\Inc\SnappingHelper\UHT;..\..\Plugins\SnappingHelper\Intermediate\Build\Win64\UnrealEditor\Inc\SnappingHelper\VNI;..\..\Plugins\SnappingHelper\Source;..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public;$(ProjectAdditionalIncludeDirectories_186);$(ProjectAdditionalIncludeDirectories_185);$(ProjectAdditionalIncludeDirectories_187)</ClCompile_AdditionalIncludeDirectories_6>
    <ClCompile_AdditionalIncludeDirectories_7>$(NMakeIncludeSearchPath);$(ProjectAdditionalIncludeDirectories_106);$(ProjectAdditionalIncludeDirectories_108);$(ProjectAdditionalIncludeDirectories_107);$(ProjectAdditionalIncludeDirectories_101);$(ProjectAdditionalIncludeDirectories_121);$(ProjectAdditionalIncludeDirectories_147);$(ProjectAdditionalIncludeDirectories_148);$(ProjectAdditionalIncludeDirectories_146);$(ProjectAdditionalIncludeDirectories_128);$(ProjectAdditionalIncludeDirectories_174);$(ProjectAdditionalIncludeDirectories_102);$(ProjectAdditionalIncludeDirectories_130);$(ProjectAdditionalIncludeDirectories_131);$(ProjectAdditionalIncludeDirectories_88);$(ProjectAdditionalIncludeDirectories_164);$(ProjectAdditionalIncludeDirectories_175);$(ProjectAdditionalIncludeDirectories_129);$(ProjectAdditionalIncludeDirectories_141);$(ProjectAdditionalIncludeDirectories_140);$(ProjectAdditionalIncludeDirectories_168);$(ProjectAdditionalIncludeDirectories_167);$(ProjectAdditionalIncludeDirectories_154);$(ProjectAdditionalIncludeDirectories_85);$(ProjectAdditionalIncludeDirectories_86);$(ProjectAdditionalIncludeDirectories_176);$(ProjectAdditionalIncludeDirectories_90);$(ProjectAdditionalIncludeDirectories_89);$(ProjectAdditionalIncludeDirectories_103);$(ProjectAdditionalIncludeDirectories_104);$(ProjectAdditionalIncludeDirectories_105);$(ProjectAdditionalIncludeDirectories_178);$(ProjectAdditionalIncludeDirectories_122);$(ProjectAdditionalIncludeDirectories_123);$(ProjectAdditionalIncludeDirectories_155);$(ProjectAdditionalIncludeDirectories_156);$(ProjectAdditionalIncludeDirectories_157);$(ProjectAdditionalIncludeDirectories_97);$(ProjectAdditionalIncludeDirectories_138);$(ProjectAdditionalIncludeDirectories_177);$(ProjectAdditionalIncludeDirectories_173);$(ProjectAdditionalIncludeDirectories_87);$(ProjectAdditionalIncludeDirectories_159);$(ProjectAdditionalIncludeDirectories_158);$(ProjectAdditionalIncludeDirectories_165);$(ProjectAdditionalIncludeDirectories_150);$(ProjectAdditionalIncludeDirectories_160);$(ProjectAdditionalIncludeDirectories_110);$(ProjectAdditionalIncludeDirectories_113);$(ProjectAdditionalIncludeDirectories_120);$(ProjectAdditionalIncludeDirectories_126);$(ProjectAdditionalIncludeDirectories_112);$(ProjectAdditionalIncludeDirectories_172);$(ProjectAdditionalIncludeDirectories_91);$(ProjectAdditionalIncludeDirectories_94);$(ProjectAdditionalIncludeDirectories_95);$(ProjectAdditionalIncludeDirectories_96);$(ProjectAdditionalIncludeDirectories_34);$(ProjectAdditionalIncludeDirectories_36);$(ProjectAdditionalIncludeDirectories_17);$(ProjectAdditionalIncludeDirectories_16);$(ProjectAdditionalIncludeDirectories_93);$(ProjectAdditionalIncludeDirectories_92);$(ProjectAdditionalIncludeDirectories_100);$(ProjectAdditionalIncludeDirectories_151);$(ProjectAdditionalIncludeDirectories_35);$(ProjectAdditionalIncludeDirectories_23);$(ProjectAdditionalIncludeDirectories_99);$(ProjectAdditionalIncludeDirectories_119);$(ProjectAdditionalIncludeDirectories_142);$(ProjectAdditionalIncludeDirectories_170);$(ProjectAdditionalIncludeDirectories_169);$(ProjectAdditionalIncludeDirectories_9);$(ProjectAdditionalIncludeDirectories_181);$(ProjectAdditionalIncludeDirectories_180);$(ProjectAdditionalIncludeDirectories_182);$(ProjectAdditionalIncludeDirectories_10);$(ProjectAdditionalIncludeDirectories_43);$(ProjectAdditionalIncludeDirectories_64);$(ProjectAdditionalIncludeDirectories_66);$(ProjectAdditionalIncludeDirectories_65);$(ProjectAdditionalIncludeDirectories_69);$(ProjectAdditionalIncludeDirectories_72);$(ProjectAdditionalIncludeDirectories_11);$(ProjectAdditionalIncludeDirectories_38);$(ProjectAdditionalIncludeDirectories_37);$(ProjectAdditionalIncludeDirectories_45);$(ProjectAdditionalIncludeDirectories_42);$(ProjectAdditionalIncludeDirectories_70);$(ProjectAdditionalIncludeDirectories_58);$(ProjectAdditionalIncludeDirectories_59);$(ProjectAdditionalIncludeDirectories_61);$(ProjectAdditionalIncludeDirectories_132);$(ProjectAdditionalIncludeDirectories_84);$(ProjectAdditionalIncludeDirectories_80);$(ProjectAdditionalIncludeDirectories_81);$(ProjectAdditionalIncludeDirectories_47);$(ProjectAdditionalIncludeDirectories_15);$(ProjectAdditionalIncludeDirectories_55);$(ProjectAdditionalIncludeDirectories_13);$(ProjectAdditionalIncludeDirectories_12);$(ProjectAdditionalIncludeDirectories_46);$(ProjectAdditionalIncludeDirectories_26);$(ProjectAdditionalIncludeDirectories_54);$(ProjectAdditionalIncludeDirectories_162);$(ProjectAdditionalIncludeDirectories_161);$(ProjectAdditionalIncludeDirectories_30);$(ProjectAdditionalIncludeDirectories_28);$(ProjectAdditionalIncludeDirectories_29);$(ProjectAdditionalIncludeDirectories_166);$(ProjectAdditionalIncludeDirectories_25);$(ProjectAdditionalIncludeDirectories_63);$(ProjectAdditionalIncludeDirectories_124);$(ProjectAdditionalIncludeDirectories_125);$(ProjectAdditionalIncludeDirectories_52);$(ProjectAdditionalIncludeDirectories_20);$(ProjectAdditionalIncludeDirectories_57);$(ProjectAdditionalIncludeDirectories_68);$(ProjectAdditionalIncludeDirectories_171);$(ProjectAdditionalIncludeDirectories_33);$(ProjectAdditionalIncludeDirectories_39);$(ProjectAdditionalIncludeDirectories_78);$(ProjectAdditionalIncludeDirectories_79);$(ProjectAdditionalIncludeDirectories_50);$(ProjectAdditionalIncludeDirectories_51);$(ProjectAdditionalIncludeDirectories_153);$(ProjectAdditionalIncludeDirectories_152);$(ProjectAdditionalIncludeDirectories_21);$(ProjectAdditionalIncludeDirectories_22);$(ProjectAdditionalIncludeDirectories_14);$(ProjectAdditionalIncludeDirectories_98);$(ProjectAdditionalIncludeDirectories_24);$(ProjectAdditionalIncludeDirectories_48);$(ProjectAdditionalIncludeDirectories_49);$(ProjectAdditionalIncludeDirectories_188);$(ProjectAdditionalIncludeDirectories_67);$(ProjectAdditionalIncludeDirectories_53);$(ProjectAdditionalIncludeDirectories_32);$(ProjectAdditionalIncludeDirectories_82);$(ProjectAdditionalIncludeDirectories_83);$(ProjectAdditionalIncludeDirectories_127);$(ProjectAdditionalIncludeDirectories_135);$(ProjectAdditionalIncludeDirectories_136);$(ProjectAdditionalIncludeDirectories_56);$(ProjectAdditionalIncludeDirectories_62);$(ProjectAdditionalIncludeDirectories_74);$(ProjectAdditionalIncludeDirectories_149);$(ProjectAdditionalIncludeDirectories_183);$(ProjectAdditionalIncludeDirectories_144);$(ProjectAdditionalIncludeDirectories_179);$(ProjectAdditionalIncludeDirectories_184);$(ProjectAdditionalIncludeDirectories_143);$(ProjectAdditionalIncludeDirectories_109);$(ProjectAdditionalIncludeDirectories_163);$(ProjectAdditionalIncludeDirectories_145);$(ProjectAdditionalIncludeDirectories_118);$(ProjectAdditionalIncludeDirectories_117);$(ProjectAdditionalIncludeDirectories_111);$(ProjectAdditionalIncludeDirectories_114);$(ProjectAdditionalIncludeDirectories_115);$(ProjectAdditionalIncludeDirectories_116);$(ProjectAdditionalIncludeDirectories_27);$(ProjectAdditionalIncludeDirectories_139);$(ProjectAdditionalIncludeDirectories_137);$(ProjectAdditionalIncludeDirectories_77);$(ProjectAdditionalIncludeDirectories_73);$(ProjectAdditionalIncludeDirectories_133);$(ProjectAdditionalIncludeDirectories_134);$(ProjectAdditionalIncludeDirectories_18);$(ProjectAdditionalIncludeDirectories_19);$(ProjectAdditionalIncludeDirectories_75);$(ProjectAdditionalIncludeDirectories_76);$(ProjectAdditionalIncludeDirectories_31);$(ProjectAdditionalIncludeDirectories_40);$(ProjectAdditionalIncludeDirectories_41);$(ProjectAdditionalIncludeDirectories_71);$(ProjectAdditionalIncludeDirectories_44);D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AIModule\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AIModule\VNI;D:\UE_5.5\Engine\Source\Runtime\AIModule\Classes;D:\UE_5.5\Engine\Source\Runtime\AIModule\Public;..\Build\Win64\UnrealEditor\Inc\Baoli\UHT;..\Build\Win64\UnrealEditor\Inc\Baoli\VNI;..\..\Source;D:\UE_5.5\Engine\Plugins\EnhancedInput\Intermediate\Build\Win64\UnrealEditor\Inc\EnhancedInput\UHT;D:\UE_5.5\Engine\Plugins\EnhancedInput\Intermediate\Build\Win64\UnrealEditor\Inc\EnhancedInput\VNI;D:\UE_5.5\Engine\Plugins\EnhancedInput\Source;D:\UE_5.5\Engine\Plugins\EnhancedInput\Source\EnhancedInput\Public;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraphRuntime\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraphRuntime\VNI;D:\UE_5.5\Engine\Source\Runtime\AnimGraphRuntime\Public;D:\UE_5.5\Engine\Plugins\Animation\MotionWarping\Intermediate\Build\Win64\UnrealEditor\Inc\MotionWarping\UHT;D:\UE_5.5\Engine\Plugins\Animation\MotionWarping\Intermediate\Build\Win64\UnrealEditor\Inc\MotionWarping\VNI;D:\UE_5.5\Engine\Plugins\Animation\MotionWarping\Source;D:\UE_5.5\Engine\Plugins\Animation\MotionWarping\Source\MotionWarping\Public;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraph\UHT;D:\UE_5.5\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraph\VNI;D:\UE_5.5\Engine\Source\Editor\AnimGraph\Public;D:\UE_5.5\Engine\Source\Editor\AnimGraph\Internal;$(ProjectAdditionalIncludeDirectories_186);$(ProjectAdditionalIncludeDirectories_185);$(ProjectAdditionalIncludeDirectories_187)</ClCompile_AdditionalIncludeDirectories_7>
    <ProjectForcedIncludeFiles>$(SolutionDir)Intermediate\Build\Win64\x64\BaoliEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h</ProjectForcedIncludeFiles>
    <ClCompile_ForcedIncludeFiles>$(SolutionDir)Intermediate\Build\Win64\x64\BaoliEditor\Development\CoreUObject\SharedPCH.CoreUObject.Project.ValApi.Cpp20.InclOrderUnreal5_3.h;$(SolutionDir)Plugins\Inkpot\Intermediate\Build\Win64\x64\UnrealEditor\Development\InkPlusPlus\Definitions.InkPlusPlus.h</ClCompile_ForcedIncludeFiles>
    <ClCompile_ForcedIncludeFiles_1>$(ProjectForcedIncludeFiles);$(SolutionDir)Plugins\Inkpot\Intermediate\Build\Win64\x64\UnrealEditor\Development\InkpotEditor\Definitions.InkpotEditor.h</ClCompile_ForcedIncludeFiles_1>
    <ClCompile_ForcedIncludeFiles_2>$(ProjectForcedIncludeFiles);$(SolutionDir)Plugins\Inkpot\Intermediate\Build\Win64\x64\UnrealEditor\Development\Inkpot\Definitions.Inkpot.h</ClCompile_ForcedIncludeFiles_2>
    <ClCompile_ForcedIncludeFiles_3>$(ProjectForcedIncludeFiles);$(SolutionDir)Plugins\OptimizedWebBrowser\Intermediate\Build\Win64\x64\UnrealEditor\Development\OptimizedWebBrowser\Definitions.OptimizedWebBrowser.h</ClCompile_ForcedIncludeFiles_3>
    <ClCompile_ForcedIncludeFiles_4>$(ProjectForcedIncludeFiles);$(SolutionDir)Plugins\PlatformFunctionsPlugin_5.4\Intermediate\Build\Win64\x64\UnrealEditor\Development\PlatformFunctions\Definitions.PlatformFunctions.h</ClCompile_ForcedIncludeFiles_4>
    <ClCompile_ForcedIncludeFiles_5>$(SolutionDir)Plugins\rdBPtools\Intermediate\Build\Win64\x64\UnrealEditor\Development\rdBPtools\PCH.rdBPtools.h;$(SolutionDir)Plugins\rdBPtools\Intermediate\Build\Win64\x64\UnrealEditor\Development\rdBPtools\Definitions.h</ClCompile_ForcedIncludeFiles_5>
    <ClCompile_ForcedIncludeFiles_6>$(ProjectForcedIncludeFiles);$(SolutionDir)Plugins\SnappingHelper\Intermediate\Build\Win64\x64\UnrealEditor\Development\SnappingHelper\Definitions.SnappingHelper.h</ClCompile_ForcedIncludeFiles_6>
    <ClCompile_ForcedIncludeFiles_7>$(ProjectForcedIncludeFiles);$(SolutionDir)Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Definitions.Baoli.h</ClCompile_ForcedIncludeFiles_7>
    <ClCompile_AdditionalOptions>$(AdditionalOptions) /Yu"$(SolutionDir)Intermediate\Build\Win64\x64\BaoliEditor\Development\CoreUObject\SharedPCH.CoreUObject.Project.ValApi.Cpp20.InclOrderUnreal5_3.h"</ClCompile_AdditionalOptions>
    <ClCompile_AdditionalOptions_1>$(AdditionalOptions) /Yu"$(SolutionDir)Intermediate\Build\Win64\x64\BaoliEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h"</ClCompile_AdditionalOptions_1>
    <ClCompile_AdditionalOptions_2>$(AdditionalOptions) /Yu"$(SolutionDir)Plugins\rdBPtools\Intermediate\Build\Win64\x64\UnrealEditor\Development\rdBPtools\PCH.rdBPtools.h"</ClCompile_AdditionalOptions_2>
  </PropertyGroup>
  <ItemGroup>
    <None Include="..\..\Baoli.uproject"/>
    <None Include="..\..\Source\Baoli.Target.cs"/>
    <None Include="..\..\Source\BaoliEditor.Target.cs"/>
    <None Include="..\..\clean.bat"/>
    <None Include="..\..\Config\DefaultEditor.ini"/>
    <None Include="..\..\Config\DefaultEngine.ini"/>
    <None Include="..\..\Config\DefaultGame.ini"/>
    <None Include="..\..\Config\DefaultGameplayTags.ini"/>
    <None Include="..\..\Config\DefaultInput.ini"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\BlockoutToolsEditorPlugin.Build.cs"/>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\Private\BlockoutToolsEditorPlugin.cpp"/>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\Public\BlockoutToolsEditorPlugin.h"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\BlockoutToolsPlugin.uplugin"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\BlockoutToolsEditorIcon16.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\BlockoutToolsIcon64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Box_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Cone_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Corner_Curved_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Corner_Ramp_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Cylinder_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Doorway_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Railing_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Ramp_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Skewbox_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Sphere_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Stairs_Curved_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Stairs_Linear_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Tube_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Window_64.png"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\DLSS\DLSS.uplugin"/>
    <None Include="..\..\Plugins\DLSS\Config\FilterPlugin.ini"/>
    <None Include="..\..\Plugins\DLSS\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\DLSS\Shaders\Private\GBufferResolve.usf"/>
    <None Include="..\..\Plugins\DLSS\Shaders\Private\VelocityCombine.usf"/>
    <None Include="..\..\Plugins\FSR3\FSR3.uplugin"/>
    <None Include="..\..\Plugins\FSR3\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_compute_game_vector_field_inpainting_pyramid_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_compute_inpainting_pyramid_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_debug_view_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_disocclusion_mask_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_game_motion_vector_field_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_inpainting_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_optical_flow_vector_field.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_reconstruct_and_dilate_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_reconstruct_prev_depth_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_setup_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_accumulate_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_autogen_reactive_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_debug_view_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_luma_instability_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_luma_pyramid_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_prepare_inputs_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_prepare_reactivity_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_rcas_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_shading_change_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_shading_change_pyramid_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_compute_luminance_pyramid_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_compute_optical_flow_advanced_pass_v5.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_compute_scd_divergence_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_filter_optical_flow_pass_v5.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_generate_scd_histogram_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_prepare_luma_pass.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_scale_optical_flow_advanced_pass_v5.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\PostProcessFFX_FIAdditionalUI.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\PostProcessFFX_FSR3ConvertVelocity.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\PostProcessFFX_FSR3CopyExposure.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\PostProcessFFX_FSR3CreateReactiveMask.usf"/>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\PostProcessFFX_FSR3DeDither.usf"/>
    <None Include="..\..\Plugins\Inkpot\Inkpot.uplugin"/>
    <None Include="..\..\Plugins\MergeAssist-master\MergeAssist.uplugin"/>
    <None Include="..\..\Plugins\MergeAssist-master\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\OptimizedWebBrowser\OptimizedWebBrowser.uplugin"/>
    <None Include="..\..\Plugins\PhysicalLayout\PhysicalLayout.uplugin"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\icon.svg"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\icon_file_save_40x.png"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\icon_file_save_clicked_40x.png"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\icon_file_save_hover_40x.png"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paint.png"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paint.svg"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paint2.png"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paint2.svg"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paintselect.png"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paintselect.svg"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\physicalLayout.png"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\select.png"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\select.svg"/>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\selectActors.png"/>
    <None Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\PlatformFunctions.uplugin"/>
    <None Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\rdBPtools\rdBPtools.uplugin"/>
    <None Include="..\..\Plugins\rdBPtools\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\rdInst\rdInst.uplugin"/>
    <None Include="..\..\Plugins\rdInst\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\SnappingHelper\SnappingHelper.uplugin"/>
    <None Include="..\..\Plugins\SnappingHelper\Resources\CheckBox2d_16x.png"/>
    <None Include="..\..\Plugins\SnappingHelper\Resources\CheckBox2d_UE5_16x.png"/>
    <None Include="..\..\Plugins\SnappingHelper\Resources\CheckBox3d_16x.png"/>
    <None Include="..\..\Plugins\SnappingHelper\Resources\CheckBox3d_UE5_16x.png"/>
    <None Include="..\..\Plugins\SnappingHelper\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\SnappingHelper\Resources\SpriteCircleIconStroke_16x.png"/>
    <None Include="..\..\Plugins\XV3dGS\XV3dGS.uplugin"/>
    <None Include="..\..\Plugins\XV3dGS\Resources\Icon128.png"/>
    <None Include="..\..\Plugins\XV3dGS\Resources\LogoThumbnail_40.png"/>
    <None Include="..\..\Plugins\XV3dGS\Resources\PlaceholderButtonIcon.svg"/>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\BlockoutToolsPlugin.Build.cs"/>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private\BlockoutToolsParent.cpp"/>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private\BlockoutToolsParent.h"/>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private\BlockoutToolsPlugin.cpp"/>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private\BlockoutToolsSettings.cpp"/>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Public\BlockoutToolsPlugin.h"/>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Public\BlockoutToolsSettings.h"/>
    <None Include="..\..\Plugins\DLSS\Source\DLSSBlueprint\DLSSBlueprint.Build.cs"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSBlueprint\Private\DLSSLibrary.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSBlueprint\Public\DLSSLibrary.h"/>
    <None Include="..\..\Plugins\DLSS\Source\DLSSEditor\DLSSEditor.Build.cs"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSEditor\Private\DLSSEditor.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSEditor\Public\DLSSEditor.h"/>
    <None Include="..\..\Plugins\DLSS\Source\DLSSUtility\DLSSUtility.Build.cs"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Private\DLSSUtility.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Private\GBufferResolvePass.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Private\VelocityCombinePass.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Public\DLSSUtility.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Public\GBufferResolvePass.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Public\VelocityCombinePass.h"/>
    <None Include="..\..\Plugins\DLSS\Source\DLSS\DLSS.Build.cs"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSS.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSDenoiser.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSDenoiser.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSUpscaler.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSUpscalerHistory.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSUpscalerHistory.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSUpscalerPrivate.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Public\DLSS.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Public\DLSSSettings.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Public\DLSSUpscaler.h"/>
    <None Include="..\..\Plugins\DLSS\Source\NGXD3D11RHI\NGXD3D11RHI.Build.cs"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXD3D11RHI\Private\NGXD3D11RHI.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXD3D11RHI\Public\NGXD3D11RHI.h"/>
    <None Include="..\..\Plugins\DLSS\Source\NGXD3D12RHI\NGXD3D12RHI.Build.cs"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXD3D12RHI\Private\NGXD3D12RHI.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXD3D12RHI\Public\NGXD3D12RHI.h"/>
    <None Include="..\..\Plugins\DLSS\Source\NGXRHI\NGXRHI.Build.cs"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXRHI\Private\NGXRHI.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXRHI\Public\NGXRHI.h"/>
    <None Include="..\..\Plugins\DLSS\Source\NGXVulkanRHIPreInit\NGXVulkanRHIPreInit.Build.cs"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXVulkanRHIPreInit\Private\NGXVulkanRHIPreInit.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXVulkanRHIPreInit\Public\NGXVulkanRHIPreInit.h"/>
    <None Include="..\..\Plugins\DLSS\Source\NGXVulkanRHI\NGXVulkanRHI.Build.cs"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXVulkanRHI\Private\NGXVulkanRHI.cpp"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXVulkanRHI\Public\NGXVulkanRHI.h"/>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\LICENSE.txt"/>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\NGX.Build.cs"/>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\NGX.tps"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_defs.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_defs_dlssd.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_helpers.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_helpers_dlssd.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_helpers_dlssd_vk.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_helpers_vk.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_params.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_params_dlssd.h"/>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_vk.h"/>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\DLSS_Debug_Jitter_Configs.txt"/>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_driver_onscreenindicator.reg"/>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_driver_onscreenindicator_off.reg"/>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_log_off.reg"/>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_log_on.reg"/>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_log_verbose.reg"/>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_log_window_off.reg"/>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_log_window_on.reg"/>
    <None Include="..\..\Plugins\FSR3\Source\FFXD3D12Backend\FFXD3D12Backend.Build.cs"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12Backend\Private\FFXD3D12Backend.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12Backend\Public\FFXD3D12Backend.h"/>
    <None Include="..\..\Plugins\FSR3\Source\FFXD3D12\FFXD3D12.Build.cs"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12\Private\FFXD3D12Includes.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12\Private\FFXD3D12Module.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12\Public\FFXD3D12Includes.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12\Public\FFXD3D12Module.h"/>
    <None Include="..\..\Plugins\FSR3\Source\FFXFSR3Api\FFXFSR3Api.Build.cs"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Api\Private\FFXFSR3.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Api\Private\FFXFSR3Module.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Api\Public\FFXFSR3.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Api\Public\FFXFSR3Module.h"/>
    <None Include="..\..\Plugins\FSR3\Source\FFXFSR3Settings\FFXFSR3Settings.Build.cs"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Settings\Private\FFXFSR3Settings.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Settings\Public\FFXFSR3Settings.h"/>
    <None Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\FFXFSR3TemporalUpscaling.Build.cs"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3Include.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscaler.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscaler.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscalerHistory.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscalerHistory.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscalerProxy.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscalerProxy.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscaling.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3ViewExtension.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRAccumulatePass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRAutogenReactiveMaskPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRDebugViewPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRLumaInstabilityPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRLumaPyramidPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRPrepareInputs.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRPrepareReactivity.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRRCASPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRShaders.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRShaders.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRShadingChangePass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRShadingChangePyramidPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\LogFFXFSR3.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Public\FFXFSR3History.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Public\FFXFSR3TemporalUpscaling.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Public\FFXFSR3ViewExtension.h"/>
    <None Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolationApi\FFXFrameInterpolationApi.Build.cs"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolationApi\Private\FFXFrameInterpolationApi.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolationApi\Private\FFXFrameInterpolationApiModule.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolationApi\Public\FFXFrameInterpolationApi.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolationApi\Public\FFXFrameInterpolationApiModule.h"/>
    <None Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\FFXFrameInterpolation.Build.cs"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolation.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolation.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationCustomPresent.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationCustomPresent.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationModule.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationSlate.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationSlate.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationViewExtension.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationViewExtension.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIDebugViewPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIDisocclusionMask.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIFrameInterpolationPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIFrameInterpolationSetupPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIGameMotionVectorField.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIGameMotionVectorFieldInpainting.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIInpainting.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIInpaintingPyramid.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIOpticalFlowVectorField.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIReconstructAndDilatePass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIReconstructPreviousDepthPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIShaders.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIShaders.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowComputeLumaPyramidPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowComputeOpticalFlowAdvPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowComputeSCDDivergencePass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowFilterOpticalFlowPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowGenSCDHistogramPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowPrepareLumaPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowScaleOpticalFlowAdvPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowShaders.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowShaders.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\LogFFXFrameInterpolation.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Public\FFXFrameInterpolationModule.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Public\IFFXFrameInterpolation.h"/>
    <None Include="..\..\Plugins\FSR3\Source\FFXOpticalFlowApi\FFXOpticalFlowApi.Build.cs"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXOpticalFlowApi\Private\FFXOpticalFlowApi.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXOpticalFlowApi\Private\FFXOpticalFlowApiModule.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXOpticalFlowApi\Public\FFXOpticalFlowApi.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXOpticalFlowApi\Public\FFXOpticalFlowApiModule.h"/>
    <None Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\FFXRHIBackend.Build.cs"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Private\FFXRHIApi.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Private\FFXRHIBackend.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Private\FFXRHIBackendModule.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Private\FFXRHIBackendShaders.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Private\FFXRHIBackendSubPass.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Public\FFXRHIBackend.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Public\FFXRHIBackendModule.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Public\FFXRHIBackendShaders.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Public\FFXRHIBackendSubPass.h"/>
    <None Include="..\..\Plugins\FSR3\Source\FFXShared\FFXShared.Build.cs"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Private\FFXShared.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Private\FFXSharedBackend.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Private\FFXSharedModule.cpp"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Public\FFXShared.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Public\FFXSharedBackend.h"/>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Public\FFXSharedModule.h"/>
    <None Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\InkPlusPlus.Build.cs"/>
    <None Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\LICENSE.txt"/>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\InkPlusPlus.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\CallStack.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Choice.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\ChoicePoint.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Container.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\ControlCommand.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\DebugMetadata.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Divert.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Flow.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\InkList.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\InkListItem.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\JsonExtension.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\JsonSerialisation.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\ListDefinition.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\ListDefinitionsOrigin.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\NamedContent.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\NativeFunctionCall.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Object.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Path.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Pointer.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\SearchResult.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\SimpleJson.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\SimpleJsonObject.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\StatePatch.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Story.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\StoryException.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\StoryState.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Tag.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Value.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\VariableAssignment.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\VariableReference.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\VariableState.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Utility\InkPlusPlusLog.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Utility\InkPlusPlusUtility.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\InkPlusPlus.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\CallStack.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Choice.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\ChoicePoint.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Container.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\ControlCommand.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\DebugMetadata.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Divert.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Error.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Flow.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Glue.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\InkList.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\InkListItem.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\JsonExtension.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\JsonSerialisation.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\ListDefinition.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\ListDefinitionsOrigin.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\NamedContent.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\NativeFunctionCall.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Object.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Path.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Pointer.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\PushPop.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\SearchResult.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\SimpleJson.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\SimpleJsonObject.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\StatePatch.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Story.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\StoryException.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\StoryState.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Tag.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Value.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\VariableAssignment.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\VariableReference.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\VariableState.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Void.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Utility\InkPlusPlusLog.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Utility\InkPlusPlusUtility.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <None Include="..\..\Plugins\Inkpot\Source\InkpotEditor\InkpotEditor.Build.cs"/>
    <None Include="..\..\Plugins\Inkpot\Source\InkpotEditor\LICENSE.txt"/>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\InkpotEditorModule.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\Asset\InkpotStoryAssetActions.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\Asset\InkpotStoryAssetFactory.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\Compiler\InkCompiler.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\Test\InkFunctionTests.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\Test\InkPlusPlusTest.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Public\InkpotEditorModule.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Public\Asset\InkpotStoryAssetActions.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Public\Asset\InkpotStoryAssetFactory.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Public\Compiler\InkCompiler.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Public\Test\InkFunctionTests.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <None Include="..\..\Plugins\Inkpot\Source\Inkpot\Inkpot.Build.cs"/>
    <None Include="..\..\Plugins\Inkpot\Source\Inkpot\LICENSE.txt"/>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\InkpotModule.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Asset\InkpotStoryAsset.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\Inkpot.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotChoice.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotLine.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotStories.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotStory.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\inkpotStoryFactory.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotStoryHistory.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotStoryInternal.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotValue.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotWatch.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\AsyncActions\AsyncAction_WaitVariableChange.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Settings\InkpotCVars.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Settings\InkpotSettings.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Utility\InkpotLog.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Utility\InkpotUtility.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\InkpotModule.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Asset\InkpotStoryAsset.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\Inkpot.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotChoice.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotLine.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotStories.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotStory.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotStoryFactory.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotStoryHistory.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotStoryInternal.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotValue.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotWatch.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\AsyncActions\AsyncAction_WaitVariableChange.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Settings\InkpotCVars.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Settings\InkpotSettings.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Utility\InkpotLog.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Utility\InkpotUtility.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_2)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_2)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <None Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\MergeAssist.Build.cs"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\BlueprintMergeData.h"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\FDiffHelper.cpp"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\FDiffHelper.h"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\GraphMergeHelper.cpp"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\GraphMergeHelper.h"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\MergeAssist.cpp"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SBlueprintMergeAssist.cpp"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SBlueprintMergeAssist.h"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SMergeGraphView.cpp"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SMergeGraphView.h"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SMergeTreeView.cpp"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SMergeTreeView.h"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal\MergeUtils.cpp"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal\MergeUtils.h"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal\SMergeAssetPickerView.cpp"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal\SMergeAssetPickerView.h"/>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Public\MergeAssist.h"/>
    <None Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\OptimizedWebBrowser.build.cs"/>
    <None Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\OptimizedWebBrowser_UPL.xml"/>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Private\OptimizedWebBrowser.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_3)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_3)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Private\OptimizedWebBrowserModule.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_3)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_3)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Private\WebBrowserOptimizer.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_3)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_3)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Public\OptimizedWebBrowser.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_3)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_3)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Public\OptimizedWebBrowserModule.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_3)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_3)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Public\WebBrowserOptimizer.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_3)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_3)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <None Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\PhysicalLayout.Build.cs"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayout.cpp"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayoutCommands.cpp"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayoutMode.cpp"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayoutPreset.cpp"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayoutStyle.cpp"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayoutToolkit.cpp"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayout.h"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayoutCommands.h"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayoutMode.h"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayoutPreset.h"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayoutStyle.h"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayoutToolkit.h"/>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\SlateUtil.h"/>
    <None Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\PlatformFunctions.Build.cs"/>
    <ClCompile Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Private\PlatformFunctions.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_4)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_4)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Private\PlatformFunctionsBPLibrary.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_4)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_4)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Public\PlatformFunctions.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_4)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_4)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Public\PlatformFunctionsBPLibrary.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_4)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_4)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <None Include="..\..\Plugins\rdBPtools\Source\rdBPtools\rdBPtools.Build.cs"/>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPSubsystem.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPSubsystem.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPtools.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPtoolsMenuCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPtoolsMenuCommands.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPtoolsOptions.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPtoolsOptions.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_About.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_BPOutlinerMenu.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_BPToolbarMenu.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_BrowserContextMenu.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_ChangeMobility.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_ConvertStaticMeshes.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_CopyToLevel.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_CreateFromSelectedActors.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_CreateLevelInstance.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_CreateSpawnActorFromSelection.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_DuplicateAndEdit.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_HarvestInstances.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_Helpers.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_InstanceSettings.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_MirrorBlueprint.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_MoveToFoliage.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_PlaceOnGround.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_RandomSettings.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_RandomSettingsComp.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_RandomSettingsFolder.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_RemoveRandomSettings.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_SetRelyOnActors.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_SetRelyOnActorsComp.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_ShiftObjects.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_Stats.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_ToolMenu.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIAbout.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIConvertToSpawnActor.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UICopyToLevel.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UICreateFromSelectedActors.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UICreateLevelInstance.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIInstanceSettings.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIRandomFolderSettings.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIRandomSettings.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIRandomSettingsComp.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIReplaceInLevel.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UISelectFoliageType.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIShiftObjects.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIUpdateFromSelectedActors.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UpdateFromSelectedActors.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_WorldContextMenu.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpActorPicker.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpActorPicker.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpAssetPicker.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpAssetPicker.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpFolderPicker.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpFolderPicker.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdUMGHelpers.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Public\IrdBPtools.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Public\rdBPtools.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_5)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_5)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_2)</AdditionalOptions>
    </ClCompile>
    <None Include="..\..\Plugins\rdInst\Source\rdInst\rdInst.Build.cs"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Assimilation.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Build.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Conversions.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Instancing.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Pooling.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Randomization.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Spawning.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Themes.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Utilities.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Visibility.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInst.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Conversions.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Distributed.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Instancing.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_LandscapeModify.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Pooling.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Procedural.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Proxies.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_SpawnStuff.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Splines.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_StaticInstancing.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Utilities.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBPLibrary.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBreakAndMake.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstSubsystem.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstSubsystem.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rd_PixelWrappers.cpp"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rd_PixelWrappers.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdActor.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdInst.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdInstances.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdInstBaseActor.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdInstBPLibrary.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdInstStates.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdLandscapeModify.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdPlacement.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdPools.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdProceduralActor.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdProxies.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdSpawnStuff.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdSpawnStuffActor.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdSplines.h"/>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdUtilities.h"/>
    <None Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\SnappingHelper.Build.cs"/>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\SnappingHelper.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\SnappingHelperCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\SnappingHelperEdMode.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\SnappingHelperSettings.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\SnappingHelperStyle.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\Helpers\VertexIterators.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\Widgets\SSnappingHelperComboButton.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\FSnappingHelperCommands.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\SnappingHelper.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\SnappingHelperEdMode.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\SnappingHelperSettings.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\SnappingHelperStyle.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\Helpers\VertexIterators.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\Widgets\SSnappingHelperComboButton.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_6)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_6)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <None Include="..\..\Plugins\XV3dGS\Source\GSEditor\GSEditor.Build.cs"/>
    <None Include="..\..\Plugins\XV3dGS\Source\GSImporter\GSImporter.Build.cs"/>
    <None Include="..\..\Plugins\XV3dGS\Source\GSRuntime\GSRuntime.Build.cs"/>
    <None Include="..\..\Source\Baoli\Baoli.Build.cs"/>
    <ClCompile Include="..\..\Source\Baoli\Baoli.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Baoli.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\BaoliGameUserSettings.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\BaoliGameUserSettings.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\AI\AI_CharacterBase.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\AI\AI_CharacterBase.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\AI\MyAIController.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\AI\MyAIController.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Chest\ChestActorPawn.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Chest\ChestActorPawn.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Chest\KettleActorPawn.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Chest\KettleActorPawn.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Cover\Almirah.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Cover\Almirah.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Cover\Bed.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Cover\Bed.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Player\Baoli_Character.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Player\Baoli_Character.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Player\Baoli_Controller.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Player\Baoli_Controller.h">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_7)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_7)</ForcedIncludeFiles>
      <AdditionalOptions>$(ClCompile_AdditionalOptions_1)</AdditionalOptions>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\NativeFunctionCall_fixed.h" />
  </ItemGroup>
  <PropertyGroup>
    <SourcePath>D:\UE_5.5\Engine\Source\Developer\TreeMap;D:\UE_5.5\Engine\Source\Editor\UATHelper;D:\UE_5.5\Engine\Source\ThirdParty\libJPG;D:\UE_5.5\Engine\Source\Developer\AITestSuite\Private;D:\UE_5.5\Engine\Source\Developer\AnimationDataController\Private;D:\UE_5.5\Engine\Source\Developer\AnimationWidgets\Private;D:\UE_5.5\Engine\Source\Developer\AssetTools\Private;D:\UE_5.5\Engine\Source\Developer\AudioFormatADPCM\Private;D:\UE_5.5\Engine\Source\Developer\AudioFormatBink\Private;D:\UE_5.5\Engine\Source\Developer\AudioFormatOgg\Private;D:\UE_5.5\Engine\Source\Developer\AudioFormatOpus\Private;D:\UE_5.5\Engine\Source\Developer\AudioFormatRad\Private;D:\UE_5.5\Engine\Source\Developer\AudioSettingsEditor\Private;D:\UE_5.5\Engine\Source\Developer\AutomationController\Private;D:\UE_5.5\Engine\Source\Developer\AutomationDriver\Private;D:\UE_5.5\Engine\Source\Developer\AutomationWindow\Private;D:\UE_5.5\Engine\Source\Developer\BlankModule\Private;D:\UE_5.5\Engine\Source\Developer\BSPUtils\Private;D:\UE_5.5\Engine\Source\Developer\CollectionManager\Private;D:\UE_5.5\Engine\Source\Developer\CollisionAnalyzer\Private;D:\UE_5.5\Engine\Source\Developer\CookedEditor\Private;D:\UE_5.5\Engine\Source\Developer\CookMetadata\Private;D:\UE_5.5\Engine\Source\Developer\CookOnTheFlyNetServer\Private;D:\UE_5.5\Engine\Source\Developer\CQTest\Private;D:\UE_5.5\Engine\Source\Developer\CrashDebugHelper\Private;D:\UE_5.5\Engine\Source\Developer\DerivedDataCache\Private;D:\UE_5.5\Engine\Source\Developer\DerivedDataCache\Tests;D:\UE_5.5\Engine\Source\Developer\DesktopPlatform\Private;D:\UE_5.5\Engine\Source\Developer\DesktopWidgets\Private;D:\UE_5.5\Engine\Source\Developer\DeveloperToolSettings\Private;D:\UE_5.5\Engine\Source\Developer\DeviceManager\Private;D:\UE_5.5\Engine\Source\Developer\DirectoryWatcher\Private;D:\UE_5.5\Engine\Source\Developer\DrawPrimitiveDebugger\Private;D:\UE_5.5\Engine\Source\Developer\EditorAnalyticsSession\Private;D:\UE_5.5\Engine\Source\Developer\ExternalImagePicker\Private;D:\UE_5.5\Engine\Source\Developer\FileUtilities\Private;D:\UE_5.5\Engine\Source\Developer\FunctionalTesting\Private;D:\UE_5.5\Engine\Source\Developer\GeometryProcessingInterfaces\Private;D:\UE_5.5\Engine\Source\Developer\GraphColor\Private;D:\UE_5.5\Engine\Source\Developer\HierarchicalLODUtilities\Private;D:\UE_5.5\Engine\Source\Developer\Horde\Private;D:\UE_5.5\Engine\Source\Developer\HotReload\Private;D:\UE_5.5\Engine\Source\Developer\IoStoreUtilities\Private;D:\UE_5.5\Engine\Source\Developer\LauncherServices\Private;D:\UE_5.5\Engine\Source\Developer\Localization\Private;D:\UE_5.5\Engine\Source\Developer\LocalizationService\Private;D:\UE_5.5\Engine\Source\Developer\LogVisualizer\Private;D:\UE_5.5\Engine\Source\Developer\LowLevelTestsRunner\Private;D:\UE_5.5\Engine\Source\Developer\LowLevelTestsRunner\Tests;D:\UE_5.5\Engine\Source\Developer\MassEntityTestSuite\Private;D:\UE_5.5\Engine\Source\Developer\MaterialBaking\Private;D:\UE_5.5\Engine\Source\Developer\MaterialUtilities\Private;D:\UE_5.5\Engine\Source\Developer\Merge\Private;D:\UE_5.5\Engine\Source\Developer\MeshBoneReduction\Private;D:\UE_5.5\Engine\Source\Developer\MeshBuilder\Private;D:\UE_5.5\Engine\Source\Developer\MeshBuilderCommon\Private;D:\UE_5.5\Engine\Source\Developer\MeshDescriptionOperations\Private;D:\UE_5.5\Engine\Source\Developer\MeshMergeUtilities\Private;D:\UE_5.5\Engine\Source\Developer\MeshReductionInterface\Private;D:\UE_5.5\Engine\Source\Developer\MeshSimplifier\Private;D:\UE_5.5\Engine\Source\Developer\MeshUtilities\Private;D:\UE_5.5\Engine\Source\Developer\MeshUtilitiesEngine\Private;D:\UE_5.5\Engine\Source\Developer\MessageLog\Private;D:\UE_5.5\Engine\Source\Developer\NaniteBuilder\Private;D:\UE_5.5\Engine\Source\Developer\NaniteUtilities\Private;D:\UE_5.5\Engine\Source\Developer\OutputLog\Private;D:\UE_5.5\Engine\Source\Developer\PakFileUtilities\Private;D:\UE_5.5\Engine\Source\Developer\PhysicsUtilities\Private;D:\UE_5.5\Engine\Source\Developer\Profiler\Private;D:\UE_5.5\Engine\Source\Developer\ProfilerClient\Private;D:\UE_5.5\Engine\Source\Developer\ProfilerMessages\Private;D:\UE_5.5\Engine\Source\Developer\ProfilerService\Private;D:\UE_5.5\Engine\Source\Developer\ProfileVisualizer\Private;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private;D:\UE_5.5\Engine\Source\Developer\RealtimeProfiler\Private;D:\UE_5.5\Engine\Source\Developer\S3Client\Private;D:\UE_5.5\Engine\Source\Developer\ScreenShotComparison\Private;D:\UE_5.5\Engine\Source\Developer\ScreenShotComparisonTools\Private;D:\UE_5.5\Engine\Source\Developer\ScriptDisassembler\Private;D:\UE_5.5\Engine\Source\Developer\SessionFrontend\Private;D:\UE_5.5\Engine\Source\Developer\Settings\Private;D:\UE_5.5\Engine\Source\Developer\SettingsEditor\Private;D:\UE_5.5\Engine\Source\Developer\ShaderCompilerCommon\Private;D:\UE_5.5\Engine\Source\Developer\ShaderFormatOpenGL\Private;D:\UE_5.5\Engine\Source\Developer\ShaderFormatVectorVM\Private;D:\UE_5.5\Engine\Source\Developer\ShaderPreprocessor\Private;D:\UE_5.5\Engine\Source\Developer\SharedSettingsWidgets\Private;D:\UE_5.5\Engine\Source\Developer\SkeletalMeshUtilitiesCommon\Private;D:\UE_5.5\Engine\Source\Developer\SlackIntegrations\Private;D:\UE_5.5\Engine\Source\Developer\SlateFileDialogs\Private;D:\UE_5.5\Engine\Source\Developer\SlateFontDialog\Private;D:\UE_5.5\Engine\Source\Developer\SlateReflector\Private;D:\UE_5.5\Engine\Source\Developer\SourceCodeAccess\Private;D:\UE_5.5\Engine\Source\Developer\SourceControl\Private;D:\UE_5.5\Engine\Source\Developer\SourceControlCheckInPrompt\Private;D:\UE_5.5\Engine\Source\Developer\SourceControlViewport\Private;D:\UE_5.5\Engine\Source\Developer\StandaloneRenderer\Private;D:\UE_5.5\Engine\Source\Developer\StructUtilsTestSuite\Private;D:\UE_5.5\Engine\Source\Developer\TargetDeviceServices\Private;D:\UE_5.5\Engine\Source\Developer\TargetPlatform\Private;D:\UE_5.5\Engine\Source\Developer\TextureBuild\Private;D:\UE_5.5\Engine\Source\Developer\TextureBuildUtilities\Private;D:\UE_5.5\Engine\Source\Developer\TextureCompressor\Private;D:\UE_5.5\Engine\Source\Developer\TextureFormat\Private;D:\UE_5.5\Engine\Source\Developer\TextureFormatASTC\Private;D:\UE_5.5\Engine\Source\Developer\TextureFormatDXT\Private;D:\UE_5.5\Engine\Source\Developer\TextureFormatETC2\Private;D:\UE_5.5\Engine\Source\Developer\TextureFormatIntelISPCTexComp\Private;D:\UE_5.5\Engine\Source\Developer\TextureFormatUncompressed\Private;D:\UE_5.5\Engine\Source\Developer\ToolMenus\Private;D:\UE_5.5\Engine\Source\Developer\ToolWidgets\Private;D:\UE_5.5\Engine\Source\Developer\TraceAnalysis\Private;D:\UE_5.5\Engine\Source\Developer\TraceServices\Private;D:\UE_5.5\Engine\Source\Developer\TraceTools\Private;D:\UE_5.5\Engine\Source\Developer\TranslationEditor\Private;D:\UE_5.5\Engine\Source\Developer\TurnkeyIO\Private;D:\UE_5.5\Engine\Source\Developer\UbaCoordinatorHorde\Private;D:\UE_5.5\Engine\Source\Developer\UncontrolledChangelists\Private;D:\UE_5.5\Engine\Source\Developer\UndoHistory\Private;D:\UE_5.5\Engine\Source\Developer\Virtualization\Private;D:\UE_5.5\Engine\Source\Developer\VisualGraphUtils\Private;D:\UE_5.5\Engine\Source\Developer\VulkanShaderFormat\Private;D:\UE_5.5\Engine\Source\Developer\WidgetRegistration\Private;D:\UE_5.5\Engine\Source\Developer\Zen\Private;D:\UE_5.5\Engine\Source\Developer\Zen\Tests;D:\UE_5.5\Engine\Source\Developer\ZenPluggableTransport\winsock;D:\UE_5.5\Engine\Source\Editor\ActionableMessage\Private;D:\UE_5.5\Engine\Source\Editor\ActorPickerMode\Private;D:\UE_5.5\Engine\Source\Editor\AddContentDialog\Private;D:\UE_5.5\Engine\Source\Editor\AdvancedPreviewScene\Private;D:\UE_5.5\Engine\Source\Editor\AIGraph\Private;D:\UE_5.5\Engine\Source\Editor\AnimationBlueprintEditor\Private;D:\UE_5.5\Engine\Source\Editor\AnimationBlueprintLibrary\Private;D:\UE_5.5\Engine\Source\Editor\AnimationEditMode\Private;D:\UE_5.5\Engine\Source\Editor\AnimationEditor\Private;D:\UE_5.5\Engine\Source\Editor\AnimationEditorWidgets\Private;D:\UE_5.5\Engine\Source\Editor\AnimationModifiers\Private;D:\UE_5.5\Engine\Source\Editor\AnimationSettings\Private;D:\UE_5.5\Engine\Source\Editor\AnimGraph\Private;D:\UE_5.5\Engine\Source\Editor\AssetDefinition\Private;D:\UE_5.5\Engine\Source\Editor\AssetTagsEditor\Private;D:\UE_5.5\Engine\Source\Editor\AudioEditor\Private;D:\UE_5.5\Engine\Source\Editor\BehaviorTreeEditor\Private;D:\UE_5.5\Engine\Source\Editor\BlueprintEditorLibrary\Private;D:\UE_5.5\Engine\Source\Editor\BlueprintGraph\Private;D:\UE_5.5\Engine\Source\Editor\Blutility\Private;D:\UE_5.5\Engine\Source\Editor\Cascade\Private;D:\UE_5.5\Engine\Source\Editor\ClassViewer\Private;D:\UE_5.5\Engine\Source\Editor\ClothingSystemEditor\Private;D:\UE_5.5\Engine\Source\Editor\ClothingSystemEditorInterface\Private;D:\UE_5.5\Engine\Source\Editor\ClothPainter\Private;D:\UE_5.5\Engine\Source\Editor\CommonMenuExtensions\Private;D:\UE_5.5\Engine\Source\Editor\ComponentVisualizers\Private;D:\UE_5.5\Engine\Source\Editor\ConfigEditor\Private;D:\UE_5.5\Engine\Source\Editor\ContentBrowser\Private;D:\UE_5.5\Engine\Source\Editor\ContentBrowserData\Private;D:\UE_5.5\Engine\Source\Editor\CSVtoSVG\Private;D:\UE_5.5\Engine\Source\Editor\CurveAssetEditor\Private;D:\UE_5.5\Engine\Source\Editor\CurveEditor\Private;D:\UE_5.5\Engine\Source\Editor\CurveTableEditor\Private;D:\UE_5.5\Engine\Source\Editor\DataLayerEditor\Private;D:\UE_5.5\Engine\Source\Editor\DataTableEditor\Private;D:\UE_5.5\Engine\Source\Editor\DerivedDataEditor\Private;D:\UE_5.5\Engine\Source\Editor\DetailCustomizations\Private;D:\UE_5.5\Engine\Source\Editor\DeviceProfileEditor\Private;D:\UE_5.5\Engine\Source\Editor\DeviceProfileServices\Private;D:\UE_5.5\Engine\Source\Editor\DistCurveEditor\Private;D:\UE_5.5\Engine\Source\Editor\Documentation\Private;D:\UE_5.5\Engine\Source\Editor\EditorConfig\Private;D:\UE_5.5\Engine\Source\Editor\EditorFramework\Private;D:\UE_5.5\Engine\Source\Editor\EditorSettingsViewer\Private;D:\UE_5.5\Engine\Source\Editor\EditorStyle\Private;D:\UE_5.5\Engine\Source\Editor\EditorSubsystem\Private;D:\UE_5.5\Engine\Source\Editor\EditorWidgets\Private;D:\UE_5.5\Engine\Source\Editor\EnvironmentLightingViewer\Private;D:\UE_5.5\Engine\Source\Editor\FoliageEdit\Private;D:\UE_5.5\Engine\Source\Editor\FontEditor\Private;D:\UE_5.5\Engine\Source\Editor\GameplayDebugger\Private;D:\UE_5.5\Engine\Source\Editor\GameplayTasksEditor\Private;D:\UE_5.5\Engine\Source\Editor\GameProjectGeneration\Private;D:\UE_5.5\Engine\Source\Editor\GraphEditor\Private;D:\UE_5.5\Engine\Source\Editor\HardwareTargeting\Private;D:\UE_5.5\Engine\Source\Editor\HierarchicalLODOutliner\Private;D:\UE_5.5\Engine\Source\Editor\InputBindingEditor\Private;D:\UE_5.5\Engine\Source\Editor\InternationalizationSettings\Private;D:\UE_5.5\Engine\Source\Editor\Kismet\Private;D:\UE_5.5\Engine\Source\Editor\KismetCompiler\Private;D:\UE_5.5\Engine\Source\Editor\KismetWidgets\Private;D:\UE_5.5\Engine\Source\Editor\LandscapeEditor\Private;D:\UE_5.5\Engine\Source\Editor\LandscapeEditorUtilities\Private;D:\UE_5.5\Engine\Source\Editor\Layers\Private;D:\UE_5.5\Engine\Source\Editor\LevelEditor\Private;D:\UE_5.5\Engine\Source\Editor\LevelInstanceEditor\Private;D:\UE_5.5\Engine\Source\Editor\LocalizationCommandletExecution\Private;D:\UE_5.5\Engine\Source\Editor\LocalizationDashboard\Private;D:\UE_5.5\Engine\Source\Editor\MainFrame\Private;D:\UE_5.5\Engine\Source\Editor\MassEntityDebugger\Private;D:\UE_5.5\Engine\Source\Editor\MassEntityEditor\Private;D:\UE_5.5\Engine\Source\Editor\MaterialEditor\Private;D:\UE_5.5\Engine\Source\Editor\MergeActors\Private;D:\UE_5.5\Engine\Source\Editor\MeshPaint\Private;D:\UE_5.5\Engine\Source\Editor\MovieSceneCaptureDialog\Private;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private;D:\UE_5.5\Engine\Source\Editor\NewLevelDialog\Private;D:\UE_5.5\Engine\Source\Editor\NNEEditor\Private;D:\UE_5.5\Engine\Source\Editor\OverlayEditor\Private;D:\UE_5.5\Engine\Source\Editor\PackagesDialog\Private;D:\UE_5.5\Engine\Source\Editor\Persona\Private;D:\UE_5.5\Engine\Source\Editor\Persona\Public;D:\UE_5.5\Engine\Source\Editor\PhysicsAssetEditor\Private;D:\UE_5.5\Engine\Source\Editor\PIEPreviewDeviceProfileSelector\Private;D:\UE_5.5\Engine\Source\Editor\PIEPreviewDeviceSpecification\Private;D:\UE_5.5\Engine\Source\Editor\PinnedCommandList\Private;D:\UE_5.5\Engine\Source\Editor\PixelInspector\Private;D:\UE_5.5\Engine\Source\Editor\PlacementMode\Private;D:\UE_5.5\Engine\Source\Editor\PListEditor\Private;D:\UE_5.5\Engine\Source\Editor\PluginWarden\Private;D:\UE_5.5\Engine\Source\Editor\ProjectSettingsViewer\Private;D:\UE_5.5\Engine\Source\Editor\ProjectTargetPlatformEditor\Private;D:\UE_5.5\Engine\Source\Editor\PropertyEditor\Private;D:\UE_5.5\Engine\Source\Editor\RenderResourceViewer\Private;D:\UE_5.5\Engine\Source\Editor\RewindDebuggerInterface\Private;D:\UE_5.5\Engine\Source\Editor\SceneDepthPickerMode\Private;D:\UE_5.5\Engine\Source\Editor\SceneOutliner\Private;D:\UE_5.5\Engine\Source\Editor\ScriptableEditorWidgets\Private;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private;D:\UE_5.5\Engine\Source\Editor\SequencerCore\Private;D:\UE_5.5\Engine\Source\Editor\SequenceRecorder\Private;D:\UE_5.5\Engine\Source\Editor\SequenceRecorderSections\Private;D:\UE_5.5\Engine\Source\Editor\SequencerWidgets\Private;D:\UE_5.5\Engine\Source\Editor\SerializedRecorderInterface\Private;D:\UE_5.5\Engine\Source\Editor\SkeletalMeshEditor\Private;D:\UE_5.5\Engine\Source\Editor\SkeletonEditor\Private;D:\UE_5.5\Engine\Source\Editor\SourceControlWindowExtender\Private;D:\UE_5.5\Engine\Source\Editor\SourceControlWindows\Private;D:\UE_5.5\Engine\Source\Editor\SparseVolumeTexture\Private;D:\UE_5.5\Engine\Source\Editor\StaticMeshEditor\Private;D:\UE_5.5\Engine\Source\Editor\StatsViewer\Private;D:\UE_5.5\Engine\Source\Editor\StatusBar\Private;D:\UE_5.5\Engine\Source\Editor\StringTableEditor\Private;D:\UE_5.5\Engine\Source\Editor\StructUtilsEditor\Private;D:\UE_5.5\Engine\Source\Editor\StructViewer\Private;D:\UE_5.5\Engine\Source\Editor\SubobjectDataInterface\Private;D:\UE_5.5\Engine\Source\Editor\SubobjectEditor\Private;D:\UE_5.5\Engine\Source\Editor\SwarmInterface\Private;D:\UE_5.5\Engine\Source\Editor\TextureEditor\Private;D:\UE_5.5\Engine\Source\Editor\ToolMenusEditor\Private;D:\UE_5.5\Engine\Source\Editor\TurnkeySupport\Private;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Classes;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private;D:\UE_5.5\Engine\Source\Editor\UndoHistoryEditor\Private;D:\UE_5.5\Engine\Source\Editor\UniversalObjectLocatorEditor\Private;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private;D:\UE_5.5\Engine\Source\Editor\UnrealEdMessages\Private;D:\UE_5.5\Engine\Source\Editor\ViewportInteraction\Private;D:\UE_5.5\Engine\Source\Editor\ViewportSnapping\Private;D:\UE_5.5\Engine\Source\Editor\VirtualizationEditor\Private;D:\UE_5.5\Engine\Source\Editor\VirtualTexturingEditor\Private;D:\UE_5.5\Engine\Source\Editor\VREditor\Private;D:\UE_5.5\Engine\Source\Editor\WorkspaceMenuStructure\Private;D:\UE_5.5\Engine\Source\Editor\WorldBrowser\Private;D:\UE_5.5\Engine\Source\Editor\WorldPartitionEditor\Private;D:\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Perforce.Native;D:\UE_5.5\Engine\Source\Runtime\AdvancedWidgets\Private;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private;D:\UE_5.5\Engine\Source\Runtime\AnimationCore\Private;D:\UE_5.5\Engine\Source\Runtime\AnimGraphRuntime\Private;D:\UE_5.5\Engine\Source\Runtime\AppFramework\Private;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private;D:\UE_5.5\Engine\Source\Runtime\AssetRegistry\Private;D:\UE_5.5\Engine\Source\Runtime\AssetRegistry\Tests;D:\UE_5.5\Engine\Source\Runtime\AudioAnalyzer\Private;D:\UE_5.5\Engine\Source\Runtime\AudioCaptureCore\Private;D:\UE_5.5\Engine\Source\Runtime\AudioExtensions\Private;D:\UE_5.5\Engine\Source\Runtime\AudioMixer\Private;D:\UE_5.5\Engine\Source\Runtime\AudioMixerCore\Private;D:\UE_5.5\Engine\Source\Runtime\AudioPlatformConfiguration\Private;D:\UE_5.5\Engine\Source\Runtime\AugmentedReality\Private;D:\UE_5.5\Engine\Source\Runtime\AutomationMessages\Private;D:\UE_5.5\Engine\Source\Runtime\AutomationTest\Private;D:\UE_5.5\Engine\Source\Runtime\AutomationWorker\Private;D:\UE_5.5\Engine\Source\Runtime\AVEncoder\Private;D:\UE_5.5\Engine\Source\Runtime\AVIWriter\Private;D:\UE_5.5\Engine\Source\Runtime\BlueprintRuntime\Private;D:\UE_5.5\Engine\Source\Runtime\BuildSettings\Private;D:\UE_5.5\Engine\Source\Runtime\Cbor\Private;D:\UE_5.5\Engine\Source\Runtime\Cbor\Tests;D:\UE_5.5\Engine\Source\Runtime\CEF3Utils\Private;D:\UE_5.5\Engine\Source\Runtime\CinematicCamera\Private;D:\UE_5.5\Engine\Source\Runtime\ClientPilot\Private;D:\UE_5.5\Engine\Source\Runtime\ClothingSystemRuntimeCommon\Private;D:\UE_5.5\Engine\Source\Runtime\ClothingSystemRuntimeInterface\Private;D:\UE_5.5\Engine\Source\Runtime\ClothingSystemRuntimeNv\Private;D:\UE_5.5\Engine\Source\Runtime\ColorManagement\Private;D:\UE_5.5\Engine\Source\Runtime\CookOnTheFly\Private;D:\UE_5.5\Engine\Source\Runtime\Core\Tests;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Tests;D:\UE_5.5\Engine\Source\Runtime\CrashReportCore\Private;D:\UE_5.5\Engine\Source\Runtime\D3D12RHI\Private;D:\UE_5.5\Engine\Source\Runtime\DeveloperSettings\Private;D:\UE_5.5\Engine\Source\Runtime\Engine\Private;D:\UE_5.5\Engine\Source\Runtime\EngineMessages\Private;D:\UE_5.5\Engine\Source\Runtime\EngineSettings\Private;D:\UE_5.5\Engine\Source\Runtime\ExternalRPCRegistry\Private;D:\UE_5.5\Engine\Source\Runtime\EyeTracker\Private;D:\UE_5.5\Engine\Source\Runtime\FieldNotification\Private;D:\UE_5.5\Engine\Source\Runtime\Foliage\Private;D:\UE_5.5\Engine\Source\Runtime\FriendsAndChat\Private;D:\UE_5.5\Engine\Source\Runtime\GameMenuBuilder\Private;D:\UE_5.5\Engine\Source\Runtime\GameplayDebugger\Private;D:\UE_5.5\Engine\Source\Runtime\GameplayMediaEncoder\Private;D:\UE_5.5\Engine\Source\Runtime\GameplayTags\Private;D:\UE_5.5\Engine\Source\Runtime\GameplayTasks\Private;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private;D:\UE_5.5\Engine\Source\Runtime\GeometryFramework\Private;D:\UE_5.5\Engine\Source\Runtime\HardwareSurvey\Private;D:\UE_5.5\Engine\Source\Runtime\HeadMountedDisplay\Private;D:\UE_5.5\Engine\Source\Runtime\IESFile\Private;D:\UE_5.5\Engine\Source\Runtime\ImageCore\Private;D:\UE_5.5\Engine\Source\Runtime\ImageWrapper\Private;D:\UE_5.5\Engine\Source\Runtime\ImageWriteQueue\Private;D:\UE_5.5\Engine\Source\Runtime\InputCore\Private;D:\UE_5.5\Engine\Source\Runtime\InputDevice\Private;D:\UE_5.5\Engine\Source\Runtime\InstallBundleManager\Private;D:\UE_5.5\Engine\Source\Runtime\InteractiveToolsFramework\Private;D:\UE_5.5\Engine\Source\Runtime\IPC\Private;D:\UE_5.5\Engine\Source\Runtime\Json\Private;D:\UE_5.5\Engine\Source\Runtime\JsonUtilities\Private;D:\UE_5.5\Engine\Source\Runtime\Landscape\Private;D:\UE_5.5\Engine\Source\Runtime\Launch\Private;D:\UE_5.5\Engine\Source\Runtime\LevelSequence\Private;D:\UE_5.5\Engine\Source\Runtime\LiveLinkAnimationCore\Private;D:\UE_5.5\Engine\Source\Runtime\LiveLinkInterface\Private;D:\UE_5.5\Engine\Source\Runtime\LiveLinkMessageBusFramework\Private;D:\UE_5.5\Engine\Source\Runtime\MassEntity\Private;D:\UE_5.5\Engine\Source\Runtime\MaterialShaderQualitySettings\Private;D:\UE_5.5\Engine\Source\Runtime\Media\Private;D:\UE_5.5\Engine\Source\Runtime\MediaAssets\Private;D:\UE_5.5\Engine\Source\Runtime\MediaUtils\Private;D:\UE_5.5\Engine\Source\Runtime\MeshConversion\Private;D:\UE_5.5\Engine\Source\Runtime\MeshConversionEngineTypes\Private;D:\UE_5.5\Engine\Source\Runtime\MeshDescription\Private;D:\UE_5.5\Engine\Source\Runtime\MeshUtilitiesCommon\Private;D:\UE_5.5\Engine\Source\Runtime\Messaging\Private;D:\UE_5.5\Engine\Source\Runtime\MessagingCommon\Private;D:\UE_5.5\Engine\Source\Runtime\MessagingRpc\Private;D:\UE_5.5\Engine\Source\Runtime\MoviePlayer\Private;D:\UE_5.5\Engine\Source\Runtime\MoviePlayerProxy\Private;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private;D:\UE_5.5\Engine\Source\Runtime\MovieSceneCapture\Private;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private;D:\UE_5.5\Engine\Source\Runtime\MRMesh\Private;D:\UE_5.5\Engine\Source\Runtime\MRMesh\Public;D:\UE_5.5\Engine\Source\Runtime\NavigationSystem\Private;D:\UE_5.5\Engine\Source\Runtime\Navmesh\Private;D:\UE_5.5\Engine\Source\Runtime\NetworkFile\Private;D:\UE_5.5\Engine\Source\Runtime\NetworkFileSystem\Private;D:\UE_5.5\Engine\Source\Runtime\Networking\Private;D:\UE_5.5\Engine\Source\Runtime\NNE\Private;D:\UE_5.5\Engine\Source\Runtime\NonRealtimeAudioRenderer\Private;D:\UE_5.5\Engine\Source\Runtime\NullDrv\Private;D:\UE_5.5\Engine\Source\Runtime\NullInstallBundleManager\Private;D:\UE_5.5\Engine\Source\Runtime\OpenColorIOWrapper\Private;D:\UE_5.5\Engine\Source\Runtime\OpenGLDrv\Private;D:\UE_5.5\Engine\Source\Runtime\Overlay\Private;D:\UE_5.5\Engine\Source\Runtime\PakFile\Private;D:\UE_5.5\Engine\Source\Runtime\PerfCounters\Private;D:\UE_5.5\Engine\Source\Runtime\PhysicsCore\Private;D:\UE_5.5\Engine\Source\Runtime\PreLoadScreen\Private;D:\UE_5.5\Engine\Source\Runtime\Projects\Private;D:\UE_5.5\Engine\Source\Runtime\PropertyPath\Private;D:\UE_5.5\Engine\Source\Runtime\RawMesh\Private;D:\UE_5.5\Engine\Source\Runtime\RenderCore\Private;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private;D:\UE_5.5\Engine\Source\Runtime\RewindDebuggerRuntimeInterface\Private;D:\UE_5.5\Engine\Source\Runtime\RHI\Private;D:\UE_5.5\Engine\Source\Runtime\RHICore\Private;D:\UE_5.5\Engine\Source\Runtime\RSA\Private;D:\UE_5.5\Engine\Source\Runtime\RuntimeAssetCache\Private;D:\UE_5.5\Engine\Source\Runtime\SandboxFile\Private;D:\UE_5.5\Engine\Source\Runtime\Serialization\Private;D:\UE_5.5\Engine\Source\Runtime\SessionMessages\Private;D:\UE_5.5\Engine\Source\Runtime\SessionServices\Private;D:\UE_5.5\Engine\Source\Runtime\SignalProcessing\Private;D:\UE_5.5\Engine\Source\Runtime\SkeletalMeshDescription\Private;D:\UE_5.5\Engine\Source\Runtime\Slate\Private;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private;D:\UE_5.5\Engine\Source\Runtime\SlateNullRenderer\Private;D:\UE_5.5\Engine\Source\Runtime\SlateRHIRenderer\Private;D:\UE_5.5\Engine\Source\Runtime\Sockets\Private;D:\UE_5.5\Engine\Source\Runtime\SoundFieldRendering\Private;D:\UE_5.5\Engine\Source\Runtime\StaticMeshDescription\Private;D:\UE_5.5\Engine\Source\Runtime\StorageServerClient\Private;D:\UE_5.5\Engine\Source\Runtime\StorageServerClientDebug\Private;D:\UE_5.5\Engine\Source\Runtime\StreamingFile\Private;D:\UE_5.5\Engine\Source\Runtime\StreamingPauseRendering\Private;D:\UE_5.5\Engine\Source\Runtime\SynthBenchmark\Private;D:\UE_5.5\Engine\Source\Runtime\TextureUtilitiesCommon\Private;D:\UE_5.5\Engine\Source\Runtime\TimeManagement\Private;D:\UE_5.5\Engine\Source\Runtime\Toolbox\Private;D:\UE_5.5\Engine\Source\Runtime\TypedElementFramework\Private;D:\UE_5.5\Engine\Source\Runtime\TypedElementRuntime\Private;D:\UE_5.5\Engine\Source\Runtime\UELibrary\Private;D:\UE_5.5\Engine\Source\Runtime\UMG\Private;D:\UE_5.5\Engine\Source\Runtime\UniversalObjectLocator\Private;D:\UE_5.5\Engine\Source\Runtime\UnrealGame\Private;D:\UE_5.5\Engine\Source\Runtime\VectorVM\Private;D:\UE_5.5\Engine\Source\Runtime\VirtualFileCache\Private;D:\UE_5.5\Engine\Source\Runtime\VulkanRHI\Private;D:\UE_5.5\Engine\Source\Runtime\WebBrowser\Private;D:\UE_5.5\Engine\Source\Runtime\WebBrowserTexture\Private;D:\UE_5.5\Engine\Source\Runtime\WidgetCarousel\Private;D:\UE_5.5\Engine\Source\Runtime\XmlParser\Private;D:\UE_5.5\Engine\Source\ThirdParty\Android\detex;D:\UE_5.5\Engine\Source\ThirdParty\HWCPipe\include;D:\UE_5.5\Engine\Source\ThirdParty\libSampleRate\Private;D:\UE_5.5\Engine\Source\ThirdParty\nanosvg\src;D:\UE_5.5\Engine\Source\Developer\AITestSuite\Private\BehaviorTree;D:\UE_5.5\Engine\Source\Developer\AITestSuite\Private\MockAI;D:\UE_5.5\Engine\Source\Developer\AITestSuite\Private\Tests;D:\UE_5.5\Engine\Source\Developer\Android\AndroidDeviceDetection\Private;D:\UE_5.5\Engine\Source\Developer\Android\AndroidPlatformEditor\Private;D:\UE_5.5\Engine\Source\Developer\Android\AndroidTargetPlatform\Private;D:\UE_5.5\Engine\Source\Developer\Android\AndroidTargetPlatformControls\Private;D:\UE_5.5\Engine\Source\Developer\Android\AndroidTargetPlatformSettings\Private;D:\UE_5.5\Engine\Source\Developer\Apple\MetalShaderFormat\Private;D:\UE_5.5\Engine\Source\Developer\AssetTools\Private\AssetTypeActions;D:\UE_5.5\Engine\Source\Developer\AutomationDriver\Private\Locators;D:\UE_5.5\Engine\Source\Developer\CQTest\Private\Commands;D:\UE_5.5\Engine\Source\Developer\CQTest\Private\Components;D:\UE_5.5\Engine\Source\Developer\CQTest\Private\Helpers;D:\UE_5.5\Engine\Source\Developer\CrashDebugHelper\Private\Android;D:\UE_5.5\Engine\Source\Developer\CrashDebugHelper\Private\IOS;D:\UE_5.5\Engine\Source\Developer\CrashDebugHelper\Private\Linux;D:\UE_5.5\Engine\Source\Developer\CrashDebugHelper\Private\Mac;D:\UE_5.5\Engine\Source\Developer\CrashDebugHelper\Private\Windows;D:\UE_5.5\Engine\Source\Developer\Datasmith\DatasmithExporter\Private;D:\UE_5.5\Engine\Source\Developer\Datasmith\DatasmithExporterUI\Private;D:\UE_5.5\Engine\Source\Developer\Datasmith\DatasmithFacade\Private;D:\UE_5.5\Engine\Source\Developer\DerivedDataCache\Private\Http;D:\UE_5.5\Engine\Source\Developer\DerivedDataCache\Private\Tests;D:\UE_5.5\Engine\Source\Developer\DesktopPlatform\Private\Linux;D:\UE_5.5\Engine\Source\Developer\DesktopPlatform\Private\Mac;D:\UE_5.5\Engine\Source\Developer\DesktopPlatform\Private\Null;D:\UE_5.5\Engine\Source\Developer\DesktopPlatform\Private\Windows;D:\UE_5.5\Engine\Source\Developer\DeviceManager\Private\Widgets;D:\UE_5.5\Engine\Source\Developer\DirectoryWatcher\Private\Linux;D:\UE_5.5\Engine\Source\Developer\DirectoryWatcher\Private\Mac;D:\UE_5.5\Engine\Source\Developer\DirectoryWatcher\Private\Tests;D:\UE_5.5\Engine\Source\Developer\DirectoryWatcher\Private\Windows;D:\UE_5.5\Engine\Source\Developer\FileUtilities\Private\Tests;D:\UE_5.5\Engine\Source\Developer\FunctionalTesting\Private\Tests;D:\UE_5.5\Engine\Source\Developer\Horde\Private\Compute;D:\UE_5.5\Engine\Source\Developer\Horde\Private\Server;D:\UE_5.5\Engine\Source\Developer\Horde\Private\Storage;D:\UE_5.5\Engine\Source\Developer\IOS\IOSPlatformEditor\Private;D:\UE_5.5\Engine\Source\Developer\IOS\IOSTargetPlatform\Private;D:\UE_5.5\Engine\Source\Developer\IOS\IOSTargetPlatformControls\Private;D:\UE_5.5\Engine\Source\Developer\IOS\IOSTargetPlatformSettings\Private;D:\UE_5.5\Engine\Source\Developer\IOS\TVOSTargetPlatform\Private;D:\UE_5.5\Engine\Source\Developer\IOS\TVOSTargetPlatformControls\Private;D:\UE_5.5\Engine\Source\Developer\IOS\TVOSTargetPlatformSettings\Private;D:\UE_5.5\Engine\Source\Developer\LauncherServices\Private\Launcher;D:\UE_5.5\Engine\Source\Developer\LauncherServices\Private\Profiles;D:\UE_5.5\Engine\Source\Developer\Linux\LinuxArm64TargetPlatform\Private;D:\UE_5.5\Engine\Source\Developer\Linux\LinuxArm64TargetPlatformControls\Private;D:\UE_5.5\Engine\Source\Developer\Linux\LinuxArm64TargetPlatformSettings\Private;D:\UE_5.5\Engine\Source\Developer\Linux\LinuxPlatformEditor\Private;D:\UE_5.5\Engine\Source\Developer\Linux\LinuxTargetPlatform\Private;D:\UE_5.5\Engine\Source\Developer\Linux\LinuxTargetPlatformControls\Private;D:\UE_5.5\Engine\Source\Developer\Linux\LinuxTargetPlatformSettings\Private;D:\UE_5.5\Engine\Source\Developer\Localization\Private\Serialization;D:\UE_5.5\Engine\Source\Developer\LowLevelTestsRunner\Private\TestCommon;D:\UE_5.5\Engine\Source\Developer\LowLevelTestsRunner\Private\TestListeners;D:\UE_5.5\Engine\Source\Developer\LowLevelTestsRunner\Private\TestStubs;D:\UE_5.5\Engine\Source\Developer\Mac\MacPlatformEditor\Private;D:\UE_5.5\Engine\Source\Developer\Mac\MacTargetPlatform\Private;D:\UE_5.5\Engine\Source\Developer\Mac\MacTargetPlatformControls\Private;D:\UE_5.5\Engine\Source\Developer\Mac\MacTargetPlatformSettings\Private;D:\UE_5.5\Engine\Source\Developer\MessageLog\Private\Model;D:\UE_5.5\Engine\Source\Developer\MessageLog\Private\Presentation;D:\UE_5.5\Engine\Source\Developer\MessageLog\Private\UserInterface;D:\UE_5.5\Engine\Source\Developer\PakFileUtilities\Private\Tests;D:\UE_5.5\Engine\Source\Developer\Profiler\Private\Widgets;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets;D:\UE_5.5\Engine\Source\Developer\ScreenShotComparison\Private\Models;D:\UE_5.5\Engine\Source\Developer\ScreenShotComparison\Private\Widgets;D:\UE_5.5\Engine\Source\Developer\SessionFrontend\Private\Widgets;D:\UE_5.5\Engine\Source\Developer\SettingsEditor\Private\Customizations;D:\UE_5.5\Engine\Source\Developer\SettingsEditor\Private\Widgets;D:\UE_5.5\Engine\Source\Developer\ShaderCompilerCommon\Private\ISAParser;D:\UE_5.5\Engine\Source\Developer\SlateReflector\Private\Models;D:\UE_5.5\Engine\Source\Developer\SlateReflector\Private\Styling;D:\UE_5.5\Engine\Source\Developer\SlateReflector\Private\Widgets;D:\UE_5.5\Engine\Source\Developer\SourceControl\Private\RevisionControlStyle;D:\UE_5.5\Engine\Source\Developer\StandaloneRenderer\Private\IOS;D:\UE_5.5\Engine\Source\Developer\StandaloneRenderer\Private\OpenGL;D:\UE_5.5\Engine\Source\Developer\TargetDeviceServices\Private\Proxies;D:\UE_5.5\Engine\Source\Developer\TargetDeviceServices\Private\Services;D:\UE_5.5\Engine\Source\Developer\ToolWidgets\Private\Dialog;D:\UE_5.5\Engine\Source\Developer\ToolWidgets\Private\Filters;D:\UE_5.5\Engine\Source\Developer\ToolWidgets\Private\Sidebar;D:\UE_5.5\Engine\Source\Developer\TraceAnalysis\Private\Analysis;D:\UE_5.5\Engine\Source\Developer\TraceAnalysis\Private\Asio;D:\UE_5.5\Engine\Source\Developer\TraceAnalysis\Private\Store;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights;D:\UE_5.5\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore;D:\UE_5.5\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend;D:\UE_5.5\Engine\Source\Developer\TraceServices\Private\Analyzers;D:\UE_5.5\Engine\Source\Developer\TraceServices\Private\Common;D:\UE_5.5\Engine\Source\Developer\TraceServices\Private\Model;D:\UE_5.5\Engine\Source\Developer\TraceServices\Private\Modules;D:\UE_5.5\Engine\Source\Developer\TraceServices\Private\Tests;D:\UE_5.5\Engine\Source\Developer\TraceTools\Private\Models;D:\UE_5.5\Engine\Source\Developer\TraceTools\Private\Services;D:\UE_5.5\Engine\Source\Developer\TraceTools\Private\Widgets;D:\UE_5.5\Engine\Source\Developer\UndoHistory\Private\Widgets;D:\UE_5.5\Engine\Source\Developer\UnsavedAssetsTracker\Source\Private;D:\UE_5.5\Engine\Source\Developer\WidgetRegistration\Private\Common;D:\UE_5.5\Engine\Source\Developer\WidgetRegistration\Private\DataVisualization;D:\UE_5.5\Engine\Source\Developer\WidgetRegistration\Private\Inputs;D:\UE_5.5\Engine\Source\Developer\WidgetRegistration\Private\Layout;D:\UE_5.5\Engine\Source\Developer\WidgetRegistration\Private\Persistence;D:\UE_5.5\Engine\Source\Developer\WidgetRegistration\Private\Styles;D:\UE_5.5\Engine\Source\Developer\WidgetRegistration\Public\Inputs;D:\UE_5.5\Engine\Source\Developer\Windows\LiveCoding\Private;D:\UE_5.5\Engine\Source\Developer\Windows\LiveCodingServer\Private;D:\UE_5.5\Engine\Source\Developer\Windows\ShaderFormatD3D\Private;D:\UE_5.5\Engine\Source\Developer\Windows\WindowsPlatformEditor\Private;D:\UE_5.5\Engine\Source\Developer\Windows\WindowsTargetPlatfomControls\Private;D:\UE_5.5\Engine\Source\Developer\Windows\WindowsTargetPlatform\Private;D:\UE_5.5\Engine\Source\Developer\Windows\WindowsTargetPlatformSettings\Private;D:\UE_5.5\Engine\Source\Editor\AddContentDialog\Private\ViewModels;D:\UE_5.5\Engine\Source\Editor\AnimationBlueprintEditor\Private\AnimationNodes;D:\UE_5.5\Engine\Source\Editor\AnimationBlueprintEditor\Private\AnimationPins;D:\UE_5.5\Engine\Source\Editor\AnimationBlueprintEditor\Private\AnimationStateNodes;D:\UE_5.5\Engine\Source\Editor\AnimationEditorWidgets\Private\SchematicGraphPanel;D:\UE_5.5\Engine\Source\Editor\AnimGraph\Private\EditModes;D:\UE_5.5\Engine\Source\Editor\AudioEditor\Private\AssetTypeActions;D:\UE_5.5\Engine\Source\Editor\AudioEditor\Private\Editors;D:\UE_5.5\Engine\Source\Editor\AudioEditor\Private\Factories;D:\UE_5.5\Engine\Source\Editor\BehaviorTreeEditor\Private\DetailCustomizations;D:\UE_5.5\Engine\Source\Editor\BlueprintGraph\Private\Tests;D:\UE_5.5\Engine\Source\Editor\Cascade\Private\Tests;D:\UE_5.5\Engine\Source\Editor\ConfigEditor\Private\PropertyVisualization;D:\UE_5.5\Engine\Source\Editor\ContentBrowser\Private\AssetView;D:\UE_5.5\Engine\Source\Editor\ContentBrowser\Private\Experimental;D:\UE_5.5\Engine\Source\Editor\CurveEditor\Private\DragOperations;D:\UE_5.5\Engine\Source\Editor\CurveEditor\Private\Filters;D:\UE_5.5\Engine\Source\Editor\CurveEditor\Private\Tree;D:\UE_5.5\Engine\Source\Editor\CurveEditor\Private\Views;D:\UE_5.5\Engine\Source\Editor\DataLayerEditor\Private\DataLayer;D:\UE_5.5\Engine\Source\Editor\DeviceProfileEditor\Private\DetailsPanel;D:\UE_5.5\Engine\Source\Editor\EditorConfig\Private\Tests;D:\UE_5.5\Engine\Source\Editor\EditorFramework\Private\Factories;D:\UE_5.5\Engine\Source\Editor\EditorFramework\Private\Subsystems;D:\UE_5.5\Engine\Source\Editor\EditorFramework\Private\Toolkits;D:\UE_5.5\Engine\Source\Editor\EditorFramework\Private\Tools;D:\UE_5.5\Engine\Source\Editor\EditorFramework\Private\Viewports;D:\UE_5.5\Engine\Source\Editor\EditorWidgets\Private\Filters;D:\UE_5.5\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Private;D:\UE_5.5\Engine\Source\Editor\GameProjectGeneration\Private\Tests;D:\UE_5.5\Engine\Source\Editor\GraphEditor\Private\KismetNodes;D:\UE_5.5\Engine\Source\Editor\GraphEditor\Private\KismetPins;D:\UE_5.5\Engine\Source\Editor\GraphEditor\Private\MaterialNodes;D:\UE_5.5\Engine\Source\Editor\GraphEditor\Private\MaterialPins;D:\UE_5.5\Engine\Source\Editor\InputBindingEditor\Private\Widgets;D:\UE_5.5\Engine\Source\Editor\Kismet\Private\Blueprints;D:\UE_5.5\Engine\Source\Editor\Kismet\Private\Debugging;D:\UE_5.5\Engine\Source\Editor\Kismet\Private\ProjectUtilities;D:\UE_5.5\Engine\Source\Editor\Kismet\Private\Tests;D:\UE_5.5\Engine\Source\Editor\LandscapeEditor\Private\Tests;D:\UE_5.5\Engine\Source\Editor\LevelEditor\Private\ViewportToolbar;D:\UE_5.5\Engine\Source\Editor\MainFrame\Private\Frame;D:\UE_5.5\Engine\Source\Editor\MainFrame\Private\Menus;D:\UE_5.5\Engine\Source\Editor\MaterialEditor\Private\Tabs;D:\UE_5.5\Engine\Source\Editor\MaterialEditor\Private\Tests;D:\UE_5.5\Engine\Source\Editor\MergeActors\Private\MergeProxyUtils;D:\UE_5.5\Engine\Source\Editor\MergeActors\Private\MeshApproximationTool;D:\UE_5.5\Engine\Source\Editor\MergeActors\Private\MeshInstancingTool;D:\UE_5.5\Engine\Source\Editor\MergeActors\Private\MeshMergingTool;D:\UE_5.5\Engine\Source\Editor\MergeActors\Private\MeshProxyTool;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\Bindings;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\Channels;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\Conditions;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\Constraints;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\CurveKeyEditors;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\EditModes;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\FCPXML;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\MVVM;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\Sections;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\TrackEditors;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\TrackEditorThumbnail;D:\UE_5.5\Engine\Source\Editor\OverlayEditor\Private\Factories;D:\UE_5.5\Engine\Source\Editor\Persona\Private\AnimTimeline;D:\UE_5.5\Engine\Source\Editor\Persona\Private\Customization;D:\UE_5.5\Engine\Source\Editor\Persona\Private\Shared;D:\UE_5.5\Engine\Source\Editor\PhysicsAssetEditor\Private\PhysicsAssetGraph;D:\UE_5.5\Engine\Source\Editor\ProjectTargetPlatformEditor\Private\Widgets;D:\UE_5.5\Engine\Source\Editor\PropertyEditor\Private\Tests;D:\UE_5.5\Engine\Source\Editor\ScriptableEditorWidgets\Private\Components;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Capabilities;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Filters;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Menus;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Misc;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\MVVM;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Scripting;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Tools;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Widgets;D:\UE_5.5\Engine\Source\Editor\SequencerCore\Private\MVVM;D:\UE_5.5\Engine\Source\Editor\SequencerCore\Private\Scripting;D:\UE_5.5\Engine\Source\Editor\SequenceRecorder\Private\Sections;D:\UE_5.5\Engine\Source\Editor\StatsViewer\Private\StatsEntries;D:\UE_5.5\Engine\Source\Editor\StatsViewer\Private\StatsPages;D:\UE_5.5\Engine\Source\Editor\StructUtilsEditor\Private\Customizations;D:\UE_5.5\Engine\Source\Editor\TextureEditor\Private\Customizations;D:\UE_5.5\Engine\Source\Editor\TextureEditor\Private\Models;D:\UE_5.5\Engine\Source\Editor\TextureEditor\Private\Widgets;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Animation;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\BlueprintModes;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Components;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Customizations;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Designer;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Details;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\DragDrop;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Extensions;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\FieldNotification;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Graph;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Hierarchy;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Library;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Navigation;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Nodes;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Palette;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Preview;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Settings;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\TabFactory;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Templates;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\ToolPalette;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Utility;D:\UE_5.5\Engine\Source\Editor\UMGEditor\Private\Widgets;D:\UE_5.5\Engine\Source\Editor\UndoHistoryEditor\Private\Widgets;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Analytics;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Animation;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\AutoReimport;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Bookmarks;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Commandlets;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Cooker;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Dialogs;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\DragAndDrop;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Editor;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\EditorDomain;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Factories;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Fbx;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Features;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\ImportUtils;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Instances;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Kismet2;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Layers;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Lightmass;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\MaterialEditor;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Settings;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\StaticLightingSystem;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Subsystems;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\TargetDomain;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Tests;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Text;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\ThumbnailRendering;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Toolkits;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Tools;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\ViewportToolbar;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\WorkflowOrientedApp;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\WorldPartition;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Public\Elements;D:\UE_5.5\Engine\Source\Editor\ViewportInteraction\Private\Gizmo;D:\UE_5.5\Engine\Source\Editor\VREditor\Private\Teleporter;D:\UE_5.5\Engine\Source\Editor\VREditor\Private\UI;D:\UE_5.5\Engine\Source\Editor\WorldBrowser\Private\StreamingLevels;D:\UE_5.5\Engine\Source\Editor\WorldBrowser\Private\Tiles;D:\UE_5.5\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition;D:\UE_5.5\Engine\Source\Runtime\AdpcmAudioDecoder\Module\Private;D:\UE_5.5\Engine\Source\Runtime\AdvancedWidgets\Private\Components;D:\UE_5.5\Engine\Source\Runtime\AdvancedWidgets\Private\Slate;D:\UE_5.5\Engine\Source\Runtime\AdvancedWidgets\Private\Styling;D:\UE_5.5\Engine\Source\Runtime\AdvancedWidgets\Private\Util;D:\UE_5.5\Engine\Source\Runtime\Advertising\Advertising\Private;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\Actions;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\BehaviorTree;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\Blueprint;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\DataProviders;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\GameplayDebugger;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\HotSpots;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\Navigation;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\Perception;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\Tasks;D:\UE_5.5\Engine\Source\Runtime\Analytics\Analytics\Private;D:\UE_5.5\Engine\Source\Runtime\Analytics\AnalyticsET\Private;D:\UE_5.5\Engine\Source\Runtime\Analytics\AnalyticsSwrve\Private;D:\UE_5.5\Engine\Source\Runtime\Analytics\AnalyticsVisualEditing\Private;D:\UE_5.5\Engine\Source\Runtime\Analytics\TelemetryUtils\Private;D:\UE_5.5\Engine\Source\Runtime\Analytics\TelemetryUtils\Tests;D:\UE_5.5\Engine\Source\Runtime\Android\AndroidLocalNotification\Private;D:\UE_5.5\Engine\Source\Runtime\Android\AndroidRuntimeSettings\Private;D:\UE_5.5\Engine\Source\Runtime\Android\AudioMixerAndroid\Private;D:\UE_5.5\Engine\Source\Runtime\AnimationCore\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\AnimGraphRuntime\Private\AnimNodes;D:\UE_5.5\Engine\Source\Runtime\AnimGraphRuntime\Private\BoneControllers;D:\UE_5.5\Engine\Source\Runtime\AnimGraphRuntime\Private\RBF;D:\UE_5.5\Engine\Source\Runtime\Apple\AudioMixerAudioUnit\Private;D:\UE_5.5\Engine\Source\Runtime\Apple\AudioMixerCoreAudio\Private;D:\UE_5.5\Engine\Source\Runtime\Apple\MetalRHI\Private;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\Android;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\Apple;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\GenericPlatform;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\HAL;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\IOS;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\Linux;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\Mac;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\Null;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\Unix;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\AudioCaptureImplementations\AudioCaptureRtAudio\Private;D:\UE_5.5\Engine\Source\Runtime\AudioLink\AudioLinkEngine\Private;D:\UE_5.5\Engine\Source\Runtime\AudioLink\AudioMixerPlatformAudioLink\Private;D:\UE_5.5\Engine\Source\Runtime\AudioMixer\Private\Components;D:\UE_5.5\Engine\Source\Runtime\AudioMixer\Private\Effects;D:\UE_5.5\Engine\Source\Runtime\AudioMixer\Private\Generators;D:\UE_5.5\Engine\Source\Runtime\AudioMixer\Private\Quartz;D:\UE_5.5\Engine\Source\Runtime\AudioMixer\Private\SoundFileIO;D:\UE_5.5\Engine\Source\Runtime\AVEncoder\Private\Decoders;D:\UE_5.5\Engine\Source\Runtime\AVEncoder\Private\Encoders;D:\UE_5.5\Engine\Source\Runtime\BinkAudioDecoder\Module\Private;D:\UE_5.5\Engine\Source\Runtime\CEF3Utils\Private\Mac;D:\UE_5.5\Engine\Source\Runtime\ClothingSystemRuntimeCommon\Private\Utils;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Android;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Apple;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Async;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Audio;D:\UE_5.5\Engine\Source\Runtime\Core\Private\AutoRTFM;D:\UE_5.5\Engine\Source\Runtime\Core\Private\ColorManagement;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Compression;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Containers;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Delegates;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Experimental;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Features;D:\UE_5.5\Engine\Source\Runtime\Core\Private\FileCache;D:\UE_5.5\Engine\Source\Runtime\Core\Private\FramePro;D:\UE_5.5\Engine\Source\Runtime\Core\Private\GenericPlatform;D:\UE_5.5\Engine\Source\Runtime\Core\Private\HAL;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Hash;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Internationalization;D:\UE_5.5\Engine\Source\Runtime\Core\Private\IO;D:\UE_5.5\Engine\Source\Runtime\Core\Private\IOS;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Linux;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Logging;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Mac;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Math;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Memory;D:\UE_5.5\Engine\Source\Runtime\Core\Private\MemPro;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Microsoft;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Misc;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Modules;D:\UE_5.5\Engine\Source\Runtime\Core\Private\ProfilingDebugging;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Serialization;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Stats;D:\UE_5.5\Engine\Source\Runtime\Core\Private\String;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Tasks;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Templates;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Unix;D:\UE_5.5\Engine\Source\Runtime\Core\Private\UObject;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Virtualization;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Algo;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Async;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Compression;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Containers;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Delegates;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\GenericPlatform;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\HAL;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Hash;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Internationalization;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\IO;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Math;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Memory;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Misc;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Serialization;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\String;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Tasks;D:\UE_5.5\Engine\Source\Runtime\Core\Tests\Templates;D:\UE_5.5\Engine\Source\Runtime\CoreOnline\Private\Online;D:\UE_5.5\Engine\Source\Runtime\CoreOnline\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\CorePreciseFP\Private\Math;D:\UE_5.5\Engine\Source\Runtime\CorePreciseFP\Private\VerseVM;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\AssetRegistry;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\Blueprint;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\Cooker;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\Internationalization;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\Misc;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\Serialization;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\StructUtils;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\Templates;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\UObject;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\VerseVM;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Public\VerseVM;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Tests\Serialization;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Tests\UObject;D:\UE_5.5\Engine\Source\Runtime\CrashReportCore\Private\Android;D:\UE_5.5\Engine\Source\Runtime\CrashReportCore\Private\IOS;D:\UE_5.5\Engine\Source\Runtime\CrashReportCore\Private\Mac;D:\UE_5.5\Engine\Source\Runtime\CrashReportCore\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\CUDA\Source\Private;D:\UE_5.5\Engine\Source\Runtime\D3D12RHI\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\Datasmith\DatasmithCore\Private;D:\UE_5.5\Engine\Source\Runtime\Datasmith\DirectLink\Private;D:\UE_5.5\Engine\Source\Runtime\DeveloperSettings\Private\Engine;D:\UE_5.5\Engine\Source\Runtime\Engine\Classes\Animation;D:\UE_5.5\Engine\Source\Runtime\Engine\Classes\Engine;D:\UE_5.5\Engine\Source\Runtime\Engine\Classes\Sound;D:\UE_5.5\Engine\Source\Runtime\Engine\Internal\Materials;D:\UE_5.5\Engine\Source\Runtime\Engine\Internal\Streaming;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\ActorEditorContext;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\ActorPartition;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\AI;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Analytics;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Animation;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Atmosphere;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Audio;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Camera;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Collision;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Commandlets;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Components;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Curves;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\DataDrivenCVars;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Debug;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\DeviceProfiles;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\EdGraph;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\EditorFramework;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Engine;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\FieldNotification;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\GameFramework;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\HLOD;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\HLSLTree;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\InstancedStaticMesh;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Instances;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Internationalization;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\ISMPartition;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Kismet;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Layers;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\LevelInstance;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Materials;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\MeshMerge;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\MeshVertexPainter;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Misc;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Net;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\ODSC;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\PackedLevelActor;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\PacketHandlers;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Particles;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Performance;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\PhysicsEngine;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\PhysicsField;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\ProfilingDebugging;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Rendering;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Shader;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\ShaderCompiler;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Slate;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\SparseVolumeTexture;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Streaming;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Subsystems;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\UniversalObjectLocators;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\UserInterface;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Vehicles;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\VisualLogger;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\VT;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition;D:\UE_5.5\Engine\Source\Runtime\Engine\Public\Rendering;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private;D:\UE_5.5\Engine\Source\Runtime\Experimental\ChaosCore\Private;D:\UE_5.5\Engine\Source\Runtime\Experimental\ChaosVDData\Private;D:\UE_5.5\Engine\Source\Runtime\Experimental\ChaosVisualDebugger\Private;D:\UE_5.5\Engine\Source\Runtime\Experimental\GeometryCollectionEngine\Private;D:\UE_5.5\Engine\Source\Runtime\Experimental\JsonObjectGraph\Private;D:\UE_5.5\Engine\Source\Runtime\FieldNotification\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\GameplayTags\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\GameplayTasks\Private\Tasks;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\Clustering;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\CompGeom;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\DynamicMesh;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\Generators;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\Implicit;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\Intersection;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\Operations;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\Parameterization;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\Sampling;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\Selections;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\Spatial;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\Util;D:\UE_5.5\Engine\Source\Runtime\GeometryFramework\Private\Changes;D:\UE_5.5\Engine\Source\Runtime\GeometryFramework\Private\Components;D:\UE_5.5\Engine\Source\Runtime\ImageWrapper\Private\Formats;D:\UE_5.5\Engine\Source\Runtime\InputCore\Private\Android;D:\UE_5.5\Engine\Source\Runtime\InputCore\Private\GenericPlatform;D:\UE_5.5\Engine\Source\Runtime\InputCore\Private\IOS;D:\UE_5.5\Engine\Source\Runtime\InputCore\Private\Linux;D:\UE_5.5\Engine\Source\Runtime\InputCore\Private\Mac;D:\UE_5.5\Engine\Source\Runtime\InputCore\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\InteractiveToolsFramework\Private\BaseBehaviors;D:\UE_5.5\Engine\Source\Runtime\InteractiveToolsFramework\Private\BaseGizmos;D:\UE_5.5\Engine\Source\Runtime\InteractiveToolsFramework\Private\BaseTools;D:\UE_5.5\Engine\Source\Runtime\InteractiveToolsFramework\Private\Changes;D:\UE_5.5\Engine\Source\Runtime\InteractiveToolsFramework\Private\SceneQueries;D:\UE_5.5\Engine\Source\Runtime\InteractiveToolsFramework\Private\ToolTargets;D:\UE_5.5\Engine\Source\Runtime\Interchange\Core\Private;D:\UE_5.5\Engine\Source\Runtime\Interchange\Engine\Private;D:\UE_5.5\Engine\Source\Runtime\IOS\IOSAudio\Private;D:\UE_5.5\Engine\Source\Runtime\IOS\IOSLocalNotification\Private;D:\UE_5.5\Engine\Source\Runtime\IOS\IOSPlatformFeatures\Private;D:\UE_5.5\Engine\Source\Runtime\IOS\IOSRuntimeSettings\Private;D:\UE_5.5\Engine\Source\Runtime\IOS\LaunchDaemonMessages\Private;D:\UE_5.5\Engine\Source\Runtime\IOS\MarketplaceKit\Private;D:\UE_5.5\Engine\Source\Runtime\Json\Private\Dom;D:\UE_5.5\Engine\Source\Runtime\Json\Private\JsonUtils;D:\UE_5.5\Engine\Source\Runtime\Json\Private\Serialization;D:\UE_5.5\Engine\Source\Runtime\Json\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\Landscape\Private\Materials;D:\UE_5.5\Engine\Source\Runtime\Launch\Private\Android;D:\UE_5.5\Engine\Source\Runtime\Launch\Private\IOS;D:\UE_5.5\Engine\Source\Runtime\Launch\Private\Linux;D:\UE_5.5\Engine\Source\Runtime\Launch\Private\Mac;D:\UE_5.5\Engine\Source\Runtime\Launch\Private\Unix;D:\UE_5.5\Engine\Source\Runtime\Launch\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\Linux\AudioMixerSDL\Private;D:\UE_5.5\Engine\Source\Runtime\LiveLinkInterface\Private\Roles;D:\UE_5.5\Engine\Source\Runtime\MathCore\Private\Graph;D:\UE_5.5\Engine\Source\Runtime\MediaAssets\Private\Assets;D:\UE_5.5\Engine\Source\Runtime\MediaAssets\Private\Misc;D:\UE_5.5\Engine\Source\Runtime\MediaAssets\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\MeshDescription\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\Messaging\Private\Bridge;D:\UE_5.5\Engine\Source\Runtime\Messaging\Private\Bus;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Bindings;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Channels;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Compilation;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Conditions;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\EntitySystem;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Evaluation;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\EventHandlers;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Generators;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Sections;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Tracks;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Variants;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private\Bindings;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private\Channels;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private\Conditions;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private\Evaluation;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private\PreAnimatedState;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private\Sections;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private\Systems;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private\TrackInstances;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private\Tracks;D:\UE_5.5\Engine\Source\Runtime\NavigationSystem\Private\NavAreas;D:\UE_5.5\Engine\Source\Runtime\NavigationSystem\Private\NavFilters;D:\UE_5.5\Engine\Source\Runtime\NavigationSystem\Private\NavGraph;D:\UE_5.5\Engine\Source\Runtime\NavigationSystem\Private\NavMesh;D:\UE_5.5\Engine\Source\Runtime\NavigationSystem\Public\NavMesh;D:\UE_5.5\Engine\Source\Runtime\Navmesh\Private\DebugUtils;D:\UE_5.5\Engine\Source\Runtime\Navmesh\Private\Detour;D:\UE_5.5\Engine\Source\Runtime\Navmesh\Private\DetourCrowd;D:\UE_5.5\Engine\Source\Runtime\Navmesh\Private\DetourTileCache;D:\UE_5.5\Engine\Source\Runtime\Navmesh\Private\Recast;D:\UE_5.5\Engine\Source\Runtime\Networking\Private\IPv4;D:\UE_5.5\Engine\Source\Runtime\Networking\Private\Steam;D:\UE_5.5\Engine\Source\Runtime\Networking\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\NetworkReplayStreaming\HttpNetworkReplayStreaming\Private;D:\UE_5.5\Engine\Source\Runtime\NetworkReplayStreaming\InMemoryNetworkReplayStreaming\Private;D:\UE_5.5\Engine\Source\Runtime\NetworkReplayStreaming\LocalFileNetworkReplayStreaming\Private;D:\UE_5.5\Engine\Source\Runtime\NetworkReplayStreaming\NetworkReplayStreaming\Private;D:\UE_5.5\Engine\Source\Runtime\NetworkReplayStreaming\NullNetworkReplayStreaming\Private;D:\UE_5.5\Engine\Source\Runtime\NetworkReplayStreaming\SaveGameNetworkReplayStreaming\Private;D:\UE_5.5\Engine\Source\Runtime\Online\BackgroundHTTP\Private;D:\UE_5.5\Engine\Source\Runtime\Online\BackgroundHTTPFileHash\Private;D:\UE_5.5\Engine\Source\Runtime\Online\BuildPatchServices\Private;D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Private;D:\UE_5.5\Engine\Source\Runtime\Online\HTTPServer\Private;D:\UE_5.5\Engine\Source\Runtime\Online\ICMP\Private;D:\UE_5.5\Engine\Source\Runtime\Online\ImageDownload\Private;D:\UE_5.5\Engine\Source\Runtime\Online\SSL\Private;D:\UE_5.5\Engine\Source\Runtime\Online\Stomp\Private;D:\UE_5.5\Engine\Source\Runtime\Online\Voice\Private;D:\UE_5.5\Engine\Source\Runtime\Online\WebSockets\Private;D:\UE_5.5\Engine\Source\Runtime\Online\XMPP\Private;D:\UE_5.5\Engine\Source\Runtime\OpenColorIOWrapper\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\OpenGLDrv\Private\Android;D:\UE_5.5\Engine\Source\Runtime\OpenGLDrv\Private\Linux;D:\UE_5.5\Engine\Source\Runtime\OpenGLDrv\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\OpusAudioDecoder\Module\Private;D:\UE_5.5\Engine\Source\Runtime\Overlay\Private\Assets;D:\UE_5.5\Engine\Source\Runtime\Overlay\Private\Factories;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\PacketHandler\Private;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\ReliabilityHandlerComponent\Private;D:\UE_5.5\Engine\Source\Runtime\PlatformThirdPartyHelpers\PosixShim\Private;D:\UE_5.5\Engine\Source\Runtime\Portal\LauncherCheck\Private;D:\UE_5.5\Engine\Source\Runtime\Portal\LauncherPlatform\Private;D:\UE_5.5\Engine\Source\Runtime\Portal\Messages\Private;D:\UE_5.5\Engine\Source\Runtime\Portal\Proxies\Private;D:\UE_5.5\Engine\Source\Runtime\Portal\Rpc\Private;D:\UE_5.5\Engine\Source\Runtime\Portal\Services\Private;D:\UE_5.5\Engine\Source\Runtime\PropertyPath\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\RadAudioCodec\Module\Private;D:\UE_5.5\Engine\Source\Runtime\RenderCore\Private\Animation;D:\UE_5.5\Engine\Source\Runtime\RenderCore\Private\ProfilingDebugging;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\CompositionLighting;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\Froxel;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\HairStrands;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\HeterogeneousVolumes;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\InstanceCulling;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\Lumen;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\MegaLights;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\Nanite;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\OIT;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\PostProcess;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\RayTracing;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\SceneCulling;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\Shadows;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\Skinning;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\SparseVolumeTexture;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\StochasticLighting;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\Substrate;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\VariableRateShading;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\VirtualShadowMaps;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\VT;D:\UE_5.5\Engine\Source\Runtime\RHI\Private\Android;D:\UE_5.5\Engine\Source\Runtime\RHI\Private\Apple;D:\UE_5.5\Engine\Source\Runtime\RHI\Private\Linux;D:\UE_5.5\Engine\Source\Runtime\RHI\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\Serialization\Private\Backends;D:\UE_5.5\Engine\Source\Runtime\Serialization\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Animation;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Application;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Brushes;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Debugging;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\FastUpdate;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Fonts;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Input;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Layout;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Rendering;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Sound;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Styling;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Test;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Textures;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Trace;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Types;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Widgets;D:\UE_5.5\Engine\Source\Runtime\SlateRHIRenderer\Private\FX;D:\UE_5.5\Engine\Source\Runtime\Sockets\Private\Android;D:\UE_5.5\Engine\Source\Runtime\Sockets\Private\BSDSockets;D:\UE_5.5\Engine\Source\Runtime\Sockets\Private\IOS;D:\UE_5.5\Engine\Source\Runtime\Sockets\Private\Mac;D:\UE_5.5\Engine\Source\Runtime\Sockets\Private\Unix;D:\UE_5.5\Engine\Source\Runtime\Sockets\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\Solaris\uLangUE\Private;D:\UE_5.5\Engine\Source\Runtime\StorageServerClient\Private\BuiltInHttpClient;D:\UE_5.5\Engine\Source\Runtime\TimeManagement\Private\Widgets;D:\UE_5.5\Engine\Source\Runtime\TraceLog\Private\Trace;D:\UE_5.5\Engine\Source\Runtime\UMG\Private\Animation;D:\UE_5.5\Engine\Source\Runtime\UMG\Private\Binding;D:\UE_5.5\Engine\Source\Runtime\UMG\Private\Blueprint;D:\UE_5.5\Engine\Source\Runtime\UMG\Private\Components;D:\UE_5.5\Engine\Source\Runtime\UMG\Private\Extensions;D:\UE_5.5\Engine\Source\Runtime\UMG\Private\Slate;D:\UE_5.5\Engine\Source\Runtime\Unix\UnixCommonStartup\Private;D:\UE_5.5\Engine\Source\Runtime\VectorVM\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\VerseCompiler\Private\uLang;D:\UE_5.5\Engine\Source\Runtime\VirtualProduction\StageDataCore\Private;D:\UE_5.5\Engine\Source\Runtime\VorbisAudioDecoder\Module\Private;D:\UE_5.5\Engine\Source\Runtime\VulkanRHI\Private\Android;D:\UE_5.5\Engine\Source\Runtime\VulkanRHI\Private\Linux;D:\UE_5.5\Engine\Source\Runtime\VulkanRHI\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\WebBrowser\Private\Android;D:\UE_5.5\Engine\Source\Runtime\WebBrowser\Private\CEF;D:\UE_5.5\Engine\Source\Runtime\WebBrowser\Private\IOS;D:\UE_5.5\Engine\Source\Runtime\WebBrowser\Private\MobileJS;D:\UE_5.5\Engine\Source\Runtime\WebBrowser\Private\Native;D:\UE_5.5\Engine\Source\Runtime\Windows\AudioMixerWasapi\Private;D:\UE_5.5\Engine\Source\Runtime\Windows\AudioMixerXAudio2\Private;D:\UE_5.5\Engine\Source\Runtime\Windows\D3D11RHI\Private;D:\UE_5.5\Engine\Source\Runtime\Windows\WindowsPlatformFeatures\Private;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\extras;D:\UE_5.5\Engine\Source\ThirdParty\Eigen\unsupported\bench;D:\UE_5.5\Engine\Source\ThirdParty\Eigen\unsupported\test;D:\UE_5.5\Engine\Source\Developer\Datasmith\DatasmithExporterUI\Private\Widgets;D:\UE_5.5\Engine\Source\Developer\Datasmith\DatasmithFacade\Private\DirectLink;D:\UE_5.5\Engine\Source\Developer\DesktopWidgets\Private\Widgets\Input;D:\UE_5.5\Engine\Source\Developer\DeviceManager\Private\Widgets\Apps;D:\UE_5.5\Engine\Source\Developer\DeviceManager\Private\Widgets\Browser;D:\UE_5.5\Engine\Source\Developer\DeviceManager\Private\Widgets\Details;D:\UE_5.5\Engine\Source\Developer\DeviceManager\Private\Widgets\Processes;D:\UE_5.5\Engine\Source\Developer\DeviceManager\Private\Widgets\Toolbar;D:\UE_5.5\Engine\Source\Developer\Horde\Private\Storage\Bundles;D:\UE_5.5\Engine\Source\Developer\Horde\Private\Storage\Clients;D:\UE_5.5\Engine\Source\Developer\Horde\Private\Storage\Nodes;D:\UE_5.5\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\Android;D:\UE_5.5\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\Apple;D:\UE_5.5\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\IOS;D:\UE_5.5\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\Linux;D:\UE_5.5\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\Mac;D:\UE_5.5\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\Windows;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Archive;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Build;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Cook;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Deploy;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Launch;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Package;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Preview;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Profile;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Progress;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Project;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Settings;D:\UE_5.5\Engine\Source\Developer\ProjectLauncher\Private\Widgets\Shared;D:\UE_5.5\Engine\Source\Developer\SessionFrontend\Private\Widgets\Browser;D:\UE_5.5\Engine\Source\Developer\SessionFrontend\Private\Widgets\Console;D:\UE_5.5\Engine\Source\Developer\StandaloneRenderer\Private\Linux\OpenGL;D:\UE_5.5\Engine\Source\Developer\StandaloneRenderer\Private\Mac\OpenGL;D:\UE_5.5\Engine\Source\Developer\StandaloneRenderer\Private\Windows\D3D;D:\UE_5.5\Engine\Source\Developer\StandaloneRenderer\Private\Windows\OpenGL;D:\UE_5.5\Engine\Source\Developer\TraceAnalysis\Private\Analysis\Transport;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\Common;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\ContextSwitches;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\CookProfiler;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\ImportTool;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\LoadingProfiler;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\MemoryProfiler;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\NetworkingProfiler;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\TaskGraphProfiler;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\Tests;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\TimingProfiler;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\Widgets;D:\UE_5.5\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore\Common;D:\UE_5.5\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\Common;D:\UE_5.5\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\StoreService;D:\UE_5.5\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\Tests;D:\UE_5.5\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\Widgets;D:\UE_5.5\Engine\Source\Developer\WidgetRegistration\Private\Layout\Containers;D:\UE_5.5\Engine\Source\Developer\Windows\LiveCoding\Private\External;D:\UE_5.5\Engine\Source\Developer\Windows\LiveCodingServer\Private\External;D:\UE_5.5\Engine\Source\Editor\AddContentDialog\Private\ContentSourceProviders\FeaturePack;D:\UE_5.5\Engine\Source\Editor\EditorFramework\Private\Elements\Framework;D:\UE_5.5\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Private\Behaviors;D:\UE_5.5\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Private\EditorGizmos;D:\UE_5.5\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Private\ToolContexts;D:\UE_5.5\Engine\Source\Editor\LevelEditor\Private\Elements\Actor;D:\UE_5.5\Engine\Source\Editor\LevelEditor\Private\Elements\Component;D:\UE_5.5\Engine\Source\Editor\LevelEditor\Private\Elements\SMInstance;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\MVVM\ViewModels;D:\UE_5.5\Engine\Source\Editor\MovieSceneTools\Private\TrackEditors\PropertyTrackEditors;D:\UE_5.5\Engine\Source\Editor\PropertyEditor\Private\Presentation\PropertyEditor;D:\UE_5.5\Engine\Source\Editor\PropertyEditor\Private\Presentation\PropertyTable;D:\UE_5.5\Engine\Source\Editor\PropertyEditor\Private\UserInterface\Categories;D:\UE_5.5\Engine\Source\Editor\PropertyEditor\Private\UserInterface\PropertyDetails;D:\UE_5.5\Engine\Source\Editor\PropertyEditor\Private\UserInterface\PropertyEditor;D:\UE_5.5\Engine\Source\Editor\PropertyEditor\Private\UserInterface\PropertyTable;D:\UE_5.5\Engine\Source\Editor\PropertyEditor\Private\UserInterface\Widgets;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Filters\Filters;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Filters\Menus;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Filters\TextExpressions;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Filters\Widgets;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Misc\Thumbnail;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\MVVM\Extensions;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\MVVM\ViewModels;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\MVVM\Views;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Widgets\CurveEditor;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Widgets\OutlinerColumns;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Widgets\OutlinerIndicators;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\Widgets\Sidebar;D:\UE_5.5\Engine\Source\Editor\SequencerCore\Private\MVVM\Extensions;D:\UE_5.5\Engine\Source\Editor\SequencerCore\Private\MVVM\Selection;D:\UE_5.5\Engine\Source\Editor\SequencerCore\Private\MVVM\ViewModels;D:\UE_5.5\Engine\Source\Editor\SequencerCore\Private\MVVM\Views;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Cooker\Algo;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Elements\Actor;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Elements\Component;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Elements\Framework;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Elements\Object;D:\UE_5.5\Engine\Source\Editor\UnrealEd\Private\Elements\SMInstance;D:\UE_5.5\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition\ContentBundle;D:\UE_5.5\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition\Customizations;D:\UE_5.5\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition\Filter;D:\UE_5.5\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition\HLOD;D:\UE_5.5\Engine\Source\Programs\LiveLinkHub\Source\LiveLinkHubLauncher\Private;D:\UE_5.5\Engine\Source\Runtime\AdvancedWidgets\Private\Framework\PropertyViewer;D:\UE_5.5\Engine\Source\Runtime\AdvancedWidgets\Private\Widgets\ColorGrading;D:\UE_5.5\Engine\Source\Runtime\AdvancedWidgets\Private\Widgets\PropertyViewer;D:\UE_5.5\Engine\Source\Runtime\Advertising\Android\AndroidAdvertising\Private;D:\UE_5.5\Engine\Source\Runtime\Advertising\IOS\IOSAdvertising\Private;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Blackboard;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Composites;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Decorators;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Services;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Tasks;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Contexts;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Generators;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Items;D:\UE_5.5\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Tests;D:\UE_5.5\Engine\Source\Runtime\Analytics\Analytics\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\Analytics\AnalyticsET\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\AppFramework\Private\Framework\Testing;D:\UE_5.5\Engine\Source\Runtime\AppFramework\Private\Widgets\Colors;D:\UE_5.5\Engine\Source\Runtime\AppFramework\Private\Widgets\Testing;D:\UE_5.5\Engine\Source\Runtime\AppFramework\Private\Widgets\Workflow;D:\UE_5.5\Engine\Source\Runtime\Apple\MetalRHI\Private\Shaders;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\GenericPlatform\Accessibility;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\IOS\Accessibility;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\Mac\Accessibility;D:\UE_5.5\Engine\Source\Runtime\ApplicationCore\Private\Windows\Accessibility;D:\UE_5.5\Engine\Source\Runtime\AudioCaptureImplementations\Android\AudioCaptureAndroid\Private;D:\UE_5.5\Engine\Source\Runtime\AudioCaptureImplementations\IOS\AudioCaptureAudioUnit\Private;D:\UE_5.5\Engine\Source\Runtime\AudioCaptureImplementations\Windows\AudioCaputureWasapi\Private;D:\UE_5.5\Engine\Source\Runtime\AudioDeviceEnumeration\Windows\WindowsMMDeviceEnumeration\Private;D:\UE_5.5\Engine\Source\Runtime\AudioPlatformSupport\Windows\WASAPI\Private;D:\UE_5.5\Engine\Source\Runtime\AVEncoder\Private\Decoders\vdecmpeg4;D:\UE_5.5\Engine\Source\Runtime\AVEncoder\Private\Decoders\Windows;D:\UE_5.5\Engine\Source\Runtime\BinkAudioDecoder\SDK\BinkAudio\Src;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Async\Fundamental;D:\UE_5.5\Engine\Source\Runtime\Core\Private\ColorManagement\Tests;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Containers\Algo;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Experimental\Containers;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Experimental\Coroutine;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Experimental\Graph;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Experimental\Misc;D:\UE_5.5\Engine\Source\Runtime\Core\Private\HAL\Allocators;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Internationalization\Cultures;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Modules\Tests;D:\UE_5.5\Engine\Source\Runtime\Core\Private\ProfilingDebugging\Apple;D:\UE_5.5\Engine\Source\Runtime\Core\Private\ProfilingDebugging\Microsoft;D:\UE_5.5\Engine\Source\Runtime\Core\Private\ProfilingDebugging\Unix;D:\UE_5.5\Engine\Source\Runtime\Core\Private\ProfilingDebugging\Windows;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Serialization\Csv;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Serialization\Formatters;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Tests\HAL;D:\UE_5.5\Engine\Source\Runtime\Core\Private\Tests\Serialization;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\Misc\DataValidation;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\Serialization\Formatters;D:\UE_5.5\Engine\Source\Runtime\CoreUObject\Private\UObject\SavePackage;D:\UE_5.5\Engine\Source\Runtime\Datasmith\DatasmithCore\Private\DirectLink;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\AI\Navigation;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Animation\AnimData;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Elements\Actor;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Elements\Component;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Elements\Framework;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Elements\Interfaces;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Elements\Object;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Elements\SMInstance;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\GameFramework\Tests;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\LevelInstance\Test;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Net\Subsystems;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\PhysicsEngine\Experimental;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\PhysicsEngine\ImmediatePhysics;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\PhysicsEngine\Tests;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Tests\AutoRTFM;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Tests\Internationalization;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Tests\Loading;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Tests\WorldPartition;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\ActorPartition;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\ContentBundle;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\Cook;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\DataLayer;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\ErrorHandling;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\Filter;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\HLOD;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\Landscape;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\LevelInstance;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\LoaderAdapter;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\NavigationData;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\PackedLevelActor;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\RuntimeHashSet;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\RuntimeSpatialHash;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\WorldPartition\StaticLightingData;D:\UE_5.5\Engine\Source\Runtime\Experimental\Animation\Constraints\Private;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\ChaosDebugDraw;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\ChaosVisualDebugger;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Field;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Framework;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\GeometryCollection;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\PhysicsProxy;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\Experimental\ChaosSolverEngine\Private\Chaos;D:\UE_5.5\Engine\Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesCore\Private;D:\UE_5.5\Engine\Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesEngine\Private;D:\UE_5.5\Engine\Source\Runtime\Experimental\ChaosVisualDebugger\Private\DataWrappers;D:\UE_5.5\Engine\Source\Runtime\Experimental\GeometryCollectionEngine\Private\GeometryCollection;D:\UE_5.5\Engine\Source\Runtime\Experimental\GeometryCollectionEngine\Public\GeometryCollection;D:\UE_5.5\Engine\Source\Runtime\Experimental\IoStore\HttpClient\Private;D:\UE_5.5\Engine\Source\Runtime\Experimental\IoStore\OnDemand\Private;D:\UE_5.5\Engine\Source\Runtime\Experimental\ISMPool\Private\ISMPool;D:\UE_5.5\Engine\Source\Runtime\Experimental\ISMPool\Public\ISMPool;D:\UE_5.5\Engine\Source\Runtime\Experimental\JsonObjectGraph\Private\JsonObjectGraph;D:\UE_5.5\Engine\Source\Runtime\Experimental\Voronoi\Private\Voronoi;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\CompGeom\ThirdParty;D:\UE_5.5\Engine\Source\Runtime\GeometryCore\Private\DynamicMesh\Operations;D:\UE_5.5\Engine\Source\Runtime\InteractiveToolsFramework\Private\BaseBehaviors\Widgets;D:\UE_5.5\Engine\Source\Runtime\Interchange\Core\Private\Nodes;D:\UE_5.5\Engine\Source\Runtime\Interchange\Core\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\Interchange\Core\Private\Types;D:\UE_5.5\Engine\Source\Runtime\Interchange\Engine\Private\Tasks;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\EntitySystem\TrackInstance;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Evaluation\Blending;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Evaluation\Instances;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Evaluation\PreAnimatedState;D:\UE_5.5\Engine\Source\Runtime\MovieScene\Private\Tests\AutoRTFM;D:\UE_5.5\Engine\Source\Runtime\MovieSceneTracks\Private\EntitySystem\Interrogation;D:\UE_5.5\Engine\Source\Runtime\Online\BackgroundHTTP\Private\GenericPlatform;D:\UE_5.5\Engine\Source\Runtime\Online\BackgroundHTTP\Private\IOS;D:\UE_5.5\Engine\Source\Runtime\Online\BackgroundHTTP\Private\PlatformWithModularFeature;D:\UE_5.5\Engine\Source\Runtime\Online\BuildPatchServices\Private\Common;D:\UE_5.5\Engine\Source\Runtime\Online\BuildPatchServices\Private\Compactify;D:\UE_5.5\Engine\Source\Runtime\Online\BuildPatchServices\Private\Core;D:\UE_5.5\Engine\Source\Runtime\Online\BuildPatchServices\Private\Data;D:\UE_5.5\Engine\Source\Runtime\Online\BuildPatchServices\Private\Diffing;D:\UE_5.5\Engine\Source\Runtime\Online\BuildPatchServices\Private\Enumeration;D:\UE_5.5\Engine\Source\Runtime\Online\BuildPatchServices\Private\Generation;D:\UE_5.5\Engine\Source\Runtime\Online\BuildPatchServices\Private\Installer;D:\UE_5.5\Engine\Source\Runtime\Online\Experimental\EventLoopTests\Tests;D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Private\Android;D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Private\Apple;D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Private\Curl;D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Private\GenericPlatform;D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Private\Interfaces;D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Private\Unix;D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Private\WinHttp;D:\UE_5.5\Engine\Source\Runtime\Online\HTTPServer\Private\Tests;D:\UE_5.5\Engine\Source\Runtime\Online\ICMP\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\Online\SSL\Private\Android;D:\UE_5.5\Engine\Source\Runtime\Online\SSL\Private\Unix;D:\UE_5.5\Engine\Source\Runtime\Online\SSL\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\Online\Voice\Private\Android;D:\UE_5.5\Engine\Source\Runtime\Online\Voice\Private\Linux;D:\UE_5.5\Engine\Source\Runtime\Online\Voice\Private\Mac;D:\UE_5.5\Engine\Source\Runtime\Online\Voice\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\Online\WebSockets\Private\Lws;D:\UE_5.5\Engine\Source\Runtime\Online\WebSockets\Private\WinHttp;D:\UE_5.5\Engine\Source\Runtime\Online\XMPP\Private\XmppStrophe;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\EncryptionHandlerComponent\Private;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\RSAKeyAESEncryption\Private;D:\UE_5.5\Engine\Source\Runtime\Portal\LauncherPlatform\Private\Linux;D:\UE_5.5\Engine\Source\Runtime\Portal\LauncherPlatform\Private\Mac;D:\UE_5.5\Engine\Source\Runtime\Portal\LauncherPlatform\Private\Windows;D:\UE_5.5\Engine\Source\Runtime\Portal\Proxies\Private\Account;D:\UE_5.5\Engine\Source\Runtime\Portal\Proxies\Private\Application;D:\UE_5.5\Engine\Source\Runtime\RadAudioCodec\SDK\Src\RadA;D:\UE_5.5\Engine\Source\Runtime\RadAudioCodec\SDK\Src\RadAudio;D:\UE_5.5\Engine\Source\Runtime\Renderer\Private\Substrate\Glint;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\Animation;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\Application;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\Commands;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\Docking;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\Layout;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\MetaData;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\MultiBox;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\Notifications;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\Styling;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\Text;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets\Accessibility;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets\Colors;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets\Docking;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets\Images;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets\Input;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets\LayerManager;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets\Layout;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets\Navigation;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets\Notifications;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets\Text;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Widgets\Views;D:\UE_5.5\Engine\Source\Runtime\Slate\Public\Widgets\Layout;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Widgets\Accessibility;D:\UE_5.5\Engine\Source\Runtime\SlateCore\Private\Widgets\Images;D:\UE_5.5\Engine\Source\Runtime\TraceLog\Private\Trace\Important;D:\UE_5.5\Engine\Source\Runtime\TypedElementFramework\Private\Elements\Common;D:\UE_5.5\Engine\Source\Runtime\TypedElementFramework\Private\Elements\Framework;D:\UE_5.5\Engine\Source\Runtime\TypedElementFramework\Private\Elements\Interfaces;D:\UE_5.5\Engine\Source\Runtime\TypedElementFramework\Tests\Elements\Framework;D:\UE_5.5\Engine\Source\Runtime\TypedElementRuntime\Private\Elements\Framework;D:\UE_5.5\Engine\Source\Runtime\TypedElementRuntime\Private\Elements\Interfaces;D:\UE_5.5\Engine\Source\Runtime\UMG\Private\Binding\States;D:\UE_5.5\Engine\Source\Runtime\VerseCompiler\Private\uLang\Diagnostics;D:\UE_5.5\Engine\Source\Runtime\VerseCompiler\Private\uLang\Parser;D:\UE_5.5\Engine\Source\Runtime\VerseCompiler\Private\uLang\SemanticAnalyzer;D:\UE_5.5\Engine\Source\Runtime\VerseCompiler\Private\uLang\Semantics;D:\UE_5.5\Engine\Source\Runtime\VerseCompiler\Private\uLang\SourceProject;D:\UE_5.5\Engine\Source\Runtime\VerseCompiler\Private\uLang\Syntax;D:\UE_5.5\Engine\Source\Runtime\VerseCompiler\Private\uLang\Toolchain;D:\UE_5.5\Engine\Source\Runtime\Windows\D3D11RHI\Private\Windows;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\ExtraTests;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\SelfTest;D:\UE_5.5\Engine\Source\ThirdParty\Eigen\unsupported\doc\examples;D:\UE_5.5\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\games-frame-pacing\common;D:\UE_5.5\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\games-frame-pacing\opengl;D:\UE_5.5\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\games-frame-pacing\vulkan;D:\UE_5.5\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\src\common;D:\UE_5.5\Engine\Source\Developer\Horde\Private\Storage\Bundles\V2;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\ContextSwitches\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\CookProfiler\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\CookProfiler\Widgets;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\LoadingProfiler\Tracks;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\LoadingProfiler\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\LoadingProfiler\Widgets;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\MemoryProfiler\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\MemoryProfiler\Widgets;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\NetworkingProfiler\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\NetworkingProfiler\Widgets;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\Table\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\Table\Widgets;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\TaskGraphProfiler\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\TaskGraphProfiler\Widgets;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\Tests\FunctionalTests;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\TimingProfiler\Tracks;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\TimingProfiler\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsights\Private\Insights\TimingProfiler\Widgets;D:\UE_5.5\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore\Filter\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore\Filter\Widgets;D:\UE_5.5\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore\Table\ViewModels;D:\UE_5.5\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore\Table\Widgets;D:\UE_5.5\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\Tests\FunctionalTests;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\MVVM\ViewModels\OutlinerColumns;D:\UE_5.5\Engine\Source\Editor\Sequencer\Private\MVVM\ViewModels\OutlinerIndicators;D:\UE_5.5\Engine\Source\Editor\SequencerCore\Private\MVVM\ViewModels\OutlinerColumns;D:\UE_5.5\Engine\Source\Editor\SequencerCore\Private\MVVM\Views\OutlinerColumns;D:\UE_5.5\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition\ContentBundle\Outliner;D:\UE_5.5\Engine\Source\Runtime\Apple\MetalRHI\Private\Shaders\Debugging;D:\UE_5.5\Engine\Source\Runtime\Apple\MetalRHI\Private\Shaders\Types;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Core;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Math;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Topo;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\UI;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Utils;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Net\Experimental\Iris;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Net\Iris\ReplicationSystem;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\Net\Tests\Util;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\PhysicsEngine\ImmediatePhysics\ImmediatePhysicsChaos;D:\UE_5.5\Engine\Source\Runtime\Engine\Private\PhysicsEngine\ImmediatePhysics\ImmediatePhysicsPhysX;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Character;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Collision;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\DebugDraw;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Deformable;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Evolution;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Framework;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Interface;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Island;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Joint;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Math;D:\UE_5.5\Engine\Source\Runtime\Experimental\Chaos\Private\GeometryCollection\Facades;D:\UE_5.5\Engine\Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesCore\Private\SimModule;D:\UE_5.5\Engine\Source\Runtime\Experimental\Dataflow\Core\Private\Dataflow;D:\UE_5.5\Engine\Source\Runtime\Experimental\Dataflow\Engine\Private\Dataflow;D:\UE_5.5\Engine\Source\Runtime\Experimental\Dataflow\Simulation\Private\Dataflow;D:\UE_5.5\Engine\Source\Runtime\Experimental\IoStore\OnDemand\Private\Tool;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Stub\Private\Iris;D:\UE_5.5\Engine\Source\Runtime\Net\Common\Private\Net\Common;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Serialization;D:\UE_5.5\Engine\Source\Runtime\Online\BuildPatchServices\Private\Installer\Statistics;D:\UE_5.5\Engine\Source\Runtime\Online\BuildPatchServices\Private\Tests\Unit;D:\UE_5.5\Engine\Source\Runtime\Online\Experimental\EventLoop\Private\EventLoop;D:\UE_5.5\Engine\Source\Runtime\Online\Experimental\EventLoopTests\Tests\EventLoop;D:\UE_5.5\Engine\Source\Runtime\Online\HTTP\Private\WinHttp\Support;D:\UE_5.5\Engine\Source\Runtime\Online\WebSockets\Private\WinHttp\Support;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\AsymmetricEncryption\RSAEncryptionHandlerComponent\Private;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\MultiBox\Mac;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\Text\Android;D:\UE_5.5\Engine\Source\Runtime\Slate\Private\Framework\Text\IOS;D:\UE_5.5\Engine\Source\Runtime\Solaris\uLangCore\Private\uLang\Common;D:\UE_5.5\Engine\Source\Runtime\Solaris\uLangJSON\Private\uLang\JSON;D:\UE_5.5\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Android;D:\UE_5.5\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Apple;D:\UE_5.5\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Unix;D:\UE_5.5\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Windows;D:\UE_5.5\Engine\Source\Runtime\UMG\Private\Binding\States\Tests;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\benchmark;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\generators;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\interfaces;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\internal;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\matchers;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\reporters;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\SelfTest\helpers;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\SelfTest\IntrospectiveTests;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\SelfTest\TimingTests;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\SelfTest\UsageTests;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\TestScripts\DiscoverTests;D:\UE_5.5\Engine\Source\ThirdParty\Eigen\unsupported\doc\examples\SYCL;D:\UE_5.5\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\src\common\jni;D:\UE_5.5\Engine\Source\ThirdParty\HWCPipe\include\vendor\arm\mali;D:\UE_5.5\Engine\Source\ThirdParty\HWCPipe\include\vendor\arm\pmu;D:\UE_5.5\Engine\Source\Programs\LiveLinkHub\Source\LiveLinkHubLauncher\Private\Platform\Linux;D:\UE_5.5\Engine\Source\Programs\LiveLinkHub\Source\LiveLinkHubLauncher\Private\Platform\Mac;D:\UE_5.5\Engine\Source\Programs\LiveLinkHub\Source\LiveLinkHubLauncher\Private\Platform\Windows;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Geo\Curves;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Geo\Sampling;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Geo\Surfaces;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Mesh\Criteria;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Mesh\Meshers;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Mesh\Structure;D:\UE_5.5\Engine\Source\Runtime\Experimental\Dataflow\Simulation\Private\Dataflow\Interfaces;D:\UE_5.5\Engine\Source\Runtime\Experimental\FieldSystem\Source\FieldSystemEngine\Private\Field;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\Core;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\DataStream;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationState;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\Serialization;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\Stats;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core\Analytics;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core\Connection;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core\DirtyNetObjectTracker;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core\Misc;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core\NetHandle;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core\NetToken;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core\PropertyConditions;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core\PushModel;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core\Serialization;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core\Trace;D:\UE_5.5\Engine\Source\Runtime\Online\Experimental\EventLoop\Private\EventLoop\BSDSocket;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\AESBlockEncryptor\Private;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\BlockEncryptionHandlerComponent\Private;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\BlowFishBlockEncryptor\Private;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\TwoFishBlockEncryptor\Private;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\XORBlockEncryptor\Private;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\StreamEncryption\StreamEncryptionHandlerComponent\Private;D:\UE_5.5\Engine\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\StreamEncryption\XORStreamEncryptor\Private;D:\UE_5.5\Engine\Source\Runtime\Solaris\uLangCore\Private\uLang\Common\Containers;D:\UE_5.5\Engine\Source\Runtime\Solaris\uLangCore\Private\uLang\Common\Memory;D:\UE_5.5\Engine\Source\Runtime\Solaris\uLangCore\Private\uLang\Common\Misc;D:\UE_5.5\Engine\Source\Runtime\Solaris\uLangCore\Private\uLang\Common\Text;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\benchmark\detail;D:\UE_5.5\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\matchers\internal;D:\UE_5.5\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Mesh\Meshers\IsoTriangulator;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\Conditionals;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\DeltaCompression;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\Filtering;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\NetBlob;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\Polling;D:\UE_5.5\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\Prioritization;D:\UE_5.5\Engine\Source\Runtime\Net\Core\Private\Net\Core\Trace\Reporters;</SourcePath>
  </PropertyGroup>
  <ItemDefinitionGroup>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <PropertyGroup>
    <CleanDependsOn> $(CleanDependsOn); </CleanDependsOn>
    <CppCleanDependsOn></CppCleanDependsOn>
  </PropertyGroup>
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
