// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OptimizedWebBrowser/Public/WebBrowserOptimizer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeWebBrowserOptimizer() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FColor();
OPTIMIZEDWEBBROWSER_API UClass* Z_Construct_UClass_UWebBrowserOptimizer();
OPTIMIZEDWEBBROWSER_API UClass* Z_Construct_UClass_UWebBrowserOptimizer_NoRegister();
OPTIMIZEDWEBBROWSER_API UScriptStruct* Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings();
UPackage* Z_Construct_UPackage__Script_OptimizedWebBrowser();
// End Cross Module References

// Begin ScriptStruct FWebBrowserOptimizerSettings
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_WebBrowserOptimizerSettings;
class UScriptStruct* FWebBrowserOptimizerSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_WebBrowserOptimizerSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_WebBrowserOptimizerSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings, (UObject*)Z_Construct_UPackage__Script_OptimizedWebBrowser(), TEXT("WebBrowserOptimizerSettings"));
	}
	return Z_Registration_Info_UScriptStruct_WebBrowserOptimizerSettings.OuterSingleton;
}
template<> OPTIMIZEDWEBBROWSER_API UScriptStruct* StaticStruct<FWebBrowserOptimizerSettings>()
{
	return FWebBrowserOptimizerSettings::StaticStruct();
}
struct Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configuration settings for the web browser optimizer\n */" },
#endif
		{ "ModuleRelativePath", "Public/WebBrowserOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration settings for the web browser optimizer" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BrowserFrameRate_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "120" },
		{ "ClampMin", "15" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** The frame rate at which the browser will render. Higher values are smoother but use more CPU. */" },
#endif
		{ "ModuleRelativePath", "Public/WebBrowserOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "The frame rate at which the browser will render. Higher values are smoother but use more CPU." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHardwareAccelerationEnabled_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether hardware acceleration is enabled. */" },
#endif
		{ "ModuleRelativePath", "Public/WebBrowserOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether hardware acceleration is enabled." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackgroundColor_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Background color of the browser. Using opaque colors can improve performance. */" },
#endif
		{ "ModuleRelativePath", "Public/WebBrowserOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Background color of the browser. Using opaque colors can improve performance." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBackgroundTabThrottlingEnabled_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether to throttle background tabs to save CPU resources. */" },
#endif
		{ "ModuleRelativePath", "Public/WebBrowserOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether to throttle background tabs to save CPU resources." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOptimizeScrolling_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether to optimize scrolling performance. */" },
#endif
		{ "ModuleRelativePath", "Public/WebBrowserOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether to optimize scrolling performance." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLazyLoadImages_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether to lazy load images for better performance. */" },
#endif
		{ "ModuleRelativePath", "Public/WebBrowserOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether to lazy load images for better performance." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDisableAnimationsWhenHidden_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether to disable animations when the page is not visible. */" },
#endif
		{ "ModuleRelativePath", "Public/WebBrowserOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether to disable animations when the page is not visible." },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_BrowserFrameRate;
	static void NewProp_bHardwareAccelerationEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHardwareAccelerationEnabled;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BackgroundColor;
	static void NewProp_bBackgroundTabThrottlingEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBackgroundTabThrottlingEnabled;
	static void NewProp_bOptimizeScrolling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOptimizeScrolling;
	static void NewProp_bLazyLoadImages_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLazyLoadImages;
	static void NewProp_bDisableAnimationsWhenHidden_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDisableAnimationsWhenHidden;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWebBrowserOptimizerSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_BrowserFrameRate = { "BrowserFrameRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWebBrowserOptimizerSettings, BrowserFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BrowserFrameRate_MetaData), NewProp_BrowserFrameRate_MetaData) };
void Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bHardwareAccelerationEnabled_SetBit(void* Obj)
{
	((FWebBrowserOptimizerSettings*)Obj)->bHardwareAccelerationEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bHardwareAccelerationEnabled = { "bHardwareAccelerationEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWebBrowserOptimizerSettings), &Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bHardwareAccelerationEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHardwareAccelerationEnabled_MetaData), NewProp_bHardwareAccelerationEnabled_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_BackgroundColor = { "BackgroundColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWebBrowserOptimizerSettings, BackgroundColor), Z_Construct_UScriptStruct_FColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackgroundColor_MetaData), NewProp_BackgroundColor_MetaData) };
void Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bBackgroundTabThrottlingEnabled_SetBit(void* Obj)
{
	((FWebBrowserOptimizerSettings*)Obj)->bBackgroundTabThrottlingEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bBackgroundTabThrottlingEnabled = { "bBackgroundTabThrottlingEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWebBrowserOptimizerSettings), &Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bBackgroundTabThrottlingEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBackgroundTabThrottlingEnabled_MetaData), NewProp_bBackgroundTabThrottlingEnabled_MetaData) };
void Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bOptimizeScrolling_SetBit(void* Obj)
{
	((FWebBrowserOptimizerSettings*)Obj)->bOptimizeScrolling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bOptimizeScrolling = { "bOptimizeScrolling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWebBrowserOptimizerSettings), &Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bOptimizeScrolling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOptimizeScrolling_MetaData), NewProp_bOptimizeScrolling_MetaData) };
void Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bLazyLoadImages_SetBit(void* Obj)
{
	((FWebBrowserOptimizerSettings*)Obj)->bLazyLoadImages = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bLazyLoadImages = { "bLazyLoadImages", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWebBrowserOptimizerSettings), &Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bLazyLoadImages_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLazyLoadImages_MetaData), NewProp_bLazyLoadImages_MetaData) };
void Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bDisableAnimationsWhenHidden_SetBit(void* Obj)
{
	((FWebBrowserOptimizerSettings*)Obj)->bDisableAnimationsWhenHidden = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bDisableAnimationsWhenHidden = { "bDisableAnimationsWhenHidden", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWebBrowserOptimizerSettings), &Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bDisableAnimationsWhenHidden_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDisableAnimationsWhenHidden_MetaData), NewProp_bDisableAnimationsWhenHidden_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_BrowserFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bHardwareAccelerationEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_BackgroundColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bBackgroundTabThrottlingEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bOptimizeScrolling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bLazyLoadImages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewProp_bDisableAnimationsWhenHidden,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_OptimizedWebBrowser,
	nullptr,
	&NewStructOps,
	"WebBrowserOptimizerSettings",
	Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::PropPointers),
	sizeof(FWebBrowserOptimizerSettings),
	alignof(FWebBrowserOptimizerSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings()
{
	if (!Z_Registration_Info_UScriptStruct_WebBrowserOptimizerSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_WebBrowserOptimizerSettings.InnerSingleton, Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_WebBrowserOptimizerSettings.InnerSingleton;
}
// End ScriptStruct FWebBrowserOptimizerSettings

// Begin Class UWebBrowserOptimizer Function ApplyOptimizations
struct Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics
{
	struct WebBrowserOptimizer_eventApplyOptimizations_Parms
	{
		UObject* WebBrowserWidget;
		FWebBrowserOptimizerSettings Settings;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Web Browser|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Apply optimization settings to a web browser widget\n     *\n     * @param WebBrowserPtr The web browser widget to optimize (as a void pointer)\n     * @param Settings The optimization settings to apply\n     */" },
#endif
		{ "ModuleRelativePath", "Public/WebBrowserOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Apply optimization settings to a web browser widget\n\n@param WebBrowserPtr The web browser widget to optimize (as a void pointer)\n@param Settings The optimization settings to apply" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Settings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WebBrowserWidget;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Settings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::NewProp_WebBrowserWidget = { "WebBrowserWidget", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WebBrowserOptimizer_eventApplyOptimizations_Parms, WebBrowserWidget), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::NewProp_Settings = { "Settings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WebBrowserOptimizer_eventApplyOptimizations_Parms, Settings), Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Settings_MetaData), NewProp_Settings_MetaData) }; // 2147442978
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::NewProp_WebBrowserWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::NewProp_Settings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWebBrowserOptimizer, nullptr, "ApplyOptimizations", nullptr, nullptr, Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::WebBrowserOptimizer_eventApplyOptimizations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04422401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::WebBrowserOptimizer_eventApplyOptimizations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWebBrowserOptimizer::execApplyOptimizations)
{
	P_GET_OBJECT(UObject,Z_Param_WebBrowserWidget);
	P_GET_STRUCT_REF(FWebBrowserOptimizerSettings,Z_Param_Out_Settings);
	P_FINISH;
	P_NATIVE_BEGIN;
	UWebBrowserOptimizer::ApplyOptimizations(Z_Param_WebBrowserWidget,Z_Param_Out_Settings);
	P_NATIVE_END;
}
// End Class UWebBrowserOptimizer Function ApplyOptimizations

// Begin Class UWebBrowserOptimizer
void UWebBrowserOptimizer::StaticRegisterNativesUWebBrowserOptimizer()
{
	UClass* Class = UWebBrowserOptimizer::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyOptimizations", &UWebBrowserOptimizer::execApplyOptimizations },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UWebBrowserOptimizer);
UClass* Z_Construct_UClass_UWebBrowserOptimizer_NoRegister()
{
	return UWebBrowserOptimizer::StaticClass();
}
struct Z_Construct_UClass_UWebBrowserOptimizer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Helper class to optimize web browser performance\n */" },
#endif
		{ "IncludePath", "WebBrowserOptimizer.h" },
		{ "ModuleRelativePath", "Public/WebBrowserOptimizer.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Helper class to optimize web browser performance" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UWebBrowserOptimizer_ApplyOptimizations, "ApplyOptimizations" }, // 2494193658
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UWebBrowserOptimizer>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UWebBrowserOptimizer_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_OptimizedWebBrowser,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UWebBrowserOptimizer_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UWebBrowserOptimizer_Statics::ClassParams = {
	&UWebBrowserOptimizer::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UWebBrowserOptimizer_Statics::Class_MetaDataParams), Z_Construct_UClass_UWebBrowserOptimizer_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UWebBrowserOptimizer()
{
	if (!Z_Registration_Info_UClass_UWebBrowserOptimizer.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UWebBrowserOptimizer.OuterSingleton, Z_Construct_UClass_UWebBrowserOptimizer_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UWebBrowserOptimizer.OuterSingleton;
}
template<> OPTIMIZEDWEBBROWSER_API UClass* StaticClass<UWebBrowserOptimizer>()
{
	return UWebBrowserOptimizer::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UWebBrowserOptimizer);
UWebBrowserOptimizer::~UWebBrowserOptimizer() {}
// End Class UWebBrowserOptimizer

// Begin Registration
struct Z_CompiledInDeferFile_FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FWebBrowserOptimizerSettings::StaticStruct, Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics::NewStructOps, TEXT("WebBrowserOptimizerSettings"), &Z_Registration_Info_UScriptStruct_WebBrowserOptimizerSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWebBrowserOptimizerSettings), 2147442978U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UWebBrowserOptimizer, UWebBrowserOptimizer::StaticClass, TEXT("UWebBrowserOptimizer"), &Z_Registration_Info_UClass_UWebBrowserOptimizer, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UWebBrowserOptimizer), 1936947362U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_850989719(TEXT("/Script/OptimizedWebBrowser"),
	Z_CompiledInDeferFile_FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_Statics::ScriptStructInfo),
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
