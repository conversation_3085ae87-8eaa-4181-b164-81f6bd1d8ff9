// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Baoli/AI/MyAIController.h"
#include "Runtime/AIModule/Classes/Perception/AIPerceptionTypes.h"
#include "Runtime/Engine/Classes/Engine/TimerHandle.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeMyAIController() {}

// Begin Cross Module References
AIMODULE_API UClass* Z_Construct_UClass_AAIController();
AIMODULE_API UClass* Z_Construct_UClass_UAIPerceptionComponent_NoRegister();
AIMODULE_API UClass* Z_Construct_UClass_UAITask_MoveTo_NoRegister();
AIMODULE_API UScriptStruct* Z_Construct_UScriptStruct_FAIStimulus();
BAOLI_API UClass* Z_Construct_UClass_AAI_CharacterBase_NoRegister();
BAOLI_API UClass* Z_Construct_UClass_AMyAIController();
BAOLI_API UClass* Z_Construct_UClass_AMyAIController_NoRegister();
BAOLI_API UEnum* Z_Construct_UEnum_Baoli_EAIState();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
NAVIGATIONSYSTEM_API UClass* Z_Construct_UClass_UNavigationPath_NoRegister();
UPackage* Z_Construct_UPackage__Script_Baoli();
// End Cross Module References

// Begin Enum EAIState
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAIState;
static UEnum* EAIState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAIState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAIState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Baoli_EAIState, (UObject*)Z_Construct_UPackage__Script_Baoli(), TEXT("EAIState"));
	}
	return Z_Registration_Info_UEnum_EAIState.OuterSingleton;
}
template<> BAOLI_API UEnum* StaticEnum<EAIState>()
{
	return EAIState_StaticEnum();
}
struct Z_Construct_UEnum_Baoli_EAIState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Chasing.Name", "EAIState::Chasing" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// AI State enumeration\n" },
#endif
		{ "Idle.Name", "EAIState::Idle" },
		{ "Investigating.Name", "EAIState::Investigating" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
		{ "Patrolling.Name", "EAIState::Patrolling" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI State enumeration" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAIState::Idle", (int64)EAIState::Idle },
		{ "EAIState::Patrolling", (int64)EAIState::Patrolling },
		{ "EAIState::Chasing", (int64)EAIState::Chasing },
		{ "EAIState::Investigating", (int64)EAIState::Investigating },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Baoli_EAIState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Baoli,
	nullptr,
	"EAIState",
	"EAIState",
	Z_Construct_UEnum_Baoli_EAIState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Baoli_EAIState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Baoli_EAIState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Baoli_EAIState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Baoli_EAIState()
{
	if (!Z_Registration_Info_UEnum_EAIState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAIState.InnerSingleton, Z_Construct_UEnum_Baoli_EAIState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAIState.InnerSingleton;
}
// End Enum EAIState

// Begin Class AMyAIController Function CalcDesiredRotationArc
struct Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics
{
	struct MyAIController_eventCalcDesiredRotationArc_Parms
	{
		TArray<FVector> PathPointsArray;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Called to perform arcs\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called to perform arcs" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PathPointsArray_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PathPointsArray;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::NewProp_PathPointsArray_Inner = { "PathPointsArray", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::NewProp_PathPointsArray = { "PathPointsArray", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventCalcDesiredRotationArc_Parms, PathPointsArray), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::NewProp_PathPointsArray_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::NewProp_PathPointsArray,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "CalcDesiredRotationArc", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::MyAIController_eventCalcDesiredRotationArc_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::MyAIController_eventCalcDesiredRotationArc_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execCalcDesiredRotationArc)
{
	P_GET_TARRAY(FVector,Z_Param_PathPointsArray);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CalcDesiredRotationArc(Z_Param_PathPointsArray);
	P_NATIVE_END;
}
// End Class AMyAIController Function CalcDesiredRotationArc

// Begin Class AMyAIController Function DebugDrawPath
struct Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics
{
	struct MyAIController_eventDebugDrawPath_Parms
	{
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug function to visualize the path\n" },
#endif
		{ "CPP_Default_Duration", "1.000000" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug function to visualize the path" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventDebugDrawPath_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "DebugDrawPath", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::MyAIController_eventDebugDrawPath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::MyAIController_eventDebugDrawPath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_DebugDrawPath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_DebugDrawPath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execDebugDrawPath)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugDrawPath(Z_Param_Duration);
	P_NATIVE_END;
}
// End Class AMyAIController Function DebugDrawPath

// Begin Class AMyAIController Function DebugDrawPatrolArea
struct Z_Construct_UFunction_AMyAIController_DebugDrawPatrolArea_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug function to visualize patrol point generation\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug function to visualize patrol point generation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_DebugDrawPatrolArea_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "DebugDrawPatrolArea", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_DebugDrawPatrolArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_DebugDrawPatrolArea_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AMyAIController_DebugDrawPatrolArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_DebugDrawPatrolArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execDebugDrawPatrolArea)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DebugDrawPatrolArea();
	P_NATIVE_END;
}
// End Class AMyAIController Function DebugDrawPatrolArea

// Begin Class AMyAIController Function FindSmoothPathSynchronously
struct Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics
{
	struct MyAIController_eventFindSmoothPathSynchronously_Parms
	{
		FVector StartLocation;
		FVector EndLocation;
		AActor* TargetActor;
		float AcceptanceRadius;
		UNavigationPath* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// New function to find a smooth path with curved trajectories\n" },
#endif
		{ "CPP_Default_AcceptanceRadius", "50.000000" },
		{ "CPP_Default_TargetActor", "None" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "New function to find a smooth path with curved trajectories" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndLocation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AcceptanceRadius;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::NewProp_StartLocation = { "StartLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventFindSmoothPathSynchronously_Parms, StartLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartLocation_MetaData), NewProp_StartLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::NewProp_EndLocation = { "EndLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventFindSmoothPathSynchronously_Parms, EndLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndLocation_MetaData), NewProp_EndLocation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventFindSmoothPathSynchronously_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::NewProp_AcceptanceRadius = { "AcceptanceRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventFindSmoothPathSynchronously_Parms, AcceptanceRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventFindSmoothPathSynchronously_Parms, ReturnValue), Z_Construct_UClass_UNavigationPath_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::NewProp_StartLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::NewProp_EndLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::NewProp_AcceptanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "FindSmoothPathSynchronously", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::MyAIController_eventFindSmoothPathSynchronously_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::MyAIController_eventFindSmoothPathSynchronously_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execFindSmoothPathSynchronously)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndLocation);
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_AcceptanceRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UNavigationPath**)Z_Param__Result=P_THIS->FindSmoothPathSynchronously(Z_Param_Out_StartLocation,Z_Param_Out_EndLocation,Z_Param_TargetActor,Z_Param_AcceptanceRadius);
	P_NATIVE_END;
}
// End Class AMyAIController Function FindSmoothPathSynchronously

// Begin Class AMyAIController Function GenerateRandomPatrolPoint
struct Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics
{
	struct MyAIController_eventGenerateRandomPatrolPoint_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventGenerateRandomPatrolPoint_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "GenerateRandomPatrolPoint", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::MyAIController_eventGenerateRandomPatrolPoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::MyAIController_eventGenerateRandomPatrolPoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execGenerateRandomPatrolPoint)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GenerateRandomPatrolPoint();
	P_NATIVE_END;
}
// End Class AMyAIController Function GenerateRandomPatrolPoint

// Begin Class AMyAIController Function GenerateSplinePoints
struct Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics
{
	struct MyAIController_eventGenerateSplinePoints_Parms
	{
		TArray<FVector> InPathPoints;
		int32 NumSplinePoints;
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called to generate spline points for smoother path\n" },
#endif
		{ "CPP_Default_NumSplinePoints", "10" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called to generate spline points for smoother path" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InPathPoints_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_InPathPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InPathPoints;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumSplinePoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::NewProp_InPathPoints_Inner = { "InPathPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::NewProp_InPathPoints = { "InPathPoints", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventGenerateSplinePoints_Parms, InPathPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InPathPoints_MetaData), NewProp_InPathPoints_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::NewProp_NumSplinePoints = { "NumSplinePoints", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventGenerateSplinePoints_Parms, NumSplinePoints), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventGenerateSplinePoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::NewProp_InPathPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::NewProp_InPathPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::NewProp_NumSplinePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "GenerateSplinePoints", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::MyAIController_eventGenerateSplinePoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::MyAIController_eventGenerateSplinePoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_GenerateSplinePoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_GenerateSplinePoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execGenerateSplinePoints)
{
	P_GET_TARRAY_REF(FVector,Z_Param_Out_InPathPoints);
	P_GET_PROPERTY(FIntProperty,Z_Param_NumSplinePoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GenerateSplinePoints(Z_Param_Out_InPathPoints,Z_Param_NumSplinePoints);
	P_NATIVE_END;
}
// End Class AMyAIController Function GenerateSplinePoints

// Begin Class AMyAIController Function GetAIStateAsString
struct Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics
{
	struct MyAIController_eventGetAIStateAsString_Parms
	{
		EAIState State;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventGetAIStateAsString_Parms, State), Z_Construct_UEnum_Baoli_EAIState, METADATA_PARAMS(0, nullptr) }; // 2921359283
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventGetAIStateAsString_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "GetAIStateAsString", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::MyAIController_eventGetAIStateAsString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::MyAIController_eventGetAIStateAsString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_GetAIStateAsString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_GetAIStateAsString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execGetAIStateAsString)
{
	P_GET_ENUM(EAIState,Z_Param_State);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetAIStateAsString(EAIState(Z_Param_State));
	P_NATIVE_END;
}
// End Class AMyAIController Function GetAIStateAsString

// Begin Class AMyAIController Function GetCurrentAIStateAsString
struct Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics
{
	struct MyAIController_eventGetCurrentAIStateAsString_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventGetCurrentAIStateAsString_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "GetCurrentAIStateAsString", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::MyAIController_eventGetCurrentAIStateAsString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::MyAIController_eventGetCurrentAIStateAsString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execGetCurrentAIStateAsString)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetCurrentAIStateAsString();
	P_NATIVE_END;
}
// End Class AMyAIController Function GetCurrentAIStateAsString

// Begin Class AMyAIController Function GoToNextPatrolPoint
struct Z_Construct_UFunction_AMyAIController_GoToNextPatrolPoint_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_GoToNextPatrolPoint_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "GoToNextPatrolPoint", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_GoToNextPatrolPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_GoToNextPatrolPoint_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AMyAIController_GoToNextPatrolPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_GoToNextPatrolPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execGoToNextPatrolPoint)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GoToNextPatrolPoint();
	P_NATIVE_END;
}
// End Class AMyAIController Function GoToNextPatrolPoint

// Begin Class AMyAIController Function IsActorReachable
struct Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics
{
	struct MyAIController_eventIsActorReachable_Parms
	{
		AActor* TargetActor;
		float OutDistance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Function to check if the target actor is reachable\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Function to check if the target actor is reachable" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OutDistance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventIsActorReachable_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::NewProp_OutDistance = { "OutDistance", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventIsActorReachable_Parms, OutDistance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MyAIController_eventIsActorReachable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MyAIController_eventIsActorReachable_Parms), &Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::NewProp_OutDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "IsActorReachable", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::MyAIController_eventIsActorReachable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::MyAIController_eventIsActorReachable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_IsActorReachable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_IsActorReachable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execIsActorReachable)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY_REF(FFloatProperty,Z_Param_Out_OutDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsActorReachable(Z_Param_TargetActor,Z_Param_Out_OutDistance);
	P_NATIVE_END;
}
// End Class AMyAIController Function IsActorReachable

// Begin Class AMyAIController Function IsLocationValid
struct Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics
{
	struct MyAIController_eventIsLocationValid_Parms
	{
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventIsLocationValid_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MyAIController_eventIsLocationValid_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MyAIController_eventIsLocationValid_Parms), &Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "IsLocationValid", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::MyAIController_eventIsLocationValid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::MyAIController_eventIsLocationValid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_IsLocationValid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_IsLocationValid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execIsLocationValid)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLocationValid(Z_Param_Out_Location);
	P_NATIVE_END;
}
// End Class AMyAIController Function IsLocationValid

// Begin Class AMyAIController Function IsLocationValidForPatrol
struct Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics
{
	struct MyAIController_eventIsLocationValidForPatrol_Parms
	{
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventIsLocationValidForPatrol_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MyAIController_eventIsLocationValidForPatrol_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MyAIController_eventIsLocationValidForPatrol_Parms), &Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "IsLocationValidForPatrol", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::MyAIController_eventIsLocationValidForPatrol_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::MyAIController_eventIsLocationValidForPatrol_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execIsLocationValidForPatrol)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLocationValidForPatrol(Z_Param_Out_Location);
	P_NATIVE_END;
}
// End Class AMyAIController Function IsLocationValidForPatrol

// Begin Class AMyAIController Function MoveToActorSmoothly
struct Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics
{
	struct MyAIController_eventMoveToActorSmoothly_Parms
	{
		AActor* TargetActor;
		float AcceptanceRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint callable function to move AI to an actor using path finding\n" },
#endif
		{ "CPP_Default_AcceptanceRadius", "50.000000" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint callable function to move AI to an actor using path finding" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AcceptanceRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventMoveToActorSmoothly_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::NewProp_AcceptanceRadius = { "AcceptanceRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventMoveToActorSmoothly_Parms, AcceptanceRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::NewProp_AcceptanceRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "MoveToActorSmoothly", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::MyAIController_eventMoveToActorSmoothly_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::MyAIController_eventMoveToActorSmoothly_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execMoveToActorSmoothly)
{
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_AcceptanceRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MoveToActorSmoothly(Z_Param_TargetActor,Z_Param_AcceptanceRadius);
	P_NATIVE_END;
}
// End Class AMyAIController Function MoveToActorSmoothly

// Begin Class AMyAIController Function MoveToLocationSmooth
struct Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics
{
	struct MyAIController_eventMoveToLocationSmooth_Parms
	{
		FVector Destination;
		AActor* TargetActor;
		float AcceptanceRadius;
		UAITask_MoveTo* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called to move AI using AIMoveTo task\n" },
#endif
		{ "CPP_Default_AcceptanceRadius", "50.000000" },
		{ "CPP_Default_TargetActor", "None" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called to move AI using AIMoveTo task" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Destination;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AcceptanceRadius;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::NewProp_Destination = { "Destination", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventMoveToLocationSmooth_Parms, Destination), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventMoveToLocationSmooth_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::NewProp_AcceptanceRadius = { "AcceptanceRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventMoveToLocationSmooth_Parms, AcceptanceRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventMoveToLocationSmooth_Parms, ReturnValue), Z_Construct_UClass_UAITask_MoveTo_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::NewProp_Destination,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::NewProp_AcceptanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "MoveToLocationSmooth", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::MyAIController_eventMoveToLocationSmooth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::MyAIController_eventMoveToLocationSmooth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execMoveToLocationSmooth)
{
	P_GET_STRUCT(FVector,Z_Param_Destination);
	P_GET_OBJECT(AActor,Z_Param_TargetActor);
	P_GET_PROPERTY(FFloatProperty,Z_Param_AcceptanceRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UAITask_MoveTo**)Z_Param__Result=P_THIS->MoveToLocationSmooth(Z_Param_Destination,Z_Param_TargetActor,Z_Param_AcceptanceRadius);
	P_NATIVE_END;
}
// End Class AMyAIController Function MoveToLocationSmooth

// Begin Class AMyAIController Function MoveToLocationSmoothly
struct Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics
{
	struct MyAIController_eventMoveToLocationSmoothly_Parms
	{
		FVector Destination;
		float AcceptanceRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint callable function to move AI to a location using path finding\n" },
#endif
		{ "CPP_Default_AcceptanceRadius", "50.000000" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint callable function to move AI to a location using path finding" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Destination;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AcceptanceRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::NewProp_Destination = { "Destination", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventMoveToLocationSmoothly_Parms, Destination), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::NewProp_AcceptanceRadius = { "AcceptanceRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventMoveToLocationSmoothly_Parms, AcceptanceRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::NewProp_Destination,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::NewProp_AcceptanceRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "MoveToLocationSmoothly", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::MyAIController_eventMoveToLocationSmoothly_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::MyAIController_eventMoveToLocationSmoothly_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execMoveToLocationSmoothly)
{
	P_GET_STRUCT(FVector,Z_Param_Destination);
	P_GET_PROPERTY(FFloatProperty,Z_Param_AcceptanceRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MoveToLocationSmoothly(Z_Param_Destination,Z_Param_AcceptanceRadius);
	P_NATIVE_END;
}
// End Class AMyAIController Function MoveToLocationSmoothly

// Begin Class AMyAIController Function OnInvestigationComplete
struct Z_Construct_UFunction_AMyAIController_OnInvestigationComplete_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_OnInvestigationComplete_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "OnInvestigationComplete", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_OnInvestigationComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_OnInvestigationComplete_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AMyAIController_OnInvestigationComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_OnInvestigationComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execOnInvestigationComplete)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnInvestigationComplete();
	P_NATIVE_END;
}
// End Class AMyAIController Function OnInvestigationComplete

// Begin Class AMyAIController Function OnTargetLost
struct Z_Construct_UFunction_AMyAIController_OnTargetLost_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_OnTargetLost_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "OnTargetLost", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_OnTargetLost_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_OnTargetLost_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AMyAIController_OnTargetLost()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_OnTargetLost_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execOnTargetLost)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnTargetLost();
	P_NATIVE_END;
}
// End Class AMyAIController Function OnTargetLost

// Begin Class AMyAIController Function OnTargetPerceptionUpdated
struct Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics
{
	struct MyAIController_eventOnTargetPerceptionUpdated_Parms
	{
		AActor* Actor;
		FAIStimulus Stimulus;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Perception functions\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perception functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Stimulus;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventOnTargetPerceptionUpdated_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::NewProp_Stimulus = { "Stimulus", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventOnTargetPerceptionUpdated_Parms, Stimulus), Z_Construct_UScriptStruct_FAIStimulus, METADATA_PARAMS(0, nullptr) }; // 177100813
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::NewProp_Stimulus,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "OnTargetPerceptionUpdated", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::MyAIController_eventOnTargetPerceptionUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::MyAIController_eventOnTargetPerceptionUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execOnTargetPerceptionUpdated)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_GET_STRUCT(FAIStimulus,Z_Param_Stimulus);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnTargetPerceptionUpdated(Z_Param_Actor,Z_Param_Stimulus);
	P_NATIVE_END;
}
// End Class AMyAIController Function OnTargetPerceptionUpdated

// Begin Class AMyAIController Function OnTargetSpotted
struct Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics
{
	struct MyAIController_eventOnTargetSpotted_Parms
	{
		AActor* SpottedActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpottedActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::NewProp_SpottedActor = { "SpottedActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventOnTargetSpotted_Parms, SpottedActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::NewProp_SpottedActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "OnTargetSpotted", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::MyAIController_eventOnTargetSpotted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::MyAIController_eventOnTargetSpotted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_OnTargetSpotted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_OnTargetSpotted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execOnTargetSpotted)
{
	P_GET_OBJECT(AActor,Z_Param_SpottedActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnTargetSpotted(Z_Param_SpottedActor);
	P_NATIVE_END;
}
// End Class AMyAIController Function OnTargetSpotted

// Begin Class AMyAIController Function ProjectPointToNavMesh
struct Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics
{
	struct MyAIController_eventProjectPointToNavMesh_Parms
	{
		FVector Point;
		FVector ProjectedPoint;
		FVector Extent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Helper function to project a point onto the navigation mesh\n" },
#endif
		{ "CPP_Default_Extent", "500.000000,500.000000,1000.000000" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Helper function to project a point onto the navigation mesh" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Point_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Extent_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Point;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProjectedPoint;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Extent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::NewProp_Point = { "Point", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventProjectPointToNavMesh_Parms, Point), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Point_MetaData), NewProp_Point_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::NewProp_ProjectedPoint = { "ProjectedPoint", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventProjectPointToNavMesh_Parms, ProjectedPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::NewProp_Extent = { "Extent", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventProjectPointToNavMesh_Parms, Extent), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Extent_MetaData), NewProp_Extent_MetaData) };
void Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((MyAIController_eventProjectPointToNavMesh_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(MyAIController_eventProjectPointToNavMesh_Parms), &Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::NewProp_Point,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::NewProp_ProjectedPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::NewProp_Extent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "ProjectPointToNavMesh", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::MyAIController_eventProjectPointToNavMesh_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::MyAIController_eventProjectPointToNavMesh_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execProjectPointToNavMesh)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Point);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ProjectedPoint);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Extent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ProjectPointToNavMesh(Z_Param_Out_Point,Z_Param_Out_ProjectedPoint,Z_Param_Out_Extent);
	P_NATIVE_END;
}
// End Class AMyAIController Function ProjectPointToNavMesh

// Begin Class AMyAIController Function RotateTowardsMovementDirection
struct Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics
{
	struct MyAIController_eventRotateTowardsMovementDirection_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called to rotate AI smoothly towards movement direction\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called to rotate AI smoothly towards movement direction" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventRotateTowardsMovementDirection_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "RotateTowardsMovementDirection", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::MyAIController_eventRotateTowardsMovementDirection_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::MyAIController_eventRotateTowardsMovementDirection_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execRotateTowardsMovementDirection)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RotateTowardsMovementDirection(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// End Class AMyAIController Function RotateTowardsMovementDirection

// Begin Class AMyAIController Function SetAIState
struct Z_Construct_UFunction_AMyAIController_SetAIState_Statics
{
	struct MyAIController_eventSetAIState_Parms
	{
		EAIState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// State management functions\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "State management functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AMyAIController_SetAIState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AMyAIController_SetAIState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(MyAIController_eventSetAIState_Parms, NewState), Z_Construct_UEnum_Baoli_EAIState, METADATA_PARAMS(0, nullptr) }; // 2921359283
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AMyAIController_SetAIState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_SetAIState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AMyAIController_SetAIState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_SetAIState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_SetAIState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "SetAIState", nullptr, nullptr, Z_Construct_UFunction_AMyAIController_SetAIState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_SetAIState_Statics::PropPointers), sizeof(Z_Construct_UFunction_AMyAIController_SetAIState_Statics::MyAIController_eventSetAIState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_SetAIState_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_SetAIState_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AMyAIController_SetAIState_Statics::MyAIController_eventSetAIState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AMyAIController_SetAIState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_SetAIState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execSetAIState)
{
	P_GET_ENUM(EAIState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAIState(EAIState(Z_Param_NewState));
	P_NATIVE_END;
}
// End Class AMyAIController Function SetAIState

// Begin Class AMyAIController Function StartInvestigating
struct Z_Construct_UFunction_AMyAIController_StartInvestigating_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_StartInvestigating_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "StartInvestigating", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_StartInvestigating_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_StartInvestigating_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AMyAIController_StartInvestigating()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_StartInvestigating_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execStartInvestigating)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartInvestigating();
	P_NATIVE_END;
}
// End Class AMyAIController Function StartInvestigating

// Begin Class AMyAIController Function StartPatrolling
struct Z_Construct_UFunction_AMyAIController_StartPatrolling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Patrol functions\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Patrol functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_StartPatrolling_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "StartPatrolling", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_StartPatrolling_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_StartPatrolling_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AMyAIController_StartPatrolling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_StartPatrolling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execStartPatrolling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartPatrolling();
	P_NATIVE_END;
}
// End Class AMyAIController Function StartPatrolling

// Begin Class AMyAIController Function StopPatrolling
struct Z_Construct_UFunction_AMyAIController_StopPatrolling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_StopPatrolling_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "StopPatrolling", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_StopPatrolling_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_StopPatrolling_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AMyAIController_StopPatrolling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_StopPatrolling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execStopPatrolling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopPatrolling();
	P_NATIVE_END;
}
// End Class AMyAIController Function StopPatrolling

// Begin Class AMyAIController Function StopTrackingActor
struct Z_Construct_UFunction_AMyAIController_StopTrackingActor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Function to stop tracking the current target actor\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Function to stop tracking the current target actor" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_StopTrackingActor_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "StopTrackingActor", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_StopTrackingActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_StopTrackingActor_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AMyAIController_StopTrackingActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_StopTrackingActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execStopTrackingActor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopTrackingActor();
	P_NATIVE_END;
}
// End Class AMyAIController Function StopTrackingActor

// Begin Class AMyAIController Function TestNavigationAroundPatrolCenter
struct Z_Construct_UFunction_AMyAIController_TestNavigationAroundPatrolCenter_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug function to test navigation mesh around patrol center\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug function to test navigation mesh around patrol center" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_TestNavigationAroundPatrolCenter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "TestNavigationAroundPatrolCenter", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_TestNavigationAroundPatrolCenter_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_TestNavigationAroundPatrolCenter_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AMyAIController_TestNavigationAroundPatrolCenter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_TestNavigationAroundPatrolCenter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execTestNavigationAroundPatrolCenter)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TestNavigationAroundPatrolCenter();
	P_NATIVE_END;
}
// End Class AMyAIController Function TestNavigationAroundPatrolCenter

// Begin Class AMyAIController Function UpdatePathToTargetActor
struct Z_Construct_UFunction_AMyAIController_UpdatePathToTargetActor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Function to update the path to the target actor\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Function to update the path to the target actor" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AMyAIController_UpdatePathToTargetActor_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AMyAIController, nullptr, "UpdatePathToTargetActor", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AMyAIController_UpdatePathToTargetActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_AMyAIController_UpdatePathToTargetActor_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AMyAIController_UpdatePathToTargetActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AMyAIController_UpdatePathToTargetActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AMyAIController::execUpdatePathToTargetActor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePathToTargetActor();
	P_NATIVE_END;
}
// End Class AMyAIController Function UpdatePathToTargetActor

// Begin Class AMyAIController
void AMyAIController::StaticRegisterNativesAMyAIController()
{
	UClass* Class = AMyAIController::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalcDesiredRotationArc", &AMyAIController::execCalcDesiredRotationArc },
		{ "DebugDrawPath", &AMyAIController::execDebugDrawPath },
		{ "DebugDrawPatrolArea", &AMyAIController::execDebugDrawPatrolArea },
		{ "FindSmoothPathSynchronously", &AMyAIController::execFindSmoothPathSynchronously },
		{ "GenerateRandomPatrolPoint", &AMyAIController::execGenerateRandomPatrolPoint },
		{ "GenerateSplinePoints", &AMyAIController::execGenerateSplinePoints },
		{ "GetAIStateAsString", &AMyAIController::execGetAIStateAsString },
		{ "GetCurrentAIStateAsString", &AMyAIController::execGetCurrentAIStateAsString },
		{ "GoToNextPatrolPoint", &AMyAIController::execGoToNextPatrolPoint },
		{ "IsActorReachable", &AMyAIController::execIsActorReachable },
		{ "IsLocationValid", &AMyAIController::execIsLocationValid },
		{ "IsLocationValidForPatrol", &AMyAIController::execIsLocationValidForPatrol },
		{ "MoveToActorSmoothly", &AMyAIController::execMoveToActorSmoothly },
		{ "MoveToLocationSmooth", &AMyAIController::execMoveToLocationSmooth },
		{ "MoveToLocationSmoothly", &AMyAIController::execMoveToLocationSmoothly },
		{ "OnInvestigationComplete", &AMyAIController::execOnInvestigationComplete },
		{ "OnTargetLost", &AMyAIController::execOnTargetLost },
		{ "OnTargetPerceptionUpdated", &AMyAIController::execOnTargetPerceptionUpdated },
		{ "OnTargetSpotted", &AMyAIController::execOnTargetSpotted },
		{ "ProjectPointToNavMesh", &AMyAIController::execProjectPointToNavMesh },
		{ "RotateTowardsMovementDirection", &AMyAIController::execRotateTowardsMovementDirection },
		{ "SetAIState", &AMyAIController::execSetAIState },
		{ "StartInvestigating", &AMyAIController::execStartInvestigating },
		{ "StartPatrolling", &AMyAIController::execStartPatrolling },
		{ "StopPatrolling", &AMyAIController::execStopPatrolling },
		{ "StopTrackingActor", &AMyAIController::execStopTrackingActor },
		{ "TestNavigationAroundPatrolCenter", &AMyAIController::execTestNavigationAroundPatrolCenter },
		{ "UpdatePathToTargetActor", &AMyAIController::execUpdatePathToTargetActor },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AMyAIController);
UClass* Z_Construct_UClass_AMyAIController_NoRegister()
{
	return AMyAIController::StaticClass();
}
struct Z_Construct_UClass_AMyAIController_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n *\n */" },
#endif
		{ "HideCategories", "Collision Rendering Transformation" },
		{ "IncludePath", "AI/MyAIController.h" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AIPerceptionComponent_MetaData[] = {
		{ "Category", "AI Perception" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// AI Perception Component\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Perception Component" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemyCharacter_MetaData[] = {
		{ "Category", "References" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathPoints_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PointTolerance_MetaData[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesiredDirection_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAddInput_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationSpeed_MetaData[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Properties for smooth movement\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Properties for smooth movement" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSplineMovement_MetaData[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplinePointCount_MetaData[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolSplinePointCount_MetaData[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LookAheadDistance_MetaData[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAngleBetweenPoints_MetaData[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Properties for smooth path finding\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Properties for smooth path finding" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDistanceBetweenPoints_MetaData[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathSmoothingFactor_MetaData[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bProjectPointsToNavMesh_MetaData[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NavMeshProjectionExtent_MetaData[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bContinuouslyTrackActor_MetaData[] = {
		{ "Category", "Navigation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Properties for actor tracking\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Properties for actor tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorTrackingUpdateInterval_MetaData[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxOffNavMeshDistance_MetaData[] = {
		{ "Category", "Navigation" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTargetActor_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentAcceptanceRadius_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorTrackingTimerHandle_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplinePoints_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetRotation_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsMoving_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsTestMovement_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPointIndex_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Flag to indicate if this is test movement (not actor tracking)\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Flag to indicate if this is test movement (not actor tracking)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentAIState_MetaData[] = {
		{ "Category", "AI Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// AI State Management\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI State Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolRadius_MetaData[] = {
		{ "Category", "AI Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Patrol Properties\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Patrol Properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolPointAcceptanceRadius_MetaData[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InvestigationDelay_MetaData[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolWaitTime_MetaData[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastKnownTargetLocation_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolCenterLocation_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InvestigationTimerHandle_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolTimerHandle_MetaData[] = {
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SightRadius_MetaData[] = {
		{ "Category", "AI Perception" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Perception Properties\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perception Properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoseSightRadius_MetaData[] = {
		{ "Category", "AI Perception" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PeripheralVisionAngleDegrees_MetaData[] = {
		{ "Category", "AI Perception" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAge_MetaData[] = {
		{ "Category", "AI Perception" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDebugDrawPath_MetaData[] = {
		{ "Category", "Debug" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Debug properties\n" },
#endif
		{ "ModuleRelativePath", "AI/MyAIController.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Debug properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogDebugInfo_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLogStateChanges_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseSimplePatrolValidation_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathColor_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineColor_MetaData[] = {
		{ "Category", "Debug" },
		{ "ModuleRelativePath", "AI/MyAIController.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AIPerceptionComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnemyCharacter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PathPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PathPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PointTolerance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DesiredDirection;
	static void NewProp_bAddInput_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAddInput;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotationSpeed;
	static void NewProp_bUseSplineMovement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSplineMovement;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SplinePointCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PatrolSplinePointCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LookAheadDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAngleBetweenPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDistanceBetweenPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathSmoothingFactor;
	static void NewProp_bProjectPointsToNavMesh_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bProjectPointsToNavMesh;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NavMeshProjectionExtent;
	static void NewProp_bContinuouslyTrackActor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bContinuouslyTrackActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActorTrackingUpdateInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxOffNavMeshDistance;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CurrentTargetActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentAcceptanceRadius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActorTrackingTimerHandle;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SplinePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SplinePoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TargetRotation;
	static void NewProp_bIsMoving_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMoving;
	static void NewProp_bIsTestMovement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsTestMovement;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetPointIndex;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentAIState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentAIState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PatrolRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PatrolPointAcceptanceRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InvestigationDelay;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PatrolWaitTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastKnownTargetLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PatrolCenterLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InvestigationTimerHandle;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PatrolTimerHandle;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SightRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoseSightRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PeripheralVisionAngleDegrees;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAge;
	static void NewProp_bDebugDrawPath_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDebugDrawPath;
	static void NewProp_bLogDebugInfo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogDebugInfo;
	static void NewProp_bLogStateChanges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLogStateChanges;
	static void NewProp_bUseSimplePatrolValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseSimplePatrolValidation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PathColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SplineColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AMyAIController_CalcDesiredRotationArc, "CalcDesiredRotationArc" }, // 2429514743
		{ &Z_Construct_UFunction_AMyAIController_DebugDrawPath, "DebugDrawPath" }, // 252298433
		{ &Z_Construct_UFunction_AMyAIController_DebugDrawPatrolArea, "DebugDrawPatrolArea" }, // 3231274657
		{ &Z_Construct_UFunction_AMyAIController_FindSmoothPathSynchronously, "FindSmoothPathSynchronously" }, // 400485244
		{ &Z_Construct_UFunction_AMyAIController_GenerateRandomPatrolPoint, "GenerateRandomPatrolPoint" }, // 3905325952
		{ &Z_Construct_UFunction_AMyAIController_GenerateSplinePoints, "GenerateSplinePoints" }, // 3374870614
		{ &Z_Construct_UFunction_AMyAIController_GetAIStateAsString, "GetAIStateAsString" }, // 1671283356
		{ &Z_Construct_UFunction_AMyAIController_GetCurrentAIStateAsString, "GetCurrentAIStateAsString" }, // 3215255584
		{ &Z_Construct_UFunction_AMyAIController_GoToNextPatrolPoint, "GoToNextPatrolPoint" }, // 735299592
		{ &Z_Construct_UFunction_AMyAIController_IsActorReachable, "IsActorReachable" }, // 2029447118
		{ &Z_Construct_UFunction_AMyAIController_IsLocationValid, "IsLocationValid" }, // 2467719942
		{ &Z_Construct_UFunction_AMyAIController_IsLocationValidForPatrol, "IsLocationValidForPatrol" }, // 3936196719
		{ &Z_Construct_UFunction_AMyAIController_MoveToActorSmoothly, "MoveToActorSmoothly" }, // 3456832560
		{ &Z_Construct_UFunction_AMyAIController_MoveToLocationSmooth, "MoveToLocationSmooth" }, // 3255674042
		{ &Z_Construct_UFunction_AMyAIController_MoveToLocationSmoothly, "MoveToLocationSmoothly" }, // 3753051209
		{ &Z_Construct_UFunction_AMyAIController_OnInvestigationComplete, "OnInvestigationComplete" }, // 3739198910
		{ &Z_Construct_UFunction_AMyAIController_OnTargetLost, "OnTargetLost" }, // 2084765760
		{ &Z_Construct_UFunction_AMyAIController_OnTargetPerceptionUpdated, "OnTargetPerceptionUpdated" }, // 1473899728
		{ &Z_Construct_UFunction_AMyAIController_OnTargetSpotted, "OnTargetSpotted" }, // 2195952021
		{ &Z_Construct_UFunction_AMyAIController_ProjectPointToNavMesh, "ProjectPointToNavMesh" }, // 2771957762
		{ &Z_Construct_UFunction_AMyAIController_RotateTowardsMovementDirection, "RotateTowardsMovementDirection" }, // 1879729390
		{ &Z_Construct_UFunction_AMyAIController_SetAIState, "SetAIState" }, // 225235704
		{ &Z_Construct_UFunction_AMyAIController_StartInvestigating, "StartInvestigating" }, // 871169045
		{ &Z_Construct_UFunction_AMyAIController_StartPatrolling, "StartPatrolling" }, // 2991466578
		{ &Z_Construct_UFunction_AMyAIController_StopPatrolling, "StopPatrolling" }, // 875585829
		{ &Z_Construct_UFunction_AMyAIController_StopTrackingActor, "StopTrackingActor" }, // 3885542740
		{ &Z_Construct_UFunction_AMyAIController_TestNavigationAroundPatrolCenter, "TestNavigationAroundPatrolCenter" }, // 3710165903
		{ &Z_Construct_UFunction_AMyAIController_UpdatePathToTargetActor, "UpdatePathToTargetActor" }, // 4066536845
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AMyAIController>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_AIPerceptionComponent = { "AIPerceptionComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, AIPerceptionComponent), Z_Construct_UClass_UAIPerceptionComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AIPerceptionComponent_MetaData), NewProp_AIPerceptionComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_EnemyCharacter = { "EnemyCharacter", nullptr, (EPropertyFlags)0x0010000000020005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, EnemyCharacter), Z_Construct_UClass_AAI_CharacterBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemyCharacter_MetaData), NewProp_EnemyCharacter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PathPoints_Inner = { "PathPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PathPoints = { "PathPoints", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, PathPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathPoints_MetaData), NewProp_PathPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PointTolerance = { "PointTolerance", nullptr, (EPropertyFlags)0x0010000000020005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, PointTolerance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PointTolerance_MetaData), NewProp_PointTolerance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_DesiredDirection = { "DesiredDirection", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, DesiredDirection), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesiredDirection_MetaData), NewProp_DesiredDirection_MetaData) };
void Z_Construct_UClass_AMyAIController_Statics::NewProp_bAddInput_SetBit(void* Obj)
{
	((AMyAIController*)Obj)->bAddInput = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_bAddInput = { "bAddInput", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMyAIController), &Z_Construct_UClass_AMyAIController_Statics::NewProp_bAddInput_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAddInput_MetaData), NewProp_bAddInput_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_RotationSpeed = { "RotationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, RotationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationSpeed_MetaData), NewProp_RotationSpeed_MetaData) };
void Z_Construct_UClass_AMyAIController_Statics::NewProp_bUseSplineMovement_SetBit(void* Obj)
{
	((AMyAIController*)Obj)->bUseSplineMovement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_bUseSplineMovement = { "bUseSplineMovement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMyAIController), &Z_Construct_UClass_AMyAIController_Statics::NewProp_bUseSplineMovement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSplineMovement_MetaData), NewProp_bUseSplineMovement_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_SplinePointCount = { "SplinePointCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, SplinePointCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplinePointCount_MetaData), NewProp_SplinePointCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolSplinePointCount = { "PatrolSplinePointCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, PatrolSplinePointCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolSplinePointCount_MetaData), NewProp_PatrolSplinePointCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_LookAheadDistance = { "LookAheadDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, LookAheadDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LookAheadDistance_MetaData), NewProp_LookAheadDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_MaxAngleBetweenPoints = { "MaxAngleBetweenPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, MaxAngleBetweenPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAngleBetweenPoints_MetaData), NewProp_MaxAngleBetweenPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_MinDistanceBetweenPoints = { "MinDistanceBetweenPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, MinDistanceBetweenPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDistanceBetweenPoints_MetaData), NewProp_MinDistanceBetweenPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PathSmoothingFactor = { "PathSmoothingFactor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, PathSmoothingFactor), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathSmoothingFactor_MetaData), NewProp_PathSmoothingFactor_MetaData) };
void Z_Construct_UClass_AMyAIController_Statics::NewProp_bProjectPointsToNavMesh_SetBit(void* Obj)
{
	((AMyAIController*)Obj)->bProjectPointsToNavMesh = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_bProjectPointsToNavMesh = { "bProjectPointsToNavMesh", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMyAIController), &Z_Construct_UClass_AMyAIController_Statics::NewProp_bProjectPointsToNavMesh_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bProjectPointsToNavMesh_MetaData), NewProp_bProjectPointsToNavMesh_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_NavMeshProjectionExtent = { "NavMeshProjectionExtent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, NavMeshProjectionExtent), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NavMeshProjectionExtent_MetaData), NewProp_NavMeshProjectionExtent_MetaData) };
void Z_Construct_UClass_AMyAIController_Statics::NewProp_bContinuouslyTrackActor_SetBit(void* Obj)
{
	((AMyAIController*)Obj)->bContinuouslyTrackActor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_bContinuouslyTrackActor = { "bContinuouslyTrackActor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMyAIController), &Z_Construct_UClass_AMyAIController_Statics::NewProp_bContinuouslyTrackActor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bContinuouslyTrackActor_MetaData), NewProp_bContinuouslyTrackActor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_ActorTrackingUpdateInterval = { "ActorTrackingUpdateInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, ActorTrackingUpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorTrackingUpdateInterval_MetaData), NewProp_ActorTrackingUpdateInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_MaxOffNavMeshDistance = { "MaxOffNavMeshDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, MaxOffNavMeshDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxOffNavMeshDistance_MetaData), NewProp_MaxOffNavMeshDistance_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_CurrentTargetActor = { "CurrentTargetActor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, CurrentTargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTargetActor_MetaData), NewProp_CurrentTargetActor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_CurrentAcceptanceRadius = { "CurrentAcceptanceRadius", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, CurrentAcceptanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentAcceptanceRadius_MetaData), NewProp_CurrentAcceptanceRadius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_ActorTrackingTimerHandle = { "ActorTrackingTimerHandle", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, ActorTrackingTimerHandle), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorTrackingTimerHandle_MetaData), NewProp_ActorTrackingTimerHandle_MetaData) }; // 756291145
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_SplinePoints_Inner = { "SplinePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_SplinePoints = { "SplinePoints", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, SplinePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplinePoints_MetaData), NewProp_SplinePoints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_TargetRotation = { "TargetRotation", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, TargetRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetRotation_MetaData), NewProp_TargetRotation_MetaData) };
void Z_Construct_UClass_AMyAIController_Statics::NewProp_bIsMoving_SetBit(void* Obj)
{
	((AMyAIController*)Obj)->bIsMoving = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_bIsMoving = { "bIsMoving", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMyAIController), &Z_Construct_UClass_AMyAIController_Statics::NewProp_bIsMoving_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsMoving_MetaData), NewProp_bIsMoving_MetaData) };
void Z_Construct_UClass_AMyAIController_Statics::NewProp_bIsTestMovement_SetBit(void* Obj)
{
	((AMyAIController*)Obj)->bIsTestMovement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_bIsTestMovement = { "bIsTestMovement", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMyAIController), &Z_Construct_UClass_AMyAIController_Statics::NewProp_bIsTestMovement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsTestMovement_MetaData), NewProp_bIsTestMovement_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_TargetPointIndex = { "TargetPointIndex", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, TargetPointIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPointIndex_MetaData), NewProp_TargetPointIndex_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_CurrentAIState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_CurrentAIState = { "CurrentAIState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, CurrentAIState), Z_Construct_UEnum_Baoli_EAIState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentAIState_MetaData), NewProp_CurrentAIState_MetaData) }; // 2921359283
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolRadius = { "PatrolRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, PatrolRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolRadius_MetaData), NewProp_PatrolRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolPointAcceptanceRadius = { "PatrolPointAcceptanceRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, PatrolPointAcceptanceRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolPointAcceptanceRadius_MetaData), NewProp_PatrolPointAcceptanceRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_InvestigationDelay = { "InvestigationDelay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, InvestigationDelay), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InvestigationDelay_MetaData), NewProp_InvestigationDelay_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolWaitTime = { "PatrolWaitTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, PatrolWaitTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolWaitTime_MetaData), NewProp_PatrolWaitTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_LastKnownTargetLocation = { "LastKnownTargetLocation", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, LastKnownTargetLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastKnownTargetLocation_MetaData), NewProp_LastKnownTargetLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolCenterLocation = { "PatrolCenterLocation", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, PatrolCenterLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolCenterLocation_MetaData), NewProp_PatrolCenterLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_InvestigationTimerHandle = { "InvestigationTimerHandle", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, InvestigationTimerHandle), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InvestigationTimerHandle_MetaData), NewProp_InvestigationTimerHandle_MetaData) }; // 756291145
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolTimerHandle = { "PatrolTimerHandle", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, PatrolTimerHandle), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolTimerHandle_MetaData), NewProp_PatrolTimerHandle_MetaData) }; // 756291145
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_SightRadius = { "SightRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, SightRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SightRadius_MetaData), NewProp_SightRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_LoseSightRadius = { "LoseSightRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, LoseSightRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoseSightRadius_MetaData), NewProp_LoseSightRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PeripheralVisionAngleDegrees = { "PeripheralVisionAngleDegrees", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, PeripheralVisionAngleDegrees), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PeripheralVisionAngleDegrees_MetaData), NewProp_PeripheralVisionAngleDegrees_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_MaxAge = { "MaxAge", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, MaxAge), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAge_MetaData), NewProp_MaxAge_MetaData) };
void Z_Construct_UClass_AMyAIController_Statics::NewProp_bDebugDrawPath_SetBit(void* Obj)
{
	((AMyAIController*)Obj)->bDebugDrawPath = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_bDebugDrawPath = { "bDebugDrawPath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMyAIController), &Z_Construct_UClass_AMyAIController_Statics::NewProp_bDebugDrawPath_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDebugDrawPath_MetaData), NewProp_bDebugDrawPath_MetaData) };
void Z_Construct_UClass_AMyAIController_Statics::NewProp_bLogDebugInfo_SetBit(void* Obj)
{
	((AMyAIController*)Obj)->bLogDebugInfo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_bLogDebugInfo = { "bLogDebugInfo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMyAIController), &Z_Construct_UClass_AMyAIController_Statics::NewProp_bLogDebugInfo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogDebugInfo_MetaData), NewProp_bLogDebugInfo_MetaData) };
void Z_Construct_UClass_AMyAIController_Statics::NewProp_bLogStateChanges_SetBit(void* Obj)
{
	((AMyAIController*)Obj)->bLogStateChanges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_bLogStateChanges = { "bLogStateChanges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMyAIController), &Z_Construct_UClass_AMyAIController_Statics::NewProp_bLogStateChanges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLogStateChanges_MetaData), NewProp_bLogStateChanges_MetaData) };
void Z_Construct_UClass_AMyAIController_Statics::NewProp_bUseSimplePatrolValidation_SetBit(void* Obj)
{
	((AMyAIController*)Obj)->bUseSimplePatrolValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_bUseSimplePatrolValidation = { "bUseSimplePatrolValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AMyAIController), &Z_Construct_UClass_AMyAIController_Statics::NewProp_bUseSimplePatrolValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseSimplePatrolValidation_MetaData), NewProp_bUseSimplePatrolValidation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_PathColor = { "PathColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, PathColor), Z_Construct_UScriptStruct_FColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathColor_MetaData), NewProp_PathColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AMyAIController_Statics::NewProp_SplineColor = { "SplineColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AMyAIController, SplineColor), Z_Construct_UScriptStruct_FColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineColor_MetaData), NewProp_SplineColor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AMyAIController_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_AIPerceptionComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_EnemyCharacter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PathPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PathPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PointTolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_DesiredDirection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_bAddInput,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_RotationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_bUseSplineMovement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_SplinePointCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolSplinePointCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_LookAheadDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_MaxAngleBetweenPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_MinDistanceBetweenPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PathSmoothingFactor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_bProjectPointsToNavMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_NavMeshProjectionExtent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_bContinuouslyTrackActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_ActorTrackingUpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_MaxOffNavMeshDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_CurrentTargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_CurrentAcceptanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_ActorTrackingTimerHandle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_SplinePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_SplinePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_TargetRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_bIsMoving,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_bIsTestMovement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_TargetPointIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_CurrentAIState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_CurrentAIState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolPointAcceptanceRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_InvestigationDelay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolWaitTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_LastKnownTargetLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolCenterLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_InvestigationTimerHandle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PatrolTimerHandle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_SightRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_LoseSightRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PeripheralVisionAngleDegrees,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_MaxAge,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_bDebugDrawPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_bLogDebugInfo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_bLogStateChanges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_bUseSimplePatrolValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_PathColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AMyAIController_Statics::NewProp_SplineColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AMyAIController_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AMyAIController_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AAIController,
	(UObject* (*)())Z_Construct_UPackage__Script_Baoli,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AMyAIController_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AMyAIController_Statics::ClassParams = {
	&AMyAIController::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AMyAIController_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AMyAIController_Statics::PropPointers),
	0,
	0x009002A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AMyAIController_Statics::Class_MetaDataParams), Z_Construct_UClass_AMyAIController_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AMyAIController()
{
	if (!Z_Registration_Info_UClass_AMyAIController.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AMyAIController.OuterSingleton, Z_Construct_UClass_AMyAIController_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AMyAIController.OuterSingleton;
}
template<> BAOLI_API UClass* StaticClass<AMyAIController>()
{
	return AMyAIController::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AMyAIController);
AMyAIController::~AMyAIController() {}
// End Class AMyAIController

// Begin Registration
struct Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_AI_MyAIController_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAIState_StaticEnum, TEXT("EAIState"), &Z_Registration_Info_UEnum_EAIState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2921359283U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AMyAIController, AMyAIController::StaticClass, TEXT("AMyAIController"), &Z_Registration_Info_UClass_AMyAIController, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AMyAIController), 3760122912U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_AI_MyAIController_h_976185385(TEXT("/Script/Baoli"),
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_AI_MyAIController_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_AI_MyAIController_h_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_AI_MyAIController_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_AI_MyAIController_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
