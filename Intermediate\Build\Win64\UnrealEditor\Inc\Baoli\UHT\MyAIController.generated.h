// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AI/MyAIController.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class AActor;
class UAITask_MoveTo;
class UNavigationPath;
enum class EAIState : uint8;
struct FAIStimulus;
#ifdef BAOLI_MyAIController_generated_h
#error "MyAIController.generated.h already included, missing '#pragma once' in MyAIController.h"
#endif
#define BAOLI_MyAIController_generated_h

#define FID_Baoli_Source_Baoli_AI_MyAIController_h_35_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnInvestigationComplete); \
	DECLARE_FUNCTION(execStartInvestigating); \
	DECLARE_FUNCTION(execOnTargetLost); \
	DECLARE_FUNCTION(execOnTargetSpotted); \
	DECLARE_FUNCTION(execOnTargetPerceptionUpdated); \
	DECLARE_FUNCTION(execGoToNextPatrolPoint); \
	DECLARE_FUNCTION(execDebugDrawPatrolArea); \
	DECLARE_FUNCTION(execTestNavigationAroundPatrolCenter); \
	DECLARE_FUNCTION(execIsLocationValidForPatrol); \
	DECLARE_FUNCTION(execIsLocationValid); \
	DECLARE_FUNCTION(execGenerateRandomPatrolPoint); \
	DECLARE_FUNCTION(execStopPatrolling); \
	DECLARE_FUNCTION(execStartPatrolling); \
	DECLARE_FUNCTION(execGetCurrentAIStateAsString); \
	DECLARE_FUNCTION(execGetAIStateAsString); \
	DECLARE_FUNCTION(execSetAIState); \
	DECLARE_FUNCTION(execStopTrackingActor); \
	DECLARE_FUNCTION(execIsActorReachable); \
	DECLARE_FUNCTION(execUpdatePathToTargetActor); \
	DECLARE_FUNCTION(execDebugDrawPath); \
	DECLARE_FUNCTION(execRotateTowardsMovementDirection); \
	DECLARE_FUNCTION(execProjectPointToNavMesh); \
	DECLARE_FUNCTION(execFindSmoothPathSynchronously); \
	DECLARE_FUNCTION(execGenerateSplinePoints); \
	DECLARE_FUNCTION(execMoveToLocationSmooth); \
	DECLARE_FUNCTION(execMoveToActorSmoothly); \
	DECLARE_FUNCTION(execMoveToLocationSmoothly); \
	DECLARE_FUNCTION(execCalcDesiredRotationArc);


#define FID_Baoli_Source_Baoli_AI_MyAIController_h_35_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAMyAIController(); \
	friend struct Z_Construct_UClass_AMyAIController_Statics; \
public: \
	DECLARE_CLASS(AMyAIController, AAIController, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Baoli"), NO_API) \
	DECLARE_SERIALIZER(AMyAIController)


#define FID_Baoli_Source_Baoli_AI_MyAIController_h_35_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AMyAIController(AMyAIController&&); \
	AMyAIController(const AMyAIController&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AMyAIController); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AMyAIController); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AMyAIController) \
	NO_API virtual ~AMyAIController();


#define FID_Baoli_Source_Baoli_AI_MyAIController_h_32_PROLOG
#define FID_Baoli_Source_Baoli_AI_MyAIController_h_35_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Baoli_Source_Baoli_AI_MyAIController_h_35_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_AI_MyAIController_h_35_INCLASS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_AI_MyAIController_h_35_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BAOLI_API UClass* StaticClass<class AMyAIController>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Baoli_Source_Baoli_AI_MyAIController_h


#define FOREACH_ENUM_EAISTATE(op) \
	op(EAIState::Idle) \
	op(EAIState::Patrolling) \
	op(EAIState::Chasing) \
	op(EAIState::Investigating) 

enum class EAIState : uint8;
template<> struct TIsUEnumClass<EAIState> { enum { Value = true }; };
template<> BAOLI_API UEnum* StaticEnum<EAIState>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
