// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Baoli/Interactable/Cover/Bed.h"
#include "Runtime/Engine/Classes/Animation/AnimNotifies/AnimNotify.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeBed() {}

// Begin Cross Module References
BAOLI_API UClass* Z_Construct_UClass_ABaoli_Character_NoRegister();
BAOLI_API UClass* Z_Construct_UClass_ABed();
BAOLI_API UClass* Z_Construct_UClass_ABed_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UAnimInstance_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAnimMontage_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UArrowComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FBranchingPointNotifyPayload();
UPackage* Z_Construct_UPackage__Script_Baoli();
// End Cross Module References

// Begin Class ABed Function ClearGarbage
struct Z_Construct_UFunction_ABed_ClearGarbage_Statics
{
	struct Bed_eventClearGarbage_Parms
	{
		FName NotifyName;
		FBranchingPointNotifyPayload BranchingPointNotifyPayload;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BranchingPointNotifyPayload_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_NotifyName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BranchingPointNotifyPayload;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ABed_ClearGarbage_Statics::NewProp_NotifyName = { "NotifyName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Bed_eventClearGarbage_Parms, NotifyName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ABed_ClearGarbage_Statics::NewProp_BranchingPointNotifyPayload = { "BranchingPointNotifyPayload", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Bed_eventClearGarbage_Parms, BranchingPointNotifyPayload), Z_Construct_UScriptStruct_FBranchingPointNotifyPayload, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BranchingPointNotifyPayload_MetaData), NewProp_BranchingPointNotifyPayload_MetaData) }; // 746097459
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABed_ClearGarbage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_ClearGarbage_Statics::NewProp_NotifyName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_ClearGarbage_Statics::NewProp_BranchingPointNotifyPayload,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_ClearGarbage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABed_ClearGarbage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABed, nullptr, "ClearGarbage", nullptr, nullptr, Z_Construct_UFunction_ABed_ClearGarbage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_ClearGarbage_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABed_ClearGarbage_Statics::Bed_eventClearGarbage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_ClearGarbage_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABed_ClearGarbage_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ABed_ClearGarbage_Statics::Bed_eventClearGarbage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABed_ClearGarbage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABed_ClearGarbage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABed::execClearGarbage)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_NotifyName);
	P_GET_STRUCT_REF(FBranchingPointNotifyPayload,Z_Param_Out_BranchingPointNotifyPayload);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearGarbage(Z_Param_NotifyName,Z_Param_Out_BranchingPointNotifyPayload);
	P_NATIVE_END;
}
// End Class ABed Function ClearGarbage

// Begin Class ABed Function GetArrow
struct Z_Construct_UFunction_ABed_GetArrow_Statics
{
	struct Bed_eventGetArrow_Parms
	{
		FVector CharacterLocation;
		UArrowComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Arrow Refrence" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Functions\n" },
#endif
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CharacterLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CharacterLocation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ABed_GetArrow_Statics::NewProp_CharacterLocation = { "CharacterLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Bed_eventGetArrow_Parms, CharacterLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CharacterLocation_MetaData), NewProp_CharacterLocation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ABed_GetArrow_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Bed_eventGetArrow_Parms, ReturnValue), Z_Construct_UClass_UArrowComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABed_GetArrow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_GetArrow_Statics::NewProp_CharacterLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_GetArrow_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_GetArrow_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABed_GetArrow_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABed, nullptr, "GetArrow", nullptr, nullptr, Z_Construct_UFunction_ABed_GetArrow_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_GetArrow_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABed_GetArrow_Statics::Bed_eventGetArrow_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_GetArrow_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABed_GetArrow_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ABed_GetArrow_Statics::Bed_eventGetArrow_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABed_GetArrow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABed_GetArrow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABed::execGetArrow)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_CharacterLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UArrowComponent**)Z_Param__Result=P_THIS->GetArrow(Z_Param_Out_CharacterLocation);
	P_NATIVE_END;
}
// End Class ABed Function GetArrow

// Begin Class ABed Function HideIn
struct Z_Construct_UFunction_ABed_HideIn_Statics
{
	struct Bed_eventHideIn_Parms
	{
		ABaoli_Character* Character;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Character;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ABed_HideIn_Statics::NewProp_Character = { "Character", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Bed_eventHideIn_Parms, Character), Z_Construct_UClass_ABaoli_Character_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABed_HideIn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_HideIn_Statics::NewProp_Character,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_HideIn_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABed_HideIn_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABed, nullptr, "HideIn", nullptr, nullptr, Z_Construct_UFunction_ABed_HideIn_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_HideIn_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABed_HideIn_Statics::Bed_eventHideIn_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_HideIn_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABed_HideIn_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ABed_HideIn_Statics::Bed_eventHideIn_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABed_HideIn()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABed_HideIn_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABed::execHideIn)
{
	P_GET_OBJECT(ABaoli_Character,Z_Param_Character);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideIn(Z_Param_Character);
	P_NATIVE_END;
}
// End Class ABed Function HideIn

// Begin Class ABed Function HideOut
struct Z_Construct_UFunction_ABed_HideOut_Statics
{
	struct Bed_eventHideOut_Parms
	{
		ABaoli_Character* Character;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Character;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ABed_HideOut_Statics::NewProp_Character = { "Character", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Bed_eventHideOut_Parms, Character), Z_Construct_UClass_ABaoli_Character_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABed_HideOut_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_HideOut_Statics::NewProp_Character,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_HideOut_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABed_HideOut_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABed, nullptr, "HideOut", nullptr, nullptr, Z_Construct_UFunction_ABed_HideOut_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_HideOut_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABed_HideOut_Statics::Bed_eventHideOut_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_HideOut_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABed_HideOut_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ABed_HideOut_Statics::Bed_eventHideOut_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABed_HideOut()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABed_HideOut_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABed::execHideOut)
{
	P_GET_OBJECT(ABaoli_Character,Z_Param_Character);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideOut(Z_Param_Character);
	P_NATIVE_END;
}
// End Class ABed Function HideOut

// Begin Class ABed Function InterpAim
struct Z_Construct_UFunction_ABed_InterpAim_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "InterpolateAim" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABed_InterpAim_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABed, nullptr, "InterpAim", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_InterpAim_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABed_InterpAim_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ABed_InterpAim()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABed_InterpAim_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABed::execInterpAim)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InterpAim();
	P_NATIVE_END;
}
// End Class ABed Function InterpAim

// Begin Class ABed Function OnBlendOut
struct Z_Construct_UFunction_ABed_OnBlendOut_Statics
{
	struct Bed_eventOnBlendOut_Parms
	{
		UAnimMontage* Montage;
		bool bInterupted;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "IN_animFunctions" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Montage;
	static void NewProp_bInterupted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInterupted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ABed_OnBlendOut_Statics::NewProp_Montage = { "Montage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Bed_eventOnBlendOut_Parms, Montage), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ABed_OnBlendOut_Statics::NewProp_bInterupted_SetBit(void* Obj)
{
	((Bed_eventOnBlendOut_Parms*)Obj)->bInterupted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABed_OnBlendOut_Statics::NewProp_bInterupted = { "bInterupted", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Bed_eventOnBlendOut_Parms), &Z_Construct_UFunction_ABed_OnBlendOut_Statics::NewProp_bInterupted_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABed_OnBlendOut_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_OnBlendOut_Statics::NewProp_Montage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_OnBlendOut_Statics::NewProp_bInterupted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_OnBlendOut_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABed_OnBlendOut_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABed, nullptr, "OnBlendOut", nullptr, nullptr, Z_Construct_UFunction_ABed_OnBlendOut_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_OnBlendOut_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABed_OnBlendOut_Statics::Bed_eventOnBlendOut_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_OnBlendOut_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABed_OnBlendOut_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ABed_OnBlendOut_Statics::Bed_eventOnBlendOut_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABed_OnBlendOut()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABed_OnBlendOut_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABed::execOnBlendOut)
{
	P_GET_OBJECT(UAnimMontage,Z_Param_Montage);
	P_GET_UBOOL(Z_Param_bInterupted);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnBlendOut(Z_Param_Montage,Z_Param_bInterupted);
	P_NATIVE_END;
}
// End Class ABed Function OnBlendOut

// Begin Class ABed Function OnCompleted
struct Z_Construct_UFunction_ABed_OnCompleted_Statics
{
	struct Bed_eventOnCompleted_Parms
	{
		UAnimMontage* Montage;
		bool bInterupted;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "IN_animFunctions" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Montage;
	static void NewProp_bInterupted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInterupted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ABed_OnCompleted_Statics::NewProp_Montage = { "Montage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Bed_eventOnCompleted_Parms, Montage), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ABed_OnCompleted_Statics::NewProp_bInterupted_SetBit(void* Obj)
{
	((Bed_eventOnCompleted_Parms*)Obj)->bInterupted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABed_OnCompleted_Statics::NewProp_bInterupted = { "bInterupted", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Bed_eventOnCompleted_Parms), &Z_Construct_UFunction_ABed_OnCompleted_Statics::NewProp_bInterupted_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABed_OnCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_OnCompleted_Statics::NewProp_Montage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_OnCompleted_Statics::NewProp_bInterupted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_OnCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABed_OnCompleted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABed, nullptr, "OnCompleted", nullptr, nullptr, Z_Construct_UFunction_ABed_OnCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_OnCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABed_OnCompleted_Statics::Bed_eventOnCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_OnCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABed_OnCompleted_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ABed_OnCompleted_Statics::Bed_eventOnCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABed_OnCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABed_OnCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABed::execOnCompleted)
{
	P_GET_OBJECT(UAnimMontage,Z_Param_Montage);
	P_GET_UBOOL(Z_Param_bInterupted);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnCompleted(Z_Param_Montage,Z_Param_bInterupted);
	P_NATIVE_END;
}
// End Class ABed Function OnCompleted

// Begin Class ABed Function OnNotify
struct Z_Construct_UFunction_ABed_OnNotify_Statics
{
	struct Bed_eventOnNotify_Parms
	{
		FName NotifyName;
		FBranchingPointNotifyPayload BranchingPointNotifyPayload;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BranchingPointNotifyPayload_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_NotifyName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BranchingPointNotifyPayload;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ABed_OnNotify_Statics::NewProp_NotifyName = { "NotifyName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Bed_eventOnNotify_Parms, NotifyName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ABed_OnNotify_Statics::NewProp_BranchingPointNotifyPayload = { "BranchingPointNotifyPayload", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Bed_eventOnNotify_Parms, BranchingPointNotifyPayload), Z_Construct_UScriptStruct_FBranchingPointNotifyPayload, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BranchingPointNotifyPayload_MetaData), NewProp_BranchingPointNotifyPayload_MetaData) }; // 746097459
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABed_OnNotify_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_OnNotify_Statics::NewProp_NotifyName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABed_OnNotify_Statics::NewProp_BranchingPointNotifyPayload,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_OnNotify_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABed_OnNotify_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABed, nullptr, "OnNotify", nullptr, nullptr, Z_Construct_UFunction_ABed_OnNotify_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_OnNotify_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABed_OnNotify_Statics::Bed_eventOnNotify_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABed_OnNotify_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABed_OnNotify_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ABed_OnNotify_Statics::Bed_eventOnNotify_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABed_OnNotify()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABed_OnNotify_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABed::execOnNotify)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_NotifyName);
	P_GET_STRUCT_REF(FBranchingPointNotifyPayload,Z_Param_Out_BranchingPointNotifyPayload);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnNotify(Z_Param_NotifyName,Z_Param_Out_BranchingPointNotifyPayload);
	P_NATIVE_END;
}
// End Class ABed Function OnNotify

// Begin Class ABed
void ABed::StaticRegisterNativesABed()
{
	UClass* Class = ABed::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearGarbage", &ABed::execClearGarbage },
		{ "GetArrow", &ABed::execGetArrow },
		{ "HideIn", &ABed::execHideIn },
		{ "HideOut", &ABed::execHideOut },
		{ "InterpAim", &ABed::execInterpAim },
		{ "OnBlendOut", &ABed::execOnBlendOut },
		{ "OnCompleted", &ABed::execOnCompleted },
		{ "OnNotify", &ABed::execOnNotify },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(ABed);
UClass* Z_Construct_UClass_ABed_NoRegister()
{
	return ABed::StaticClass();
}
struct Z_Construct_UClass_ABed_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "Interactable/Cover/Bed.h" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mesh_Bed_MetaData[] = {
		{ "Category", "Construction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Construction\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Construction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ANorth_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ASouth_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AEast_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AWest_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AReference_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimInstance_MetaData[] = {
		{ "Category", "AnimInstance" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bwasCrouched_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Baoli_Character_MetaData[] = {
		{ "Category", "Refrences" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanGetOut_MetaData[] = {
		{ "Category", "References" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IN_Crouched_MetaData[] = {
		{ "Category", "Montages" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IN_UNCrouched_MetaData[] = {
		{ "Category", "Montages" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OUT_Crouched_MetaData[] = {
		{ "Category", "Montages" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OUT_UNCrouched_MetaData[] = {
		{ "Category", "Montages" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Arrows_MetaData[] = {
		{ "Category", "Arrow Array" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Variables\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Bed.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Variables" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Mesh_Bed;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ANorth;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ASouth;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AEast;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AWest;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AReference;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AnimInstance;
	static void NewProp_bwasCrouched_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bwasCrouched;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Baoli_Character;
	static void NewProp_bCanGetOut_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanGetOut;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IN_Crouched;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IN_UNCrouched;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OUT_Crouched;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OUT_UNCrouched;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Arrows_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Arrows;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ABed_ClearGarbage, "ClearGarbage" }, // 2045667174
		{ &Z_Construct_UFunction_ABed_GetArrow, "GetArrow" }, // 3815566572
		{ &Z_Construct_UFunction_ABed_HideIn, "HideIn" }, // 1363224919
		{ &Z_Construct_UFunction_ABed_HideOut, "HideOut" }, // 1335570839
		{ &Z_Construct_UFunction_ABed_InterpAim, "InterpAim" }, // 386990950
		{ &Z_Construct_UFunction_ABed_OnBlendOut, "OnBlendOut" }, // 3638654911
		{ &Z_Construct_UFunction_ABed_OnCompleted, "OnCompleted" }, // 198093758
		{ &Z_Construct_UFunction_ABed_OnNotify, "OnNotify" }, // 316664919
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ABed>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_Mesh_Bed = { "Mesh_Bed", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, Mesh_Bed), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mesh_Bed_MetaData), NewProp_Mesh_Bed_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_ANorth = { "ANorth", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, ANorth), Z_Construct_UClass_UArrowComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ANorth_MetaData), NewProp_ANorth_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_ASouth = { "ASouth", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, ASouth), Z_Construct_UClass_UArrowComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ASouth_MetaData), NewProp_ASouth_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_AEast = { "AEast", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, AEast), Z_Construct_UClass_UArrowComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AEast_MetaData), NewProp_AEast_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_AWest = { "AWest", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, AWest), Z_Construct_UClass_UArrowComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AWest_MetaData), NewProp_AWest_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_AReference = { "AReference", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, AReference), Z_Construct_UClass_UArrowComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AReference_MetaData), NewProp_AReference_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_AnimInstance = { "AnimInstance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, AnimInstance), Z_Construct_UClass_UAnimInstance_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimInstance_MetaData), NewProp_AnimInstance_MetaData) };
void Z_Construct_UClass_ABed_Statics::NewProp_bwasCrouched_SetBit(void* Obj)
{
	((ABed*)Obj)->bwasCrouched = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_bwasCrouched = { "bwasCrouched", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABed), &Z_Construct_UClass_ABed_Statics::NewProp_bwasCrouched_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bwasCrouched_MetaData), NewProp_bwasCrouched_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_Baoli_Character = { "Baoli_Character", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, Baoli_Character), Z_Construct_UClass_ABaoli_Character_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Baoli_Character_MetaData), NewProp_Baoli_Character_MetaData) };
void Z_Construct_UClass_ABed_Statics::NewProp_bCanGetOut_SetBit(void* Obj)
{
	((ABed*)Obj)->bCanGetOut = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_bCanGetOut = { "bCanGetOut", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABed), &Z_Construct_UClass_ABed_Statics::NewProp_bCanGetOut_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanGetOut_MetaData), NewProp_bCanGetOut_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_IN_Crouched = { "IN_Crouched", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, IN_Crouched), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IN_Crouched_MetaData), NewProp_IN_Crouched_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_IN_UNCrouched = { "IN_UNCrouched", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, IN_UNCrouched), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IN_UNCrouched_MetaData), NewProp_IN_UNCrouched_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_OUT_Crouched = { "OUT_Crouched", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, OUT_Crouched), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OUT_Crouched_MetaData), NewProp_OUT_Crouched_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_OUT_UNCrouched = { "OUT_UNCrouched", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, OUT_UNCrouched), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OUT_UNCrouched_MetaData), NewProp_OUT_UNCrouched_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_Arrows_Inner = { "Arrows", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UArrowComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ABed_Statics::NewProp_Arrows = { "Arrows", nullptr, (EPropertyFlags)0x001000800000000c, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABed, Arrows), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Arrows_MetaData), NewProp_Arrows_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ABed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_Mesh_Bed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_ANorth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_ASouth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_AEast,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_AWest,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_AReference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_AnimInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_bwasCrouched,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_Baoli_Character,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_bCanGetOut,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_IN_Crouched,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_IN_UNCrouched,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_OUT_Crouched,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_OUT_UNCrouched,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_Arrows_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABed_Statics::NewProp_Arrows,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ABed_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ABed_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Baoli,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ABed_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ABed_Statics::ClassParams = {
	&ABed::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ABed_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ABed_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ABed_Statics::Class_MetaDataParams), Z_Construct_UClass_ABed_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ABed()
{
	if (!Z_Registration_Info_UClass_ABed.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ABed.OuterSingleton, Z_Construct_UClass_ABed_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ABed.OuterSingleton;
}
template<> BAOLI_API UClass* StaticClass<ABed>()
{
	return ABed::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ABed);
ABed::~ABed() {}
// End Class ABed

// Begin Registration
struct Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ABed, ABed::StaticClass, TEXT("ABed"), &Z_Registration_Info_UClass_ABed, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ABed), 2201510918U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_254476976(TEXT("/Script/Baoli"),
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
