// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "OptimizedWebBrowser/Public/OptimizedWebBrowser.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeOptimizedWebBrowser() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FColor();
OPTIMIZEDWEBBROWSER_API UClass* Z_Construct_UClass_UOptimizedWebBrowser();
OPTIMIZEDWEBBROWSER_API UClass* Z_Construct_UClass_UOptimizedWebBrowser_NoRegister();
OPTIMIZEDWEBBROWSER_API UFunction* Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature();
OPTIMIZEDWEBBROWSER_API UFunction* Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature();
OPTIMIZEDWEBBROWSER_API UFunction* Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature();
UMG_API UClass* Z_Construct_UClass_UWidget();
UPackage* Z_Construct_UPackage__Script_OptimizedWebBrowser();
// End Cross Module References

// Begin Delegate FOnUrlChanged
struct Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics
{
	struct OptimizedWebBrowser_eventOnUrlChanged_Parms
	{
		FText Text;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Text_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_Text;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::NewProp_Text = { "Text", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventOnUrlChanged_Parms, Text), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Text_MetaData), NewProp_Text_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::NewProp_Text,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "OnUrlChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::OptimizedWebBrowser_eventOnUrlChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::OptimizedWebBrowser_eventOnUrlChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UOptimizedWebBrowser::FOnUrlChanged_DelegateWrapper(const FMulticastScriptDelegate& OnUrlChanged, FText const& Text)
{
	struct OptimizedWebBrowser_eventOnUrlChanged_Parms
	{
		FText Text;
	};
	OptimizedWebBrowser_eventOnUrlChanged_Parms Parms;
	Parms.Text=Text;
	OnUrlChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnUrlChanged

// Begin Delegate FOnBeforePopup
struct Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics
{
	struct OptimizedWebBrowser_eventOnBeforePopup_Parms
	{
		FString URL;
		FString Frame;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_URL;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Frame;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::NewProp_URL = { "URL", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventOnBeforePopup_Parms, URL), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::NewProp_Frame = { "Frame", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventOnBeforePopup_Parms, Frame), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::NewProp_URL,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::NewProp_Frame,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "OnBeforePopup__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::OptimizedWebBrowser_eventOnBeforePopup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::OptimizedWebBrowser_eventOnBeforePopup_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UOptimizedWebBrowser::FOnBeforePopup_DelegateWrapper(const FMulticastScriptDelegate& OnBeforePopup, const FString& URL, const FString& Frame)
{
	struct OptimizedWebBrowser_eventOnBeforePopup_Parms
	{
		FString URL;
		FString Frame;
	};
	OptimizedWebBrowser_eventOnBeforePopup_Parms Parms;
	Parms.URL=URL;
	Parms.Frame=Frame;
	OnBeforePopup.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnBeforePopup

// Begin Delegate FOnConsoleMessage
struct Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics
{
	struct OptimizedWebBrowser_eventOnConsoleMessage_Parms
	{
		FString Message;
		FString Source;
		int32 Line;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Source_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Source;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Line;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventOnConsoleMessage_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::NewProp_Source = { "Source", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventOnConsoleMessage_Parms, Source), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Source_MetaData), NewProp_Source_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::NewProp_Line = { "Line", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventOnConsoleMessage_Parms, Line), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::NewProp_Message,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::NewProp_Source,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::NewProp_Line,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "OnConsoleMessage__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::OptimizedWebBrowser_eventOnConsoleMessage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::OptimizedWebBrowser_eventOnConsoleMessage_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void UOptimizedWebBrowser::FOnConsoleMessage_DelegateWrapper(const FMulticastScriptDelegate& OnConsoleMessage, const FString& Message, const FString& Source, int32 Line)
{
	struct OptimizedWebBrowser_eventOnConsoleMessage_Parms
	{
		FString Message;
		FString Source;
		int32 Line;
	};
	OptimizedWebBrowser_eventOnConsoleMessage_Parms Parms;
	Parms.Message=Message;
	Parms.Source=Source;
	Parms.Line=Line;
	OnConsoleMessage.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnConsoleMessage

// Begin Class UOptimizedWebBrowser Function ExecuteJavascript
struct Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics
{
	struct OptimizedWebBrowser_eventExecuteJavascript_Parms
	{
		FString ScriptText;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Web Browser" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n    * Executes a JavaScript string in the context of the web page\n    *\n    * @param ScriptText JavaScript string to execute\n    */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Executes a JavaScript string in the context of the web page\n\n@param ScriptText JavaScript string to execute" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScriptText_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScriptText;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::NewProp_ScriptText = { "ScriptText", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventExecuteJavascript_Parms, ScriptText), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScriptText_MetaData), NewProp_ScriptText_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::NewProp_ScriptText,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "ExecuteJavascript", nullptr, nullptr, Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::OptimizedWebBrowser_eventExecuteJavascript_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::OptimizedWebBrowser_eventExecuteJavascript_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOptimizedWebBrowser::execExecuteJavascript)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScriptText);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExecuteJavascript(Z_Param_ScriptText);
	P_NATIVE_END;
}
// End Class UOptimizedWebBrowser Function ExecuteJavascript

// Begin Class UOptimizedWebBrowser Function GetTitleText
struct Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics
{
	struct OptimizedWebBrowser_eventGetTitleText_Parms
	{
		FText ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Web Browser" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get the current title of the web page\n     */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get the current title of the web page" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventGetTitleText_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "GetTitleText", nullptr, nullptr, Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::OptimizedWebBrowser_eventGetTitleText_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::OptimizedWebBrowser_eventGetTitleText_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOptimizedWebBrowser::execGetTitleText)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FText*)Z_Param__Result=P_THIS->GetTitleText();
	P_NATIVE_END;
}
// End Class UOptimizedWebBrowser Function GetTitleText

// Begin Class UOptimizedWebBrowser Function GetUrl
struct Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics
{
	struct OptimizedWebBrowser_eventGetUrl_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Web Browser" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n    * Gets the currently loaded URL.\n    *\n    * @return The URL, or empty string if no document is loaded.\n    */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gets the currently loaded URL.\n\n@return The URL, or empty string if no document is loaded." },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventGetUrl_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "GetUrl", nullptr, nullptr, Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::OptimizedWebBrowser_eventGetUrl_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::OptimizedWebBrowser_eventGetUrl_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOptimizedWebBrowser::execGetUrl)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetUrl();
	P_NATIVE_END;
}
// End Class UOptimizedWebBrowser Function GetUrl

// Begin Class UOptimizedWebBrowser Function LoadString
struct Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics
{
	struct OptimizedWebBrowser_eventLoadString_Parms
	{
		FString Contents;
		FString DummyURL;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Web Browser" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Load a string as data to create a web page\n     *\n     * @param Contents String to load\n     * @param DummyURL Dummy URL for the page\n     */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Load a string as data to create a web page\n\n@param Contents String to load\n@param DummyURL Dummy URL for the page" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Contents;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DummyURL;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::NewProp_Contents = { "Contents", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventLoadString_Parms, Contents), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::NewProp_DummyURL = { "DummyURL", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventLoadString_Parms, DummyURL), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::NewProp_Contents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::NewProp_DummyURL,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "LoadString", nullptr, nullptr, Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::OptimizedWebBrowser_eventLoadString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::OptimizedWebBrowser_eventLoadString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOptimizedWebBrowser_LoadString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOptimizedWebBrowser_LoadString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOptimizedWebBrowser::execLoadString)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Contents);
	P_GET_PROPERTY(FStrProperty,Z_Param_DummyURL);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadString(Z_Param_Contents,Z_Param_DummyURL);
	P_NATIVE_END;
}
// End Class UOptimizedWebBrowser Function LoadString

// Begin Class UOptimizedWebBrowser Function LoadURL
struct Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics
{
	struct OptimizedWebBrowser_eventLoadURL_Parms
	{
		FString NewURL;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Web Browser" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Load the specified URL\n     *\n     * @param NewURL New URL to load\n     */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Load the specified URL\n\n@param NewURL New URL to load" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NewURL;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::NewProp_NewURL = { "NewURL", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventLoadURL_Parms, NewURL), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::NewProp_NewURL,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "LoadURL", nullptr, nullptr, Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::OptimizedWebBrowser_eventLoadURL_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::OptimizedWebBrowser_eventLoadURL_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOptimizedWebBrowser::execLoadURL)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NewURL);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadURL(Z_Param_NewURL);
	P_NATIVE_END;
}
// End Class UOptimizedWebBrowser Function LoadURL

// Begin Class UOptimizedWebBrowser Function LogToConsole
struct Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics
{
	struct OptimizedWebBrowser_eventLogToConsole_Parms
	{
		FString Message;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Web Browser" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n    * Logs a message to the browser's JavaScript console\n    *\n    * @param Message The message to log to the console\n    */" },
#endif
		{ "DisplayName", "Log To Console" },
		{ "Keywords", "javascript console log debug" },
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Logs a message to the browser's JavaScript console\n\n@param Message The message to log to the console" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventLogToConsole_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::NewProp_Message,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "LogToConsole", nullptr, nullptr, Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::OptimizedWebBrowser_eventLogToConsole_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::OptimizedWebBrowser_eventLogToConsole_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOptimizedWebBrowser::execLogToConsole)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Message);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogToConsole(Z_Param_Message);
	P_NATIVE_END;
}
// End Class UOptimizedWebBrowser Function LogToConsole

// Begin Class UOptimizedWebBrowser Function SetBackgroundColor
struct Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics
{
	struct OptimizedWebBrowser_eventSetBackgroundColor_Parms
	{
		FColor Color;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Web Browser|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set the background color of the browser. Using opaque colors can improve performance.\n     *\n     * @param Color The background color to set\n     */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set the background color of the browser. Using opaque colors can improve performance.\n\n@param Color The background color to set" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Color;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::NewProp_Color = { "Color", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventSetBackgroundColor_Parms, Color), Z_Construct_UScriptStruct_FColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::NewProp_Color,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "SetBackgroundColor", nullptr, nullptr, Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::OptimizedWebBrowser_eventSetBackgroundColor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::OptimizedWebBrowser_eventSetBackgroundColor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOptimizedWebBrowser::execSetBackgroundColor)
{
	P_GET_STRUCT(FColor,Z_Param_Color);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBackgroundColor(Z_Param_Color);
	P_NATIVE_END;
}
// End Class UOptimizedWebBrowser Function SetBackgroundColor

// Begin Class UOptimizedWebBrowser Function SetBackgroundTabThrottlingEnabled
struct Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics
{
	struct OptimizedWebBrowser_eventSetBackgroundTabThrottlingEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Web Browser|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enable or disable throttling of background tabs. When enabled, tabs that are not\n     * visible will use less CPU resources.\n     *\n     * @param bEnabled Whether background tab throttling should be enabled\n     */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable or disable throttling of background tabs. When enabled, tabs that are not\nvisible will use less CPU resources.\n\n@param bEnabled Whether background tab throttling should be enabled" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((OptimizedWebBrowser_eventSetBackgroundTabThrottlingEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(OptimizedWebBrowser_eventSetBackgroundTabThrottlingEnabled_Parms), &Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "SetBackgroundTabThrottlingEnabled", nullptr, nullptr, Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::OptimizedWebBrowser_eventSetBackgroundTabThrottlingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::OptimizedWebBrowser_eventSetBackgroundTabThrottlingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOptimizedWebBrowser::execSetBackgroundTabThrottlingEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBackgroundTabThrottlingEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// End Class UOptimizedWebBrowser Function SetBackgroundTabThrottlingEnabled

// Begin Class UOptimizedWebBrowser Function SetBrowserFrameRate
struct Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics
{
	struct OptimizedWebBrowser_eventSetBrowserFrameRate_Parms
	{
		int32 FrameRate;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Web Browser|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Set the browser frame rate. Higher values improve smoothness but increase CPU usage.\n     * Default is 24 fps. Recommended values: 30-60 for smoother experience, 15-24 for lower CPU usage.\n     *\n     * @param FrameRate The new frame rate to set\n     */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Set the browser frame rate. Higher values improve smoothness but increase CPU usage.\nDefault is 24 fps. Recommended values: 30-60 for smoother experience, 15-24 for lower CPU usage.\n\n@param FrameRate The new frame rate to set" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_FrameRate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::NewProp_FrameRate = { "FrameRate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(OptimizedWebBrowser_eventSetBrowserFrameRate_Parms, FrameRate), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::NewProp_FrameRate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "SetBrowserFrameRate", nullptr, nullptr, Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::OptimizedWebBrowser_eventSetBrowserFrameRate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::OptimizedWebBrowser_eventSetBrowserFrameRate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOptimizedWebBrowser::execSetBrowserFrameRate)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_FrameRate);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBrowserFrameRate(Z_Param_FrameRate);
	P_NATIVE_END;
}
// End Class UOptimizedWebBrowser Function SetBrowserFrameRate

// Begin Class UOptimizedWebBrowser Function SetHardwareAccelerationEnabled
struct Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics
{
	struct OptimizedWebBrowser_eventSetHardwareAccelerationEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Web Browser|Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Enable or disable hardware acceleration. Hardware acceleration can improve performance\n     * but may cause issues on some systems.\n     *\n     * @param bEnabled Whether hardware acceleration should be enabled\n     */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enable or disable hardware acceleration. Hardware acceleration can improve performance\nbut may cause issues on some systems.\n\n@param bEnabled Whether hardware acceleration should be enabled" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((OptimizedWebBrowser_eventSetHardwareAccelerationEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(OptimizedWebBrowser_eventSetHardwareAccelerationEnabled_Parms), &Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UOptimizedWebBrowser, nullptr, "SetHardwareAccelerationEnabled", nullptr, nullptr, Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::OptimizedWebBrowser_eventSetHardwareAccelerationEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::OptimizedWebBrowser_eventSetHardwareAccelerationEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UOptimizedWebBrowser::execSetHardwareAccelerationEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetHardwareAccelerationEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// End Class UOptimizedWebBrowser Function SetHardwareAccelerationEnabled

// Begin Class UOptimizedWebBrowser
void UOptimizedWebBrowser::StaticRegisterNativesUOptimizedWebBrowser()
{
	UClass* Class = UOptimizedWebBrowser::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ExecuteJavascript", &UOptimizedWebBrowser::execExecuteJavascript },
		{ "GetTitleText", &UOptimizedWebBrowser::execGetTitleText },
		{ "GetUrl", &UOptimizedWebBrowser::execGetUrl },
		{ "LoadString", &UOptimizedWebBrowser::execLoadString },
		{ "LoadURL", &UOptimizedWebBrowser::execLoadURL },
		{ "LogToConsole", &UOptimizedWebBrowser::execLogToConsole },
		{ "SetBackgroundColor", &UOptimizedWebBrowser::execSetBackgroundColor },
		{ "SetBackgroundTabThrottlingEnabled", &UOptimizedWebBrowser::execSetBackgroundTabThrottlingEnabled },
		{ "SetBrowserFrameRate", &UOptimizedWebBrowser::execSetBrowserFrameRate },
		{ "SetHardwareAccelerationEnabled", &UOptimizedWebBrowser::execSetHardwareAccelerationEnabled },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UOptimizedWebBrowser);
UClass* Z_Construct_UClass_UOptimizedWebBrowser_NoRegister()
{
	return UOptimizedWebBrowser::StaticClass();
}
struct Z_Construct_UClass_UOptimizedWebBrowser_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * An optimized version of the WebBrowser widget that improves performance\n * while maintaining all functionality.\n */" },
#endif
		{ "IncludePath", "OptimizedWebBrowser.h" },
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "An optimized version of the WebBrowser widget that improves performance\nwhile maintaining all functionality." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnUrlChanged_MetaData[] = {
		{ "Category", "Web Browser|Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when the Url changes. */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when the Url changes." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBeforePopup_MetaData[] = {
		{ "Category", "Web Browser|Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when a popup is about to spawn. */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when a popup is about to spawn." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnConsoleMessage_MetaData[] = {
		{ "Category", "Web Browser|Event" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Called when the browser has console spew to print */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when the browser has console spew to print" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InitialURL_MetaData[] = {
		{ "Category", "Appearance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** URL that the browser will initially navigate to. The URL should include the protocol, eg http:// */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "URL that the browser will initially navigate to. The URL should include the protocol, eg http://" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSupportsTransparency_MetaData[] = {
		{ "Category", "Appearance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Should the browser window support transparency. */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Should the browser window support transparency." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BrowserFrameRate_MetaData[] = {
		{ "Category", "Performance" },
		{ "ClampMax", "120" },
		{ "ClampMin", "15" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** The frame rate at which the browser will render. Higher values are smoother but use more CPU. */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "The frame rate at which the browser will render. Higher values are smoother but use more CPU." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHardwareAccelerationEnabled_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether hardware acceleration is enabled. */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether hardware acceleration is enabled." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BackgroundColor_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Background color of the browser. Using opaque colors can improve performance. */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Background color of the browser. Using opaque colors can improve performance." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bBackgroundTabThrottlingEnabled_MetaData[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether to throttle background tabs to save CPU resources. */" },
#endif
		{ "ModuleRelativePath", "Public/OptimizedWebBrowser.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether to throttle background tabs to save CPU resources." },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnUrlChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBeforePopup;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnConsoleMessage;
	static const UECodeGen_Private::FStrPropertyParams NewProp_InitialURL;
	static void NewProp_bSupportsTransparency_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSupportsTransparency;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BrowserFrameRate;
	static void NewProp_bHardwareAccelerationEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHardwareAccelerationEnabled;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BackgroundColor;
	static void NewProp_bBackgroundTabThrottlingEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bBackgroundTabThrottlingEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UOptimizedWebBrowser_ExecuteJavascript, "ExecuteJavascript" }, // 1971825772
		{ &Z_Construct_UFunction_UOptimizedWebBrowser_GetTitleText, "GetTitleText" }, // 1705753043
		{ &Z_Construct_UFunction_UOptimizedWebBrowser_GetUrl, "GetUrl" }, // 1414840859
		{ &Z_Construct_UFunction_UOptimizedWebBrowser_LoadString, "LoadString" }, // 3375369582
		{ &Z_Construct_UFunction_UOptimizedWebBrowser_LoadURL, "LoadURL" }, // 2251292500
		{ &Z_Construct_UFunction_UOptimizedWebBrowser_LogToConsole, "LogToConsole" }, // 31455052
		{ &Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature, "OnBeforePopup__DelegateSignature" }, // 4149149030
		{ &Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature, "OnConsoleMessage__DelegateSignature" }, // 753841277
		{ &Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature, "OnUrlChanged__DelegateSignature" }, // 4270250922
		{ &Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundColor, "SetBackgroundColor" }, // 3562662395
		{ &Z_Construct_UFunction_UOptimizedWebBrowser_SetBackgroundTabThrottlingEnabled, "SetBackgroundTabThrottlingEnabled" }, // 2466084802
		{ &Z_Construct_UFunction_UOptimizedWebBrowser_SetBrowserFrameRate, "SetBrowserFrameRate" }, // 1018314716
		{ &Z_Construct_UFunction_UOptimizedWebBrowser_SetHardwareAccelerationEnabled, "SetHardwareAccelerationEnabled" }, // 187141155
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UOptimizedWebBrowser>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_OnUrlChanged = { "OnUrlChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOptimizedWebBrowser, OnUrlChanged), Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnUrlChanged_MetaData), NewProp_OnUrlChanged_MetaData) }; // 4270250922
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_OnBeforePopup = { "OnBeforePopup", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOptimizedWebBrowser, OnBeforePopup), Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBeforePopup_MetaData), NewProp_OnBeforePopup_MetaData) }; // 4149149030
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_OnConsoleMessage = { "OnConsoleMessage", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOptimizedWebBrowser, OnConsoleMessage), Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnConsoleMessage_MetaData), NewProp_OnConsoleMessage_MetaData) }; // 753841277
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_InitialURL = { "InitialURL", nullptr, (EPropertyFlags)0x0020080000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOptimizedWebBrowser, InitialURL), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InitialURL_MetaData), NewProp_InitialURL_MetaData) };
void Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bSupportsTransparency_SetBit(void* Obj)
{
	((UOptimizedWebBrowser*)Obj)->bSupportsTransparency = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bSupportsTransparency = { "bSupportsTransparency", nullptr, (EPropertyFlags)0x0020080000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UOptimizedWebBrowser), &Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bSupportsTransparency_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSupportsTransparency_MetaData), NewProp_bSupportsTransparency_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_BrowserFrameRate = { "BrowserFrameRate", nullptr, (EPropertyFlags)0x0020080000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOptimizedWebBrowser, BrowserFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BrowserFrameRate_MetaData), NewProp_BrowserFrameRate_MetaData) };
void Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bHardwareAccelerationEnabled_SetBit(void* Obj)
{
	((UOptimizedWebBrowser*)Obj)->bHardwareAccelerationEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bHardwareAccelerationEnabled = { "bHardwareAccelerationEnabled", nullptr, (EPropertyFlags)0x0020080000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UOptimizedWebBrowser), &Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bHardwareAccelerationEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHardwareAccelerationEnabled_MetaData), NewProp_bHardwareAccelerationEnabled_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_BackgroundColor = { "BackgroundColor", nullptr, (EPropertyFlags)0x0020080000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UOptimizedWebBrowser, BackgroundColor), Z_Construct_UScriptStruct_FColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BackgroundColor_MetaData), NewProp_BackgroundColor_MetaData) };
void Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bBackgroundTabThrottlingEnabled_SetBit(void* Obj)
{
	((UOptimizedWebBrowser*)Obj)->bBackgroundTabThrottlingEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bBackgroundTabThrottlingEnabled = { "bBackgroundTabThrottlingEnabled", nullptr, (EPropertyFlags)0x0020080000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UOptimizedWebBrowser), &Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bBackgroundTabThrottlingEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bBackgroundTabThrottlingEnabled_MetaData), NewProp_bBackgroundTabThrottlingEnabled_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UOptimizedWebBrowser_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_OnUrlChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_OnBeforePopup,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_OnConsoleMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_InitialURL,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bSupportsTransparency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_BrowserFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bHardwareAccelerationEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_BackgroundColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UOptimizedWebBrowser_Statics::NewProp_bBackgroundTabThrottlingEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UOptimizedWebBrowser_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UOptimizedWebBrowser_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWidget,
	(UObject* (*)())Z_Construct_UPackage__Script_OptimizedWebBrowser,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UOptimizedWebBrowser_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UOptimizedWebBrowser_Statics::ClassParams = {
	&UOptimizedWebBrowser::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UOptimizedWebBrowser_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UOptimizedWebBrowser_Statics::PropPointers),
	0,
	0x00B000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UOptimizedWebBrowser_Statics::Class_MetaDataParams), Z_Construct_UClass_UOptimizedWebBrowser_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UOptimizedWebBrowser()
{
	if (!Z_Registration_Info_UClass_UOptimizedWebBrowser.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UOptimizedWebBrowser.OuterSingleton, Z_Construct_UClass_UOptimizedWebBrowser_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UOptimizedWebBrowser.OuterSingleton;
}
template<> OPTIMIZEDWEBBROWSER_API UClass* StaticClass<UOptimizedWebBrowser>()
{
	return UOptimizedWebBrowser::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UOptimizedWebBrowser);
UOptimizedWebBrowser::~UOptimizedWebBrowser() {}
// End Class UOptimizedWebBrowser

// Begin Registration
struct Z_CompiledInDeferFile_FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UOptimizedWebBrowser, UOptimizedWebBrowser::StaticClass, TEXT("UOptimizedWebBrowser"), &Z_Registration_Info_UClass_UOptimizedWebBrowser, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UOptimizedWebBrowser), 3032458444U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_1699830921(TEXT("/Script/OptimizedWebBrowser"),
	Z_CompiledInDeferFile_FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_OptimizedWebBrowser_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
