/**
 * WebBrowserOptimizations.js
 *
 * This file contains JavaScript optimizations for the WebBrowserWidget in Unreal Engine.
 * It improves performance by optimizing rendering, reducing unnecessary animations,
 * and implementing various performance best practices.
 *
 * Copyright (C) 2025 Anshul Patalbansi
 * Website: https://anshulpatalbansi.com
 * Email: <EMAIL>
 */

(function() {
    // Configuration (can be overridden by UE4)
    window.UE_WebBrowserConfig = window.UE_WebBrowserConfig || {
        enableHardwareAcceleration: true,
        throttleBackgroundTabs: true,
        limitAnimationFrameRate: true,
        optimizeScrolling: true,
        lazyLoadImages: true,
        disableAnimationsWhenHidden: true,
        maxFPS: 30
    };

    // Force hardware acceleration if enabled
    if (window.UE_WebBrowserConfig.enableHardwareAcceleration) {
        document.body.style.transform = 'translateZ(0)';
        document.body.style.backfaceVisibility = 'hidden';
    }

    // Optimize scrolling
    if (window.UE_WebBrowserConfig.optimizeScrolling) {
        // Use passive event listeners for scroll events
        const supportsPassive = (function() {
            let passive = false;
            try {
                const opts = Object.defineProperty({}, 'passive', {
                    get: function() { passive = true; }
                });
                window.addEventListener('test', null, opts);
                window.removeEventListener('test', null, opts);
            } catch (e) {}
            return passive;
        })();

        // Add passive scroll listeners to improve scrolling performance
        if (supportsPassive) {
            const preventDefault = function(e) { e.preventDefault(); };
            document.addEventListener('touchstart', preventDefault, { passive: false });
            document.addEventListener('touchmove', preventDefault, { passive: false });
        }
    }

    // Throttle animations in background tabs
    if (window.UE_WebBrowserConfig.throttleBackgroundTabs) {
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // Reduce animation frame rate when tab is not visible
                if (window._originalRAF && !window._throttledRAF) {
                    window._throttledRAF = true;
                    window.requestAnimationFrame = function(callback) {
                        return setTimeout(callback, 100); // Throttle to ~10fps when hidden
                    };
                }
            } else {
                // Restore original requestAnimationFrame when tab becomes visible
                if (window._originalRAF && window._throttledRAF) {
                    window._throttledRAF = false;
                    window.requestAnimationFrame = window._originalRAF;
                }
            }
        });
    }

    // Limit animation frame rate for better performance
    if (window.UE_WebBrowserConfig.limitAnimationFrameRate && window.requestAnimationFrame) {
        // Store original requestAnimationFrame
        window._originalRAF = window.requestAnimationFrame;

        // Calculate frame interval based on maxFPS
        const frameInterval = 1000 / window.UE_WebBrowserConfig.maxFPS;
        let lastFrameTime = 0;

        // Override requestAnimationFrame to limit frame rate
        window.requestAnimationFrame = function(callback) {
            return window._originalRAF(function(timestamp) {
                const elapsed = timestamp - lastFrameTime;
                if (elapsed >= frameInterval) {
                    lastFrameTime = timestamp - (elapsed % frameInterval);
                    callback(timestamp);
                } else {
                    window.requestAnimationFrame(callback);
                }
            });
        };
    }

    // Disable animations when page is not visible
    if (window.UE_WebBrowserConfig.disableAnimationsWhenHidden) {
        document.addEventListener('visibilitychange', function() {
            const style = document.createElement('style');
            if (document.hidden) {
                style.textContent = '* { animation-play-state: paused !important; transition: none !important; }';
            } else {
                style.textContent = '';
            }
            document.head.appendChild(style);

            // Remove the style element after it's been applied
            setTimeout(function() {
                if (style.parentNode) {
                    style.parentNode.removeChild(style);
                }
            }, 100);
        });
    }

    // Implement lazy loading for images
    if (window.UE_WebBrowserConfig.lazyLoadImages) {
        // Use IntersectionObserver if available
        if ('IntersectionObserver' in window) {
            const lazyImageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const lazyImage = entry.target;
                        if (lazyImage.dataset.src) {
                            lazyImage.src = lazyImage.dataset.src;
                            lazyImage.removeAttribute('data-src');
                        }
                        observer.unobserve(lazyImage);
                    }
                });
            });

            // Find all images with data-src attribute and observe them
            document.addEventListener('DOMContentLoaded', function() {
                const lazyImages = document.querySelectorAll('img[data-src]');
                lazyImages.forEach(function(lazyImage) {
                    lazyImageObserver.observe(lazyImage);
                });
            });
        }
    }

    // Optimize CSS animations
    const optimizeCSS = function() {
        // Force GPU acceleration for animations
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            .ue-gpu-accelerated {
                transform: translateZ(0);
                will-change: transform;
                backface-visibility: hidden;
            }
            @keyframes ue-optimized-fade {
                from { opacity: 0; }
                to { opacity: 1; }
            }
        `;
        document.head.appendChild(styleSheet);

        // Apply GPU acceleration class to animated elements
        const animatedElements = document.querySelectorAll('[class*="animate"], [class*="transition"]');
        animatedElements.forEach(function(el) {
            el.classList.add('ue-gpu-accelerated');
        });
    };

    // Run optimization when DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', optimizeCSS);
    } else {
        optimizeCSS();
    }

    // Expose API to UE4
    window.UE_WebBrowserOptimizations = {
        setConfig: function(config) {
            window.UE_WebBrowserConfig = Object.assign(window.UE_WebBrowserConfig, config);
        },
        enableHardwareAcceleration: function(enable) {
            window.UE_WebBrowserConfig.enableHardwareAcceleration = enable;
            document.body.style.transform = enable ? 'translateZ(0)' : 'none';
        },
        setMaxFPS: function(fps) {
            window.UE_WebBrowserConfig.maxFPS = fps;
        }
    };
})();
