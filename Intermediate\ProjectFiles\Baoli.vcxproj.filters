<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <None Include="..\..\Baoli.uproject" />
    <Filter Include="Source">
      <UniqueIdentifier>{F31BBDD1-B3E8-3BCC-9652-680E16935819}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\Baoli.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\Source\BaoliEditor.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\clean.bat" />
    <Filter Include="Config">
      <UniqueIdentifier>{FA535FFB-25E1-3D20-B416-52F9BE21E06E}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Config\DefaultEditor.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultEngine.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultGame.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultGameplayTags.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultInput.ini">
      <Filter>Config</Filter>
    </None>
    <Filter Include="Plugins">
      <UniqueIdentifier>{BB38096A-B391-30DC-A0D4-4F3EA6B44507}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\BlockoutToolsPlugin">
      <UniqueIdentifier>{CE26034D-1B92-378E-A134-86E372BEE06C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\BlockoutToolsPlugin\Source">
      <UniqueIdentifier>{22B65921-9F1D-309A-AB1F-0A4D583E6063}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin">
      <UniqueIdentifier>{D1B9549D-F570-33E4-9F2D-23AC04CEAC2F}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\BlockoutToolsEditorPlugin.Build.cs">
      <Filter>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin</Filter>
    </None>
    <Filter Include="Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\Private">
      <UniqueIdentifier>{43BDAEF2-4316-3AD4-B397-36796C8ECD82}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\Private\BlockoutToolsEditorPlugin.cpp">
      <Filter>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\Public">
      <UniqueIdentifier>{F0A39F03-A341-32A3-87BA-25957081040A}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\Public\BlockoutToolsEditorPlugin.h">
      <Filter>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\Public</Filter>
    </ClCompile>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\BlockoutToolsPlugin.uplugin">
      <Filter>Plugins\BlockoutToolsPlugin</Filter>
    </None>
    <Filter Include="Plugins\BlockoutToolsPlugin\Resources">
      <UniqueIdentifier>{70169486-8661-32C2-896B-1487F168ACDC}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\BlockoutToolsEditorIcon16.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\BlockoutToolsIcon64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Box_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Cone_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Corner_Curved_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Corner_Ramp_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Cylinder_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Doorway_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Railing_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Ramp_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Skewbox_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Sphere_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Stairs_Curved_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Stairs_Linear_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Tube_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Blockout_Window_64.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Resources\Icon128.png">
      <Filter>Plugins\BlockoutToolsPlugin\Resources</Filter>
    </None>
    <Filter Include="Plugins\DLSS">
      <UniqueIdentifier>{2A4FACC8-4400-312A-973A-252726C1F41F}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\DLSS.uplugin">
      <Filter>Plugins\DLSS</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Config">
      <UniqueIdentifier>{802B5328-A79F-3D82-B20D-506FBD7B6F81}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Config\FilterPlugin.ini">
      <Filter>Plugins\DLSS\Config</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Resources">
      <UniqueIdentifier>{4801242E-AAEF-3AD9-8CDF-17782BA703FE}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Resources\Icon128.png">
      <Filter>Plugins\DLSS\Resources</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Shaders">
      <UniqueIdentifier>{6B3E8AED-3546-3FC6-9859-9BC2145B3ADB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\DLSS\Shaders\Private">
      <UniqueIdentifier>{12AC913D-BFDC-39FA-A970-8C787C8BFC9C}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Shaders\Private\GBufferResolve.usf">
      <Filter>Plugins\DLSS\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\DLSS\Shaders\Private\VelocityCombine.usf">
      <Filter>Plugins\DLSS\Shaders\Private</Filter>
    </None>
    <Filter Include="Plugins\FSR3">
      <UniqueIdentifier>{DAD19E7F-09CC-344C-B5EC-19569851222E}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\FSR3.uplugin">
      <Filter>Plugins\FSR3</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Resources">
      <UniqueIdentifier>{CA663E9D-AE5F-3673-8A01-AD8D4A6B62A8}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Resources\Icon128.png">
      <Filter>Plugins\FSR3\Resources</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Shaders">
      <UniqueIdentifier>{6FCC07D9-CA5A-3C7A-A615-4FE4543BE0D2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\FSR3\Shaders\Private">
      <UniqueIdentifier>{F44294AE-A55D-3C83-8100-1D6BB0E81077}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_compute_game_vector_field_inpainting_pyramid_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_compute_inpainting_pyramid_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_debug_view_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_disocclusion_mask_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_game_motion_vector_field_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_inpainting_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_optical_flow_vector_field.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_reconstruct_and_dilate_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_reconstruct_prev_depth_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_frameinterpolation_setup_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_accumulate_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_autogen_reactive_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_debug_view_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_luma_instability_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_luma_pyramid_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_prepare_inputs_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_prepare_reactivity_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_rcas_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_shading_change_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_fsr3upscaler_shading_change_pyramid_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_compute_luminance_pyramid_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_compute_optical_flow_advanced_pass_v5.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_compute_scd_divergence_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_filter_optical_flow_pass_v5.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_generate_scd_histogram_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_prepare_luma_pass.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\ffx_opticalflow_scale_optical_flow_advanced_pass_v5.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\PostProcessFFX_FIAdditionalUI.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\PostProcessFFX_FSR3ConvertVelocity.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\PostProcessFFX_FSR3CopyExposure.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\PostProcessFFX_FSR3CreateReactiveMask.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <None Include="..\..\Plugins\FSR3\Shaders\Private\PostProcessFFX_FSR3DeDither.usf">
      <Filter>Plugins\FSR3\Shaders\Private</Filter>
    </None>
    <Filter Include="Plugins\Inkpot">
      <UniqueIdentifier>{06019571-A8AC-373B-B93D-4F60644F9CB8}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\Inkpot\Inkpot.uplugin">
      <Filter>Plugins\Inkpot</Filter>
    </None>
    <Filter Include="Plugins\MergeAssist-master">
      <UniqueIdentifier>{45474EF7-EFF7-399F-83F2-78DAFDABD0B9}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\MergeAssist-master\MergeAssist.uplugin">
      <Filter>Plugins\MergeAssist-master</Filter>
    </None>
    <Filter Include="Plugins\MergeAssist-master\Resources">
      <UniqueIdentifier>{DE0997D3-2C28-3EE5-9B37-43606A70B6F8}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\MergeAssist-master\Resources\Icon128.png">
      <Filter>Plugins\MergeAssist-master\Resources</Filter>
    </None>
    <Filter Include="Plugins\OptimizedWebBrowser">
      <UniqueIdentifier>{B39A3124-D205-3A9F-BE6E-2721B73C7E42}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\OptimizedWebBrowser\OptimizedWebBrowser.uplugin">
      <Filter>Plugins\OptimizedWebBrowser</Filter>
    </None>
    <Filter Include="Plugins\PhysicalLayout">
      <UniqueIdentifier>{3746AA75-21B4-3706-91F7-DD222624D801}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\PhysicalLayout\PhysicalLayout.uplugin">
      <Filter>Plugins\PhysicalLayout</Filter>
    </None>
    <Filter Include="Plugins\PhysicalLayout\Resources">
      <UniqueIdentifier>{F2960420-1A94-3258-9204-1206A690EE24}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\icon.svg">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\Icon128.png">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\icon_file_save_40x.png">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\icon_file_save_clicked_40x.png">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\icon_file_save_hover_40x.png">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paint.png">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paint.svg">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paint2.png">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paint2.svg">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paintselect.png">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\paintselect.svg">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\physicalLayout.png">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\select.png">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\select.svg">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\PhysicalLayout\Resources\selectActors.png">
      <Filter>Plugins\PhysicalLayout\Resources</Filter>
    </None>
    <Filter Include="Plugins\PlatformFunctionsPlugin_5.4">
      <UniqueIdentifier>{67FB1EBB-D2EC-33CB-B0A5-A31AE6F2AA5F}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\PlatformFunctions.uplugin">
      <Filter>Plugins\PlatformFunctionsPlugin_5.4</Filter>
    </None>
    <Filter Include="Plugins\PlatformFunctionsPlugin_5.4\Resources">
      <UniqueIdentifier>{14F59FF1-47C6-39BD-AD30-70EFE088B588}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Resources\Icon128.png">
      <Filter>Plugins\PlatformFunctionsPlugin_5.4\Resources</Filter>
    </None>
    <Filter Include="Plugins\rdBPtools">
      <UniqueIdentifier>{FE4CD089-3941-34BF-B325-5A375BC3276E}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\rdBPtools\rdBPtools.uplugin">
      <Filter>Plugins\rdBPtools</Filter>
    </None>
    <Filter Include="Plugins\rdBPtools\Resources">
      <UniqueIdentifier>{B131F92F-63E3-3977-B0C4-1AE991ED0FC9}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\rdBPtools\Resources\Icon128.png">
      <Filter>Plugins\rdBPtools\Resources</Filter>
    </None>
    <Filter Include="Plugins\rdInst">
      <UniqueIdentifier>{1E4BB5D1-F999-3D13-A569-3CD5DC8E418B}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\rdInst\rdInst.uplugin">
      <Filter>Plugins\rdInst</Filter>
    </None>
    <Filter Include="Plugins\rdInst\Resources">
      <UniqueIdentifier>{37D99DB9-243E-3DD8-95EB-B62974FDF3A3}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\rdInst\Resources\Icon128.png">
      <Filter>Plugins\rdInst\Resources</Filter>
    </None>
    <Filter Include="Plugins\SnappingHelper">
      <UniqueIdentifier>{943EF00D-2AF7-338F-9C61-4A29520568EA}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\SnappingHelper\SnappingHelper.uplugin">
      <Filter>Plugins\SnappingHelper</Filter>
    </None>
    <Filter Include="Plugins\SnappingHelper\Resources">
      <UniqueIdentifier>{2672C319-A9BF-35F6-9320-7C3976746209}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\SnappingHelper\Resources\CheckBox2d_16x.png">
      <Filter>Plugins\SnappingHelper\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\SnappingHelper\Resources\CheckBox2d_UE5_16x.png">
      <Filter>Plugins\SnappingHelper\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\SnappingHelper\Resources\CheckBox3d_16x.png">
      <Filter>Plugins\SnappingHelper\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\SnappingHelper\Resources\CheckBox3d_UE5_16x.png">
      <Filter>Plugins\SnappingHelper\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\SnappingHelper\Resources\Icon128.png">
      <Filter>Plugins\SnappingHelper\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\SnappingHelper\Resources\SpriteCircleIconStroke_16x.png">
      <Filter>Plugins\SnappingHelper\Resources</Filter>
    </None>
    <Filter Include="Plugins\XV3dGS">
      <UniqueIdentifier>{94419251-BA9C-3DFD-9424-930CECE267CE}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\XV3dGS\XV3dGS.uplugin">
      <Filter>Plugins\XV3dGS</Filter>
    </None>
    <Filter Include="Plugins\XV3dGS\Resources">
      <UniqueIdentifier>{5429227F-6707-3191-A7B0-141B686390EF}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\XV3dGS\Resources\Icon128.png">
      <Filter>Plugins\XV3dGS\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\XV3dGS\Resources\LogoThumbnail_40.png">
      <Filter>Plugins\XV3dGS\Resources</Filter>
    </None>
    <None Include="..\..\Plugins\XV3dGS\Resources\PlaceholderButtonIcon.svg">
      <Filter>Plugins\XV3dGS\Resources</Filter>
    </None>
    <Filter Include="Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin">
      <UniqueIdentifier>{19F44DCB-41C6-388A-9A36-AF645E8AC8C5}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\BlockoutToolsPlugin.Build.cs">
      <Filter>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin</Filter>
    </None>
    <Filter Include="Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private">
      <UniqueIdentifier>{38A797E5-762B-3F1D-AFAD-8CB516CAE19F}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private\BlockoutToolsParent.cpp">
      <Filter>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private\BlockoutToolsParent.h">
      <Filter>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private\BlockoutToolsPlugin.cpp">
      <Filter>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private\BlockoutToolsSettings.cpp">
      <Filter>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Public">
      <UniqueIdentifier>{52579334-2E72-3F90-BCD0-186FC9138973}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Public\BlockoutToolsPlugin.h">
      <Filter>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Public\BlockoutToolsSettings.h">
      <Filter>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source">
      <UniqueIdentifier>{095721E9-7112-362B-A65F-D4C3735A234E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\DLSS\Source\DLSSBlueprint">
      <UniqueIdentifier>{38B81CEC-BCA7-3E8C-AB8F-2298C9BD8EF6}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Source\DLSSBlueprint\DLSSBlueprint.Build.cs">
      <Filter>Plugins\DLSS\Source\DLSSBlueprint</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Source\DLSSBlueprint\Private">
      <UniqueIdentifier>{682F86AB-F8B3-32DC-9760-241FB505256F}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSBlueprint\Private\DLSSLibrary.cpp">
      <Filter>Plugins\DLSS\Source\DLSSBlueprint\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\DLSSBlueprint\Public">
      <UniqueIdentifier>{4ADF1750-BD42-34B9-849B-234750EE8229}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSBlueprint\Public\DLSSLibrary.h">
      <Filter>Plugins\DLSS\Source\DLSSBlueprint\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\DLSSEditor">
      <UniqueIdentifier>{26D08ED7-4C0C-36AA-B550-DACB37990FBE}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Source\DLSSEditor\DLSSEditor.Build.cs">
      <Filter>Plugins\DLSS\Source\DLSSEditor</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Source\DLSSEditor\Private">
      <UniqueIdentifier>{26390370-47F5-3F66-973D-76F134430FB5}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSEditor\Private\DLSSEditor.cpp">
      <Filter>Plugins\DLSS\Source\DLSSEditor\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\DLSSEditor\Public">
      <UniqueIdentifier>{AC13DA30-C80C-3269-BD61-FA1F512A6702}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSEditor\Public\DLSSEditor.h">
      <Filter>Plugins\DLSS\Source\DLSSEditor\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\DLSSUtility">
      <UniqueIdentifier>{18A070D9-33E1-308A-B3D4-3AA685386D4A}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Source\DLSSUtility\DLSSUtility.Build.cs">
      <Filter>Plugins\DLSS\Source\DLSSUtility</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Source\DLSSUtility\Private">
      <UniqueIdentifier>{473F9A9A-4B47-30F1-B29F-90938F436688}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Private\DLSSUtility.cpp">
      <Filter>Plugins\DLSS\Source\DLSSUtility\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Private\GBufferResolvePass.cpp">
      <Filter>Plugins\DLSS\Source\DLSSUtility\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Private\VelocityCombinePass.cpp">
      <Filter>Plugins\DLSS\Source\DLSSUtility\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\DLSSUtility\Public">
      <UniqueIdentifier>{********-A443-3B5C-B897-EA157B4E0173}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Public\DLSSUtility.h">
      <Filter>Plugins\DLSS\Source\DLSSUtility\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Public\GBufferResolvePass.h">
      <Filter>Plugins\DLSS\Source\DLSSUtility\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSSUtility\Public\VelocityCombinePass.h">
      <Filter>Plugins\DLSS\Source\DLSSUtility\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\DLSS">
      <UniqueIdentifier>{13D54732-3903-3912-B627-903539FA3F68}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Source\DLSS\DLSS.Build.cs">
      <Filter>Plugins\DLSS\Source\DLSS</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Source\DLSS\Private">
      <UniqueIdentifier>{35D3525A-ECE4-3879-82B3-BC5FB7A38DEE}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSS.cpp">
      <Filter>Plugins\DLSS\Source\DLSS\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSDenoiser.cpp">
      <Filter>Plugins\DLSS\Source\DLSS\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSDenoiser.h">
      <Filter>Plugins\DLSS\Source\DLSS\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSUpscaler.cpp">
      <Filter>Plugins\DLSS\Source\DLSS\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSUpscalerHistory.cpp">
      <Filter>Plugins\DLSS\Source\DLSS\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSUpscalerHistory.h">
      <Filter>Plugins\DLSS\Source\DLSS\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Private\DLSSUpscalerPrivate.h">
      <Filter>Plugins\DLSS\Source\DLSS\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\DLSS\Public">
      <UniqueIdentifier>{8F5697F7-9357-3696-978E-87678C1292F4}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Public\DLSS.h">
      <Filter>Plugins\DLSS\Source\DLSS\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Public\DLSSSettings.h">
      <Filter>Plugins\DLSS\Source\DLSS\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\DLSS\Public\DLSSUpscaler.h">
      <Filter>Plugins\DLSS\Source\DLSS\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\NGXD3D11RHI">
      <UniqueIdentifier>{8ABC8339-48E3-3CF0-AF21-2C045A3B941E}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Source\NGXD3D11RHI\NGXD3D11RHI.Build.cs">
      <Filter>Plugins\DLSS\Source\NGXD3D11RHI</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Source\NGXD3D11RHI\Private">
      <UniqueIdentifier>{B4064E97-B5ED-364E-B585-377DF9FD072F}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXD3D11RHI\Private\NGXD3D11RHI.cpp">
      <Filter>Plugins\DLSS\Source\NGXD3D11RHI\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\NGXD3D11RHI\Public">
      <UniqueIdentifier>{0A8DD7F4-80F2-3BDE-8E47-DDE182F5ACD4}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXD3D11RHI\Public\NGXD3D11RHI.h">
      <Filter>Plugins\DLSS\Source\NGXD3D11RHI\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\NGXD3D12RHI">
      <UniqueIdentifier>{82B6A870-EB08-3906-B9BB-C827A01B7C24}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Source\NGXD3D12RHI\NGXD3D12RHI.Build.cs">
      <Filter>Plugins\DLSS\Source\NGXD3D12RHI</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Source\NGXD3D12RHI\Private">
      <UniqueIdentifier>{213266A9-A2B9-3A1D-ABF1-CFA0EE51A019}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXD3D12RHI\Private\NGXD3D12RHI.cpp">
      <Filter>Plugins\DLSS\Source\NGXD3D12RHI\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\NGXD3D12RHI\Public">
      <UniqueIdentifier>{39ED93AA-2469-3597-8AC1-BEFFD572EDBC}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXD3D12RHI\Public\NGXD3D12RHI.h">
      <Filter>Plugins\DLSS\Source\NGXD3D12RHI\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\NGXRHI">
      <UniqueIdentifier>{E91B9FFD-DF83-3B75-8D96-61F43DF0526A}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Source\NGXRHI\NGXRHI.Build.cs">
      <Filter>Plugins\DLSS\Source\NGXRHI</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Source\NGXRHI\Private">
      <UniqueIdentifier>{8DA2CA04-3D78-3A03-A4FD-1CDC70896B75}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXRHI\Private\NGXRHI.cpp">
      <Filter>Plugins\DLSS\Source\NGXRHI\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\NGXRHI\Public">
      <UniqueIdentifier>{9693D19C-6AAF-348C-BA2E-ADA5BB9EE612}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXRHI\Public\NGXRHI.h">
      <Filter>Plugins\DLSS\Source\NGXRHI\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\NGXVulkanRHIPreInit">
      <UniqueIdentifier>{4D1695D4-0EA1-3E10-91A4-E34FEB390A22}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Source\NGXVulkanRHIPreInit\NGXVulkanRHIPreInit.Build.cs">
      <Filter>Plugins\DLSS\Source\NGXVulkanRHIPreInit</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Source\NGXVulkanRHIPreInit\Private">
      <UniqueIdentifier>{FA61CCCE-CBAA-3C9E-A200-45F5F758C142}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXVulkanRHIPreInit\Private\NGXVulkanRHIPreInit.cpp">
      <Filter>Plugins\DLSS\Source\NGXVulkanRHIPreInit\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\NGXVulkanRHIPreInit\Public">
      <UniqueIdentifier>{E6AA7F60-5C35-3F6F-AB88-70C2A4D321DA}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXVulkanRHIPreInit\Public\NGXVulkanRHIPreInit.h">
      <Filter>Plugins\DLSS\Source\NGXVulkanRHIPreInit\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\NGXVulkanRHI">
      <UniqueIdentifier>{AA70F8D8-4BA4-37E3-9254-4060271CDB94}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Source\NGXVulkanRHI\NGXVulkanRHI.Build.cs">
      <Filter>Plugins\DLSS\Source\NGXVulkanRHI</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Source\NGXVulkanRHI\Private">
      <UniqueIdentifier>{7A0E0D89-071D-3CA2-BDD1-33AC4AF0239F}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXVulkanRHI\Private\NGXVulkanRHI.cpp">
      <Filter>Plugins\DLSS\Source\NGXVulkanRHI\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\NGXVulkanRHI\Public">
      <UniqueIdentifier>{2FBDE505-B41A-35D9-9189-326F2E6D4CDC}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\NGXVulkanRHI\Public\NGXVulkanRHI.h">
      <Filter>Plugins\DLSS\Source\NGXVulkanRHI\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\ThirdParty">
      <UniqueIdentifier>{6853F756-BFE6-32AD-8329-CEB1D6011B25}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\DLSS\Source\ThirdParty\NGX">
      <UniqueIdentifier>{43232E86-2548-3808-AABF-DD6700F9D954}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\LICENSE.txt">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX</Filter>
    </None>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\NGX.Build.cs">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX</Filter>
    </None>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\NGX.tps">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX</Filter>
    </None>
    <Filter Include="Plugins\DLSS\Source\ThirdParty\NGX\Include">
      <UniqueIdentifier>{AC7356D0-55EF-3187-9C8E-215C24058511}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx.h">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Include</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_defs.h">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Include</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_defs_dlssd.h">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Include</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_helpers.h">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Include</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_helpers_dlssd.h">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Include</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_helpers_dlssd_vk.h">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Include</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_helpers_vk.h">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Include</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_params.h">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Include</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_params_dlssd.h">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Include</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Include\nvsdk_ngx_vk.h">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Include</Filter>
    </ClCompile>
    <Filter Include="Plugins\DLSS\Source\ThirdParty\NGX\Utils">
      <UniqueIdentifier>{A3E215EC-BF5A-3669-A812-D71A73F491B6}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\DLSS_Debug_Jitter_Configs.txt">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Utils</Filter>
    </None>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_driver_onscreenindicator.reg">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Utils</Filter>
    </None>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_driver_onscreenindicator_off.reg">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Utils</Filter>
    </None>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_log_off.reg">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Utils</Filter>
    </None>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_log_on.reg">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Utils</Filter>
    </None>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_log_verbose.reg">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Utils</Filter>
    </None>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_log_window_off.reg">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Utils</Filter>
    </None>
    <None Include="..\..\Plugins\DLSS\Source\ThirdParty\NGX\Utils\ngx_log_window_on.reg">
      <Filter>Plugins\DLSS\Source\ThirdParty\NGX\Utils</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Source">
      <UniqueIdentifier>{4ED0AE24-B484-3589-A9BF-C2F77D48F5EF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\FSR3\Source\FFXD3D12Backend">
      <UniqueIdentifier>{544DA1A1-C75D-3FB7-A19E-A1D813294375}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Source\FFXD3D12Backend\FFXD3D12Backend.Build.cs">
      <Filter>Plugins\FSR3\Source\FFXD3D12Backend</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Source\FFXD3D12Backend\Private">
      <UniqueIdentifier>{E992CA5F-76AA-35F3-9EDC-24E8455835F8}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12Backend\Private\FFXD3D12Backend.cpp">
      <Filter>Plugins\FSR3\Source\FFXD3D12Backend\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXD3D12Backend\Public">
      <UniqueIdentifier>{BCD44E85-26B2-31AB-8162-2B8BA92894D0}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12Backend\Public\FFXD3D12Backend.h">
      <Filter>Plugins\FSR3\Source\FFXD3D12Backend\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXD3D12">
      <UniqueIdentifier>{E5B237E0-6564-3733-8A4D-312D70D214BB}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Source\FFXD3D12\FFXD3D12.Build.cs">
      <Filter>Plugins\FSR3\Source\FFXD3D12</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Source\FFXD3D12\Private">
      <UniqueIdentifier>{BE3456D3-D09E-3A5E-A73D-0A723BC572A9}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12\Private\FFXD3D12Includes.cpp">
      <Filter>Plugins\FSR3\Source\FFXD3D12\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12\Private\FFXD3D12Module.cpp">
      <Filter>Plugins\FSR3\Source\FFXD3D12\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXD3D12\Public">
      <UniqueIdentifier>{95E505A6-C0C4-3B3A-B979-755081EC9042}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12\Public\FFXD3D12Includes.h">
      <Filter>Plugins\FSR3\Source\FFXD3D12\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXD3D12\Public\FFXD3D12Module.h">
      <Filter>Plugins\FSR3\Source\FFXD3D12\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXFSR3Api">
      <UniqueIdentifier>{21C54AE4-F280-338F-B827-D43D2A1CEF32}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Source\FFXFSR3Api\FFXFSR3Api.Build.cs">
      <Filter>Plugins\FSR3\Source\FFXFSR3Api</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Source\FFXFSR3Api\Private">
      <UniqueIdentifier>{0D38DDA4-8C57-36B4-A767-B2176322915B}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Api\Private\FFXFSR3.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3Api\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Api\Private\FFXFSR3Module.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3Api\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXFSR3Api\Public">
      <UniqueIdentifier>{939002B5-37E2-3A7F-AE8E-17409F6E4991}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Api\Public\FFXFSR3.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3Api\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Api\Public\FFXFSR3Module.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3Api\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXFSR3Settings">
      <UniqueIdentifier>{28541DC6-E203-380D-B02B-46D3C57EB90D}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Source\FFXFSR3Settings\FFXFSR3Settings.Build.cs">
      <Filter>Plugins\FSR3\Source\FFXFSR3Settings</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Source\FFXFSR3Settings\Private">
      <UniqueIdentifier>{DE4953FA-5210-3EE1-A77B-F6DF2BD117E9}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Settings\Private\FFXFSR3Settings.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3Settings\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXFSR3Settings\Public">
      <UniqueIdentifier>{2E690ED1-4F45-3BEC-A346-3A0D9A32AD86}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3Settings\Public\FFXFSR3Settings.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3Settings\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXFSR3TemporalUpscaling">
      <UniqueIdentifier>{141C9E8A-D535-3972-93EA-D88EE11EB162}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\FFXFSR3TemporalUpscaling.Build.cs">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private">
      <UniqueIdentifier>{50EF4AE2-A2C1-3983-97E8-13F89478B9C6}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3Include.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscaler.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscaler.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscalerHistory.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscalerHistory.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscalerProxy.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscalerProxy.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3TemporalUpscaling.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXFSR3ViewExtension.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRAccumulatePass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRAutogenReactiveMaskPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRDebugViewPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRLumaInstabilityPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRLumaPyramidPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRPrepareInputs.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRPrepareReactivity.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRRCASPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRShaders.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRShaders.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRShadingChangePass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\FFXRHIBackendFSRShadingChangePyramidPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private\LogFFXFSR3.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Public">
      <UniqueIdentifier>{E1EE87FF-4B87-3B20-A771-F0E67E1E0CCF}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Public\FFXFSR3History.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Public\FFXFSR3TemporalUpscaling.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Public\FFXFSR3ViewExtension.h">
      <Filter>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXFrameInterpolationApi">
      <UniqueIdentifier>{5D0A8EE3-0676-3D98-8943-43928905596F}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolationApi\FFXFrameInterpolationApi.Build.cs">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolationApi</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Source\FFXFrameInterpolationApi\Private">
      <UniqueIdentifier>{7FB241BC-B3FE-3E2C-8D0F-AD8FB52BB6D2}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolationApi\Private\FFXFrameInterpolationApi.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolationApi\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolationApi\Private\FFXFrameInterpolationApiModule.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolationApi\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXFrameInterpolationApi\Public">
      <UniqueIdentifier>{FEF88FA6-9E40-3212-910C-579475FBEC3F}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolationApi\Public\FFXFrameInterpolationApi.h">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolationApi\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolationApi\Public\FFXFrameInterpolationApiModule.h">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolationApi\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXFrameInterpolation">
      <UniqueIdentifier>{07A8771B-A82D-368C-9399-024B48ED8909}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\FFXFrameInterpolation.Build.cs">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Source\FFXFrameInterpolation\Private">
      <UniqueIdentifier>{F3A3FA87-AED1-3247-BE6A-F011B36E7B9F}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolation.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolation.h">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationCustomPresent.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationCustomPresent.h">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationModule.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationSlate.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationSlate.h">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationViewExtension.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXFrameInterpolationViewExtension.h">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIDebugViewPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIDisocclusionMask.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIFrameInterpolationPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIFrameInterpolationSetupPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIGameMotionVectorField.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIGameMotionVectorFieldInpainting.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIInpainting.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIInpaintingPyramid.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIOpticalFlowVectorField.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIReconstructAndDilatePass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIReconstructPreviousDepthPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIShaders.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendFIShaders.h">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowComputeLumaPyramidPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowComputeOpticalFlowAdvPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowComputeSCDDivergencePass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowFilterOpticalFlowPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowGenSCDHistogramPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowPrepareLumaPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowScaleOpticalFlowAdvPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowShaders.cpp">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\FFXRHIBackendOpticalFlowShaders.h">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Private\LogFFXFrameInterpolation.h">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXFrameInterpolation\Public">
      <UniqueIdentifier>{328D9D48-EEF5-3913-866D-9C469C8BD719}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Public\FFXFrameInterpolationModule.h">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXFrameInterpolation\Public\IFFXFrameInterpolation.h">
      <Filter>Plugins\FSR3\Source\FFXFrameInterpolation\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXOpticalFlowApi">
      <UniqueIdentifier>{8735B806-E6F1-3C52-B078-3C6ABCBB3822}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Source\FFXOpticalFlowApi\FFXOpticalFlowApi.Build.cs">
      <Filter>Plugins\FSR3\Source\FFXOpticalFlowApi</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Source\FFXOpticalFlowApi\Private">
      <UniqueIdentifier>{22F63C66-1E41-3E59-B1F8-542271AC25A3}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXOpticalFlowApi\Private\FFXOpticalFlowApi.cpp">
      <Filter>Plugins\FSR3\Source\FFXOpticalFlowApi\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXOpticalFlowApi\Private\FFXOpticalFlowApiModule.cpp">
      <Filter>Plugins\FSR3\Source\FFXOpticalFlowApi\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXOpticalFlowApi\Public">
      <UniqueIdentifier>{19A90E7D-AE32-3101-8DB3-B15B134AC1B4}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXOpticalFlowApi\Public\FFXOpticalFlowApi.h">
      <Filter>Plugins\FSR3\Source\FFXOpticalFlowApi\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXOpticalFlowApi\Public\FFXOpticalFlowApiModule.h">
      <Filter>Plugins\FSR3\Source\FFXOpticalFlowApi\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXRHIBackend">
      <UniqueIdentifier>{A9D466D4-6554-3DD2-88F6-27B5A1CDA8A5}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\FFXRHIBackend.Build.cs">
      <Filter>Plugins\FSR3\Source\FFXRHIBackend</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Source\FFXRHIBackend\Private">
      <UniqueIdentifier>{D0DCA09A-B2DB-3FAA-98C5-6BE05B89C8EB}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Private\FFXRHIApi.cpp">
      <Filter>Plugins\FSR3\Source\FFXRHIBackend\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Private\FFXRHIBackend.cpp">
      <Filter>Plugins\FSR3\Source\FFXRHIBackend\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Private\FFXRHIBackendModule.cpp">
      <Filter>Plugins\FSR3\Source\FFXRHIBackend\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Private\FFXRHIBackendShaders.cpp">
      <Filter>Plugins\FSR3\Source\FFXRHIBackend\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Private\FFXRHIBackendSubPass.cpp">
      <Filter>Plugins\FSR3\Source\FFXRHIBackend\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXRHIBackend\Public">
      <UniqueIdentifier>{94D810BE-023F-3607-9CBB-48CD680A7ABC}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Public\FFXRHIBackend.h">
      <Filter>Plugins\FSR3\Source\FFXRHIBackend\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Public\FFXRHIBackendModule.h">
      <Filter>Plugins\FSR3\Source\FFXRHIBackend\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Public\FFXRHIBackendShaders.h">
      <Filter>Plugins\FSR3\Source\FFXRHIBackend\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXRHIBackend\Public\FFXRHIBackendSubPass.h">
      <Filter>Plugins\FSR3\Source\FFXRHIBackend\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXShared">
      <UniqueIdentifier>{0B19231E-D2BD-3BF8-ACDF-56BD173DC356}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\FSR3\Source\FFXShared\FFXShared.Build.cs">
      <Filter>Plugins\FSR3\Source\FFXShared</Filter>
    </None>
    <Filter Include="Plugins\FSR3\Source\FFXShared\Private">
      <UniqueIdentifier>{9E58B4A8-6FB1-38C3-AC37-E23966E454CB}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Private\FFXShared.cpp">
      <Filter>Plugins\FSR3\Source\FFXShared\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Private\FFXSharedBackend.cpp">
      <Filter>Plugins\FSR3\Source\FFXShared\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Private\FFXSharedModule.cpp">
      <Filter>Plugins\FSR3\Source\FFXShared\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\FSR3\Source\FFXShared\Public">
      <UniqueIdentifier>{CA75C857-3B53-39B4-8CD1-880B97749652}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Public\FFXShared.h">
      <Filter>Plugins\FSR3\Source\FFXShared\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Public\FFXSharedBackend.h">
      <Filter>Plugins\FSR3\Source\FFXShared\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\FSR3\Source\FFXShared\Public\FFXSharedModule.h">
      <Filter>Plugins\FSR3\Source\FFXShared\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source">
      <UniqueIdentifier>{30AD779E-DCBB-34C6-A17D-E374D0D07F5F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\Inkpot\Source\InkPlusPlus">
      <UniqueIdentifier>{C2AAA873-865C-3509-8E8C-21B6457D042D}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\InkPlusPlus.Build.cs">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus</Filter>
    </None>
    <None Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\LICENSE.txt">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus</Filter>
    </None>
    <Filter Include="Plugins\Inkpot\Source\InkPlusPlus\Private">
      <UniqueIdentifier>{0FAA4984-D4E4-32C3-AA11-75F2B9F61090}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\InkPlusPlus.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkPlusPlus\Private\Ink">
      <UniqueIdentifier>{B97623ED-DBF7-3711-BC1C-7E4DB71A0A32}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\CallStack.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Choice.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\ChoicePoint.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Container.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\ControlCommand.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\DebugMetadata.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Divert.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Flow.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\InkList.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\InkListItem.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\JsonExtension.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\JsonSerialisation.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\ListDefinition.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\ListDefinitionsOrigin.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\NamedContent.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\NativeFunctionCall.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Object.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Path.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Pointer.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\SearchResult.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\SimpleJson.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\SimpleJsonObject.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\StatePatch.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Story.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\StoryException.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\StoryState.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Tag.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\Value.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\VariableAssignment.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\VariableReference.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Ink\VariableState.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Ink</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkPlusPlus\Private\Utility">
      <UniqueIdentifier>{0EF0644A-7C4F-30BD-A198-C22710AC3271}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Utility\InkPlusPlusLog.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Utility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Private\Utility\InkPlusPlusUtility.cpp">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Private\Utility</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkPlusPlus\Public">
      <UniqueIdentifier>{2A204604-72A5-35C9-995E-43C7F7D1124A}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\InkPlusPlus.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkPlusPlus\Public\Ink">
      <UniqueIdentifier>{197B613D-A13F-316F-A7F2-8D6A176591EE}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\CallStack.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Choice.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\ChoicePoint.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Container.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\ControlCommand.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\DebugMetadata.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Divert.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Error.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Flow.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Glue.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\InkList.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\InkListItem.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\JsonExtension.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\JsonSerialisation.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\ListDefinition.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\ListDefinitionsOrigin.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\NamedContent.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\NativeFunctionCall.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Object.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Path.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Pointer.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\PushPop.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\SearchResult.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\SimpleJson.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\SimpleJsonObject.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\StatePatch.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Story.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\StoryException.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\StoryState.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Tag.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Value.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\VariableAssignment.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\VariableReference.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\VariableState.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Ink\Void.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Ink</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkPlusPlus\Public\Utility">
      <UniqueIdentifier>{13E12356-6A55-35AB-A903-FF614587EDE3}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Utility\InkPlusPlusLog.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Utility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkPlusPlus\Public\Utility\InkPlusPlusUtility.h">
      <Filter>Plugins\Inkpot\Source\InkPlusPlus\Public\Utility</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkpotEditor">
      <UniqueIdentifier>{B5F21358-2F8F-35EF-9C86-A1455B58462F}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\Inkpot\Source\InkpotEditor\InkpotEditor.Build.cs">
      <Filter>Plugins\Inkpot\Source\InkpotEditor</Filter>
    </None>
    <None Include="..\..\Plugins\Inkpot\Source\InkpotEditor\LICENSE.txt">
      <Filter>Plugins\Inkpot\Source\InkpotEditor</Filter>
    </None>
    <Filter Include="Plugins\Inkpot\Source\InkpotEditor\Private">
      <UniqueIdentifier>{BF36DE27-4646-3C70-A321-EFC2F9C11D9B}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\InkpotEditorModule.cpp">
      <Filter>Plugins\Inkpot\Source\InkpotEditor\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkpotEditor\Private\Asset">
      <UniqueIdentifier>{DFC6426C-2A30-38F1-8F2D-64DA3ACF4719}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\Asset\InkpotStoryAssetActions.cpp">
      <Filter>Plugins\Inkpot\Source\InkpotEditor\Private\Asset</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\Asset\InkpotStoryAssetFactory.cpp">
      <Filter>Plugins\Inkpot\Source\InkpotEditor\Private\Asset</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkpotEditor\Private\Compiler">
      <UniqueIdentifier>{9A35A332-44E4-3850-BDB9-1205FF289785}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\Compiler\InkCompiler.cpp">
      <Filter>Plugins\Inkpot\Source\InkpotEditor\Private\Compiler</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkpotEditor\Private\Test">
      <UniqueIdentifier>{16B46317-B4C1-3E23-9C72-8FCE7D8D4F16}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\Test\InkFunctionTests.cpp">
      <Filter>Plugins\Inkpot\Source\InkpotEditor\Private\Test</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Private\Test\InkPlusPlusTest.cpp">
      <Filter>Plugins\Inkpot\Source\InkpotEditor\Private\Test</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkpotEditor\Public">
      <UniqueIdentifier>{D7EFF5CD-AC8E-350F-99AC-28A93827635C}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Public\InkpotEditorModule.h">
      <Filter>Plugins\Inkpot\Source\InkpotEditor\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkpotEditor\Public\Asset">
      <UniqueIdentifier>{D108CF3E-0146-3E14-9745-EE1804CE7A63}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Public\Asset\InkpotStoryAssetActions.h">
      <Filter>Plugins\Inkpot\Source\InkpotEditor\Public\Asset</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Public\Asset\InkpotStoryAssetFactory.h">
      <Filter>Plugins\Inkpot\Source\InkpotEditor\Public\Asset</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkpotEditor\Public\Compiler">
      <UniqueIdentifier>{2D894D32-FE8D-36A2-B6D9-DDB5DF37746C}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Public\Compiler\InkCompiler.h">
      <Filter>Plugins\Inkpot\Source\InkpotEditor\Public\Compiler</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\InkpotEditor\Public\Test">
      <UniqueIdentifier>{6D61B190-4DE9-37D1-9041-07F05DEE29C1}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\InkpotEditor\Public\Test\InkFunctionTests.h">
      <Filter>Plugins\Inkpot\Source\InkpotEditor\Public\Test</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot">
      <UniqueIdentifier>{C8D11451-C3A4-3E1C-8A9A-CAF3CFBB03F8}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\Inkpot\Source\Inkpot\Inkpot.Build.cs">
      <Filter>Plugins\Inkpot\Source\Inkpot</Filter>
    </None>
    <None Include="..\..\Plugins\Inkpot\Source\Inkpot\LICENSE.txt">
      <Filter>Plugins\Inkpot\Source\Inkpot</Filter>
    </None>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Private">
      <UniqueIdentifier>{2C756915-857E-39A8-ABBB-832867802983}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\InkpotModule.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Private\Asset">
      <UniqueIdentifier>{FD93C01C-5106-35A3-AD7F-3ADAD53A2BB2}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Asset\InkpotStoryAsset.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Asset</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Private\Inkpot">
      <UniqueIdentifier>{4A4364FE-69FC-3C88-89AF-3D675DAD64E5}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\Inkpot.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotChoice.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotLine.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotStories.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotStory.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\inkpotStoryFactory.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotStoryHistory.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotStoryInternal.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotValue.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\InkpotWatch.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Inkpot</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Private\Inkpot\AsyncActions">
      <UniqueIdentifier>{5A83040C-FDF5-376B-ADBD-41B5D76EF379}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Inkpot\AsyncActions\AsyncAction_WaitVariableChange.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Inkpot\AsyncActions</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Private\Settings">
      <UniqueIdentifier>{CD33E5C1-AAFC-3AE4-B478-79194A112E8A}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Settings\InkpotCVars.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Settings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Settings\InkpotSettings.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Settings</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Private\Utility">
      <UniqueIdentifier>{479DA28F-4BF2-3113-8C67-1705F1B44061}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Utility\InkpotLog.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Utility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Private\Utility\InkpotUtility.cpp">
      <Filter>Plugins\Inkpot\Source\Inkpot\Private\Utility</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Public">
      <UniqueIdentifier>{39207EEC-5C5F-369F-AFAE-6605B6DFFDEA}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\InkpotModule.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Public\Asset">
      <UniqueIdentifier>{2B84551E-16C6-3A53-8F21-CB5D9F14FC70}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Asset\InkpotStoryAsset.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Asset</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Public\Inkpot">
      <UniqueIdentifier>{5027317C-606E-3EDB-9AED-F027140DCF7A}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\Inkpot.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotChoice.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotLine.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotStories.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotStory.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotStoryFactory.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotStoryHistory.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotStoryInternal.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotValue.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Inkpot</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\InkpotWatch.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Inkpot</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Public\Inkpot\AsyncActions">
      <UniqueIdentifier>{68F92431-8F50-3A93-A972-656869B436F5}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Inkpot\AsyncActions\AsyncAction_WaitVariableChange.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Inkpot\AsyncActions</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Public\Settings">
      <UniqueIdentifier>{739378CE-27C0-32D3-AD65-5BF0499FD751}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Settings\InkpotCVars.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Settings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Settings\InkpotSettings.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Settings</Filter>
    </ClCompile>
    <Filter Include="Plugins\Inkpot\Source\Inkpot\Public\Utility">
      <UniqueIdentifier>{B98BDDCD-B491-3878-A1E2-8BC332435527}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Utility\InkpotLog.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Utility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\Inkpot\Source\Inkpot\Public\Utility\InkpotUtility.h">
      <Filter>Plugins\Inkpot\Source\Inkpot\Public\Utility</Filter>
    </ClCompile>
    <Filter Include="Plugins\MergeAssist-master\Source">
      <UniqueIdentifier>{E0C95C61-8A3B-372E-A63D-2F0F05CA11C4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\MergeAssist-master\Source\MergeAssist">
      <UniqueIdentifier>{60F4014A-8C3E-3F03-BD58-C30723A95E7C}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\MergeAssist.Build.cs">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist</Filter>
    </None>
    <Filter Include="Plugins\MergeAssist-master\Source\MergeAssist\Private">
      <UniqueIdentifier>{3DB17466-C41B-3804-82D0-B0373D1892D8}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\BlueprintMergeData.h">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\FDiffHelper.cpp">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\FDiffHelper.h">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\GraphMergeHelper.cpp">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\GraphMergeHelper.h">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\MergeAssist.cpp">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SBlueprintMergeAssist.cpp">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SBlueprintMergeAssist.h">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SMergeGraphView.cpp">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SMergeGraphView.h">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SMergeTreeView.cpp">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\SMergeTreeView.h">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal">
      <UniqueIdentifier>{39F24EC1-7036-312B-841D-5FAAB971F863}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal\MergeUtils.cpp">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal\MergeUtils.h">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal\SMergeAssetPickerView.cpp">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal\SMergeAssetPickerView.h">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Private\Unreal</Filter>
    </ClCompile>
    <Filter Include="Plugins\MergeAssist-master\Source\MergeAssist\Public">
      <UniqueIdentifier>{91263A0C-C851-3D8E-9FE5-AA36791A8887}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\MergeAssist-master\Source\MergeAssist\Public\MergeAssist.h">
      <Filter>Plugins\MergeAssist-master\Source\MergeAssist\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\OptimizedWebBrowser\Source">
      <UniqueIdentifier>{14E930CC-97AB-35BE-842B-859EBD99BE86}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser">
      <UniqueIdentifier>{6C7DF978-F55E-394A-9D89-A80172A3CA9B}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\OptimizedWebBrowser.build.cs">
      <Filter>Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser</Filter>
    </None>
    <None Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\OptimizedWebBrowser_UPL.xml">
      <Filter>Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser</Filter>
    </None>
    <Filter Include="Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Private">
      <UniqueIdentifier>{84B29192-B062-398C-9ADF-870DF88D0A12}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Private\OptimizedWebBrowser.cpp">
      <Filter>Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Private\OptimizedWebBrowserModule.cpp">
      <Filter>Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Private\WebBrowserOptimizer.cpp">
      <Filter>Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Public">
      <UniqueIdentifier>{39C87FAD-0D7C-3C17-B47E-45B6632DB239}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Public\OptimizedWebBrowser.h">
      <Filter>Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Public\OptimizedWebBrowserModule.h">
      <Filter>Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Public\WebBrowserOptimizer.h">
      <Filter>Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\PhysicalLayout\Source">
      <UniqueIdentifier>{56885A4E-05C3-39D5-AA7D-8E86A9944308}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\PhysicalLayout\Source\PhysicalLayout">
      <UniqueIdentifier>{A85D8910-8ED6-363E-97BC-575531F8A72E}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\PhysicalLayout.Build.cs">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout</Filter>
    </None>
    <Filter Include="Plugins\PhysicalLayout\Source\PhysicalLayout\Private">
      <UniqueIdentifier>{1A038CAA-1FD7-372A-A3C0-E6A7D296CFD2}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayout.cpp">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayoutCommands.cpp">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayoutMode.cpp">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayoutPreset.cpp">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayoutStyle.cpp">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Private\PhysicalLayoutToolkit.cpp">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\PhysicalLayout\Source\PhysicalLayout\Public">
      <UniqueIdentifier>{EF9446C8-0A54-300C-860E-8E1BF2E55F79}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayout.h">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayoutCommands.h">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayoutMode.h">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayoutPreset.h">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayoutStyle.h">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\PhysicalLayoutToolkit.h">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\Public\SlateUtil.h">
      <Filter>Plugins\PhysicalLayout\Source\PhysicalLayout\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\PlatformFunctionsPlugin_5.4\Source">
      <UniqueIdentifier>{50C7DB3B-5BDF-3760-8A5C-CCE58707F5F9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions">
      <UniqueIdentifier>{70829976-45EB-34FC-9696-7E563AD58B0B}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\PlatformFunctions.Build.cs">
      <Filter>Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions</Filter>
    </None>
    <Filter Include="Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Private">
      <UniqueIdentifier>{6D0023A9-2DDC-38FE-84DC-046BC278062F}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Private\PlatformFunctions.cpp">
      <Filter>Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Private\PlatformFunctionsBPLibrary.cpp">
      <Filter>Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Public">
      <UniqueIdentifier>{482A6341-5835-3E02-832C-FCA9162A019A}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Public\PlatformFunctions.h">
      <Filter>Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Public\PlatformFunctionsBPLibrary.h">
      <Filter>Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\rdBPtools\Source">
      <UniqueIdentifier>{A190F783-0A6C-351A-A407-4A227708D1AC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\rdBPtools\Source\rdBPtools">
      <UniqueIdentifier>{B6622B9C-ACDF-36FF-BB33-9E8F1C7E9098}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\rdBPtools\Source\rdBPtools\rdBPtools.Build.cs">
      <Filter>Plugins\rdBPtools\Source\rdBPtools</Filter>
    </None>
    <Filter Include="Plugins\rdBPtools\Source\rdBPtools\Private">
      <UniqueIdentifier>{26BE097C-8A66-346A-8388-41971F9BF455}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPSubsystem.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPSubsystem.h">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPtools.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPtoolsMenuCommands.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPtoolsMenuCommands.h">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPtoolsOptions.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBPtoolsOptions.h">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_About.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_BPOutlinerMenu.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_BPToolbarMenu.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_BrowserContextMenu.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_ChangeMobility.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_ConvertStaticMeshes.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_CopyToLevel.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_CreateFromSelectedActors.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_CreateLevelInstance.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_CreateSpawnActorFromSelection.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_DuplicateAndEdit.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_HarvestInstances.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_Helpers.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_InstanceSettings.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_MirrorBlueprint.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_MoveToFoliage.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_PlaceOnGround.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_RandomSettings.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_RandomSettingsComp.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_RandomSettingsFolder.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_RemoveRandomSettings.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_SetRelyOnActors.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_SetRelyOnActorsComp.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_ShiftObjects.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_Stats.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_ToolMenu.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIAbout.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIConvertToSpawnActor.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UICopyToLevel.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UICreateFromSelectedActors.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UICreateLevelInstance.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIInstanceSettings.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIRandomFolderSettings.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIRandomSettings.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIRandomSettingsComp.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIReplaceInLevel.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UISelectFoliageType.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIShiftObjects.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UIUpdateFromSelectedActors.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_UpdateFromSelectedActors.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdBP_WorldContextMenu.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpActorPicker.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpActorPicker.h">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpAssetPicker.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpAssetPicker.h">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpFolderPicker.cpp">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdPopUpFolderPicker.h">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Private\rdUMGHelpers.h">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\rdBPtools\Source\rdBPtools\Public">
      <UniqueIdentifier>{6C2849A4-C265-35E9-BEA3-E17AF015DF88}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Public\IrdBPtools.h">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdBPtools\Source\rdBPtools\Public\rdBPtools.h">
      <Filter>Plugins\rdBPtools\Source\rdBPtools\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\rdInst\Source">
      <UniqueIdentifier>{DE39B5E8-62E6-3A1A-B2BC-B634FE55B28D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\rdInst\Source\rdInst">
      <UniqueIdentifier>{131A7F9E-7ED4-38C4-BFDE-A0ED7A62A310}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\rdInst\Source\rdInst\rdInst.Build.cs">
      <Filter>Plugins\rdInst\Source\rdInst</Filter>
    </None>
    <Filter Include="Plugins\rdInst\Source\rdInst\Private">
      <UniqueIdentifier>{76555DB9-A019-3545-88A4-B36C368A4166}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Assimilation.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Build.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Conversions.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Instancing.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Pooling.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Randomization.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Spawning.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Themes.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Utilities.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdActor_Visibility.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInst.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Conversions.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Distributed.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Instancing.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_LandscapeModify.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Pooling.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Procedural.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Proxies.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_SpawnStuff.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Splines.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_StaticInstancing.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBaseActor_Utilities.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBPLibrary.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstBreakAndMake.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstSubsystem.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rdInstSubsystem.h">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rd_PixelWrappers.cpp">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Private\rd_PixelWrappers.h">
      <Filter>Plugins\rdInst\Source\rdInst\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\rdInst\Source\rdInst\Public">
      <UniqueIdentifier>{5BB92EE5-5890-33C8-859F-992C09CE9F73}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdActor.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdInst.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdInstances.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdInstBaseActor.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdInstBPLibrary.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdInstStates.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdLandscapeModify.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdPlacement.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdPools.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdProceduralActor.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdProxies.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdSpawnStuff.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdSpawnStuffActor.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdSplines.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\rdInst\Source\rdInst\Public\rdUtilities.h">
      <Filter>Plugins\rdInst\Source\rdInst\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\SnappingHelper\Source">
      <UniqueIdentifier>{61A8DF63-1729-3300-9E63-FBA40C93B6EA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\SnappingHelper\Source\SnappingHelper">
      <UniqueIdentifier>{6459F419-4D96-3112-93B2-3E11B242076B}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\SnappingHelper.Build.cs">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper</Filter>
    </None>
    <Filter Include="Plugins\SnappingHelper\Source\SnappingHelper\Private">
      <UniqueIdentifier>{67529D83-B4CB-3B74-95A7-BEB20A5B55B1}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\SnappingHelper.cpp">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\SnappingHelperCommands.cpp">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\SnappingHelperEdMode.cpp">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\SnappingHelperSettings.cpp">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\SnappingHelperStyle.cpp">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\SnappingHelper\Source\SnappingHelper\Private\Helpers">
      <UniqueIdentifier>{4CEAECAB-8492-337A-8709-3DD7456622D2}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\Helpers\VertexIterators.cpp">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Private\Helpers</Filter>
    </ClCompile>
    <Filter Include="Plugins\SnappingHelper\Source\SnappingHelper\Private\Widgets">
      <UniqueIdentifier>{8AC90080-B134-34B8-8986-DA5ECE29127E}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Private\Widgets\SSnappingHelperComboButton.cpp">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Private\Widgets</Filter>
    </ClCompile>
    <Filter Include="Plugins\SnappingHelper\Source\SnappingHelper\Public">
      <UniqueIdentifier>{77D7686D-FEFA-3FDD-B7EA-C2BE74A89448}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\FSnappingHelperCommands.h">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\SnappingHelper.h">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\SnappingHelperEdMode.h">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\SnappingHelperSettings.h">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Public</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\SnappingHelperStyle.h">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Public</Filter>
    </ClCompile>
    <Filter Include="Plugins\SnappingHelper\Source\SnappingHelper\Public\Helpers">
      <UniqueIdentifier>{C2292C1D-C2AD-38D0-8FC2-73DB9F60780F}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\Helpers\VertexIterators.h">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Public\Helpers</Filter>
    </ClCompile>
    <Filter Include="Plugins\SnappingHelper\Source\SnappingHelper\Public\Widgets">
      <UniqueIdentifier>{A31925D6-E19F-3E64-85C2-9E3B17E3C3B4}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\SnappingHelper\Source\SnappingHelper\Public\Widgets\SSnappingHelperComboButton.h">
      <Filter>Plugins\SnappingHelper\Source\SnappingHelper\Public\Widgets</Filter>
    </ClCompile>
    <Filter Include="Plugins\XV3dGS\Source">
      <UniqueIdentifier>{A5A49ACF-1AE6-3708-8047-527CE2006DD5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\XV3dGS\Source\GSEditor">
      <UniqueIdentifier>{B673A9BD-143A-3F63-937B-81E4A3553AAF}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\XV3dGS\Source\GSEditor\GSEditor.Build.cs">
      <Filter>Plugins\XV3dGS\Source\GSEditor</Filter>
    </None>
    <Filter Include="Plugins\XV3dGS\Source\GSImporter">
      <UniqueIdentifier>{521DA58F-A727-3D93-898F-0260B98859F5}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\XV3dGS\Source\GSImporter\GSImporter.Build.cs">
      <Filter>Plugins\XV3dGS\Source\GSImporter</Filter>
    </None>
    <Filter Include="Plugins\XV3dGS\Source\GSRuntime">
      <UniqueIdentifier>{296E7244-00EE-3E57-AB9B-E7077ECD3E20}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\XV3dGS\Source\GSRuntime\GSRuntime.Build.cs">
      <Filter>Plugins\XV3dGS\Source\GSRuntime</Filter>
    </None>
    <Filter Include="Source\Baoli">
      <UniqueIdentifier>{E741B00A-A5EB-35BE-ADE8-599716867FAB}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\Baoli\Baoli.Build.cs">
      <Filter>Source\Baoli</Filter>
    </None>
    <ClCompile Include="..\..\Source\Baoli\Baoli.cpp">
      <Filter>Source\Baoli</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Baoli.h">
      <Filter>Source\Baoli</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\BaoliGameUserSettings.cpp">
      <Filter>Source\Baoli</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\BaoliGameUserSettings.h">
      <Filter>Source\Baoli</Filter>
    </ClCompile>
    <Filter Include="Source\Baoli\AI">
      <UniqueIdentifier>{7733EAE8-5207-3880-8343-DDCA261A02F0}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\Baoli\AI\AI_CharacterBase.cpp">
      <Filter>Source\Baoli\AI</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\AI\AI_CharacterBase.h">
      <Filter>Source\Baoli\AI</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\AI\MyAIController.cpp">
      <Filter>Source\Baoli\AI</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\AI\MyAIController.h">
      <Filter>Source\Baoli\AI</Filter>
    </ClCompile>
    <Filter Include="Source\Baoli\Interactable">
      <UniqueIdentifier>{538A0D77-140B-3D60-A22E-75939CED36F9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source\Baoli\Interactable\Chest">
      <UniqueIdentifier>{19100872-9E94-3073-A9D5-452330099F6D}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Chest\ChestActorPawn.cpp">
      <Filter>Source\Baoli\Interactable\Chest</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Chest\ChestActorPawn.h">
      <Filter>Source\Baoli\Interactable\Chest</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Chest\KettleActorPawn.cpp">
      <Filter>Source\Baoli\Interactable\Chest</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Chest\KettleActorPawn.h">
      <Filter>Source\Baoli\Interactable\Chest</Filter>
    </ClCompile>
    <Filter Include="Source\Baoli\Interactable\Cover">
      <UniqueIdentifier>{EFFCBBA1-CC8F-3C6D-A822-B8E4A3415423}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Cover\Almirah.cpp">
      <Filter>Source\Baoli\Interactable\Cover</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Cover\Almirah.h">
      <Filter>Source\Baoli\Interactable\Cover</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Cover\Bed.cpp">
      <Filter>Source\Baoli\Interactable\Cover</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Interactable\Cover\Bed.h">
      <Filter>Source\Baoli\Interactable\Cover</Filter>
    </ClCompile>
    <Filter Include="Source\Baoli\Player">
      <UniqueIdentifier>{E492CB67-0578-3DDE-8BEF-3668E7331C48}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\Baoli\Player\Baoli_Character.cpp">
      <Filter>Source\Baoli\Player</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Player\Baoli_Character.h">
      <Filter>Source\Baoli\Player</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Player\Baoli_Controller.cpp">
      <Filter>Source\Baoli\Player</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Baoli\Player\Baoli_Controller.h">
      <Filter>Source\Baoli\Player</Filter>
    </ClCompile>
  </ItemGroup>
</Project>
