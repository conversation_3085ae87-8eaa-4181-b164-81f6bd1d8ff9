// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "WebBrowserOptimizer.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class UObject;
struct FWebBrowserOptimizerSettings;
#ifdef OPTIMIZEDWEBBROWSER_WebBrowserOptimizer_generated_h
#error "WebBrowserOptimizer.generated.h already included, missing '#pragma once' in WebBrowserOptimizer.h"
#endif
#define OPTIMIZEDWEBBROWSER_WebBrowserOptimizer_generated_h

#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_19_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWebBrowserOptimizerSettings_Statics; \
	OPTIMIZEDWEBBROWSER_API static class UScriptStruct* StaticStruct();


template<> OPTIMIZEDWEBBROWSER_API UScriptStruct* StaticStruct<struct FWebBrowserOptimizerSettings>();

#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_56_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execApplyOptimizations);


#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_56_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUWebBrowserOptimizer(); \
	friend struct Z_Construct_UClass_UWebBrowserOptimizer_Statics; \
public: \
	DECLARE_CLASS(UWebBrowserOptimizer, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/OptimizedWebBrowser"), NO_API) \
	DECLARE_SERIALIZER(UWebBrowserOptimizer)


#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_56_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UWebBrowserOptimizer(UWebBrowserOptimizer&&); \
	UWebBrowserOptimizer(const UWebBrowserOptimizer&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UWebBrowserOptimizer); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UWebBrowserOptimizer); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UWebBrowserOptimizer) \
	NO_API virtual ~UWebBrowserOptimizer();


#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_53_PROLOG
#define FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_56_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_56_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_56_INCLASS_NO_PURE_DECLS \
	FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h_56_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> OPTIMIZEDWEBBROWSER_API UClass* StaticClass<class UWebBrowserOptimizer>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Baoli_Plugins_OptimizedWebBrowser_Source_OptimizedWebBrowser_Public_WebBrowserOptimizer_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
