// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Baoli/Interactable/Chest/ChestActorPawn.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeChestActorPawn() {}

// Begin Cross Module References
BAOLI_API UClass* Z_Construct_UClass_AChestActorPawn();
BAOLI_API UClass* Z_Construct_UClass_AChestActorPawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APawn();
ENGINE_API UClass* Z_Construct_UClass_UCameraComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_Baoli();
// End Cross Module References

// Begin Class AChestActorPawn
void AChestActorPawn::StaticRegisterNativesAChestActorPawn()
{
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AChestActorPawn);
UClass* Z_Construct_UClass_AChestActorPawn_NoRegister()
{
	return AChestActorPawn::StaticClass();
}
struct Z_Construct_UClass_AChestActorPawn_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "Interactable/Chest/ChestActorPawn.h" },
		{ "ModuleRelativePath", "Interactable/Chest/ChestActorPawn.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Root_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Chest/ChestActorPawn.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SM_Chest_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Chest/ChestActorPawn.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraComponent_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Chest/ChestActorPawn.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Root;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SM_Chest;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CameraComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AChestActorPawn>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChestActorPawn_Statics::NewProp_Root = { "Root", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChestActorPawn, Root), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Root_MetaData), NewProp_Root_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChestActorPawn_Statics::NewProp_SM_Chest = { "SM_Chest", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChestActorPawn, SM_Chest), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SM_Chest_MetaData), NewProp_SM_Chest_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AChestActorPawn_Statics::NewProp_CameraComponent = { "CameraComponent", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AChestActorPawn, CameraComponent), Z_Construct_UClass_UCameraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraComponent_MetaData), NewProp_CameraComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AChestActorPawn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChestActorPawn_Statics::NewProp_Root,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChestActorPawn_Statics::NewProp_SM_Chest,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AChestActorPawn_Statics::NewProp_CameraComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AChestActorPawn_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AChestActorPawn_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_APawn,
	(UObject* (*)())Z_Construct_UPackage__Script_Baoli,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AChestActorPawn_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AChestActorPawn_Statics::ClassParams = {
	&AChestActorPawn::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_AChestActorPawn_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_AChestActorPawn_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AChestActorPawn_Statics::Class_MetaDataParams), Z_Construct_UClass_AChestActorPawn_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AChestActorPawn()
{
	if (!Z_Registration_Info_UClass_AChestActorPawn.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AChestActorPawn.OuterSingleton, Z_Construct_UClass_AChestActorPawn_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AChestActorPawn.OuterSingleton;
}
template<> BAOLI_API UClass* StaticClass<AChestActorPawn>()
{
	return AChestActorPawn::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AChestActorPawn);
AChestActorPawn::~AChestActorPawn() {}
// End Class AChestActorPawn

// Begin Registration
struct Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Chest_ChestActorPawn_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AChestActorPawn, AChestActorPawn::StaticClass, TEXT("AChestActorPawn"), &Z_Registration_Info_UClass_AChestActorPawn, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AChestActorPawn), 564452356U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Chest_ChestActorPawn_h_2464527763(TEXT("/Script/Baoli"),
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Chest_ChestActorPawn_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Chest_ChestActorPawn_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
