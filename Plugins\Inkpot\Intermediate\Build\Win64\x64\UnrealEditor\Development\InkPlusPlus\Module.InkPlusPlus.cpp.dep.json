{"Version": "1.2", "Data": {"Source": "h:\\p4\\dev\\baoli\\plugins\\inkpot\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\inkplusplus\\module.inkplusplus.cpp", "ProvidedModule": "", "PCH": "h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\x64\\baolieditor\\development\\coreuobject\\sharedpch.coreuobject.project.valapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["h:\\p4\\dev\\baoli\\plugins\\inkpot\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\inkplusplus\\definitions.inkplusplus.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\inkplusplus\\permoduleinline.gen.cpp", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\inkplusplus.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\inkplusplus.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\utility\\inkpluspluslog.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\callstack.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\callstack.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\pushpop.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\json.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\core.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformnamedpipe.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\hal\\platformnamedpipe.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformnamedpipe.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformcriticalsection.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\hal\\platformincludes.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\hal\\platformfilemanager.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\profilingdebugging\\scopeddebuginfo.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\profilingdebugging\\externalprofiler.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\stringutility.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\serialization\\nameasstringproxyarchive.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\containers\\mruarray.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\itransaction.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\change.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\containers\\arraybuilder.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\singlethreadevent.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\hal\\threadmanager.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\containers\\staticbitarray.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\containers\\mapbuilder.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\hal\\threadingbase.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\internationalization\\textlocalizationmanagerglobals.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\internationalization\\culture.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\logging\\logsuppressioninterface.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\hal\\outputdevices.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\logging\\logscopedverbosityoverride.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\outputdevicenull.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\outputdevicememory.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\outputdevicefile.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\outputdevicedebug.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\outputdevicearchivewrapper.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceansierror.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\timeguard.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\serialization\\largememorywriter.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\serialization\\largememorydata.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\serialization\\largememoryreader.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\serialization\\bufferarchive.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\serialization\\arrayreader.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\serialization\\arraywriter.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\serialization\\bufferwriter.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\variant.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\wildcardstring.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\containers\\circularbuffer.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\containers\\circularqueue.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\outputdevicehelper.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\scopedslowtask.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\callbackdevice.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\localtimestampdirectoryvisitor.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\blueprintsobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\buildobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\coreobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\editorobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\frameworkobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\mobileobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\networkingobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\onlineobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\physicsobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\platformobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\renderingobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\sequencerobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\uobject\\vrobjectversion.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceconsole.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\monitoredprocess.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\enumrange.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\jsonglobals.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\policies\\jsonprintpolicy.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\policies\\prettyjsonprintpolicy.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\policies\\condensedjsonprintpolicy.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\serialization\\jsontypes.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\dom\\jsonvalue.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\dom\\jsonobject.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\serialization\\jsonreader.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\serialization\\jsonwriter.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\serialization\\jsonserializer.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\serialization\\jsonserializermacros.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\serialization\\jsondatabag.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\serialization\\jsonserializable.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\serialization\\jsonserializerreader.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\serialization\\jsonserializerbase.h", "d:\\ue_5.5\\engine\\source\\runtime\\json\\public\\serialization\\jsonserializerwriter.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\pointer.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\container.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\object.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\storyexception.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\jsonserialisation.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\story.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\listdefinitionsorigin.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\listdefinition.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\inklistitem.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\error.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\value.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\containers\\union.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\inklist.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\variablestate.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\statepatch.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\variableassignment.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\path.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\searchresult.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\choice.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\choice.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\choicepoint.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\choicepoint.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\container.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\controlcommand.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\controlcommand.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\debugmetadata.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\debugmetadata.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\divert.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\divert.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\flow.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\flow.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\inklist.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\inklistitem.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\jsonextension.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\jsonextension.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\jsonserialisation.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\glue.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\nativefunctioncall.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\void.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\tag.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\variablereference.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\defaultvaluehelper.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\listdefinition.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\listdefinitionsorigin.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\namedcontent.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\nativefunctioncall.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\object.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\path.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\pointer.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\searchresult.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\simplejson.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\simplejsonobject.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\statepatch.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\story.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\ink\\storystate.h", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\storyexception.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\storystate.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\tag.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\value.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\variableassignment.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\variablereference.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\ink\\variablestate.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\utility\\inkpluspluslog.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\private\\utility\\inkplusplusutility.cpp", "h:\\p4\\dev\\baoli\\plugins\\inkpot\\source\\inkplusplus\\public\\utility\\inkplusplusutility.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}