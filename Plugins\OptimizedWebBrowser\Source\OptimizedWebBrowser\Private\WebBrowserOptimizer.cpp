// Copyright Epic Games, Inc. All Rights Reserved.
// Modifications Copyright (C) 2025 Anshul <PERSON>

#include "WebBrowserOptimizer.h"
#include "WebBrowserModule.h"
#include "IWebBrowserSingleton.h"
#include "SWebBrowser.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "UObject/Class.h"
#include "UObject/UObjectGlobals.h"
#include "UObject/PropertyPortFlags.h"

UWebBrowserOptimizer::UWebBrowserOptimizer()
{
}

void UWebBrowserOptimizer::ApplyOptimizations(UObject* WebBrowserWidget, const FWebBrowserOptimizerSettings& Settings)
{
    if (!WebBrowserWidget)
    {
        return;
    }

    // Get the SWebBrowser widget from the UObject
    // We expect the WebBrowserWidget to be a UOptimizedWebBrowser
    // which has a WebBrowserWidget property of type TSharedPtr<SWebBrowser>

    // First, try to execute a JavaScript function to see if this is a web browser widget
    bool bIsWebBrowserWidget = false;

    // Use reflection to find the ExecuteJavascript method
    UFunction* ExecuteJavascriptFunc = WebBrowserWidget->FindFunction(TEXT("ExecuteJavascript"));
    if (ExecuteJavascriptFunc)
    {
        bIsWebBrowserWidget = true;
    }

    if (!bIsWebBrowserWidget)
    {
        UE_LOG(LogTemp, Warning, TEXT("WebBrowserOptimizer: Object is not a web browser widget"));
        return;
    }

    // Now try to find the WebBrowserWidget property using a different approach
    // We'll use the SWebBrowser directly from the OptimizedWebBrowser implementation
    // This is a workaround to avoid TSharedPtr issues in the header

    // Apply the JavaScript directly to the widget
    FString OptimizationJS = GetOptimizationJavaScript();

    // Call the ExecuteJavascript method using reflection
    if (ExecuteJavascriptFunc)
    {
        struct
        {
            FString ScriptText;
        } Params;
        Params.ScriptText = OptimizationJS;

        WebBrowserWidget->ProcessEvent(ExecuteJavascriptFunc, &Params);
    }

    // Now apply the configuration
    // We'll use a different approach for this
    FString ConfigJS = FString::Printf(TEXT(
        "if (window.UE_WebBrowserOptimizations) {"
        "  window.UE_WebBrowserOptimizations.setConfig({"
        "    enableHardwareAcceleration: %s,"
        "    throttleBackgroundTabs: %s,"
        "    optimizeScrolling: %s,"
        "    lazyLoadImages: %s,"
        "    disableAnimationsWhenHidden: %s,"
        "    maxFPS: %d"
        "  });"
        "}"),
        Settings.bHardwareAccelerationEnabled ? TEXT("true") : TEXT("false"),
        Settings.bBackgroundTabThrottlingEnabled ? TEXT("true") : TEXT("false"),
        Settings.bOptimizeScrolling ? TEXT("true") : TEXT("false"),
        Settings.bLazyLoadImages ? TEXT("true") : TEXT("false"),
        Settings.bDisableAnimationsWhenHidden ? TEXT("true") : TEXT("false"),
        Settings.BrowserFrameRate
    );

    // Call the ExecuteJavascript method again with the config
    if (ExecuteJavascriptFunc)
    {
        struct
        {
            FString ScriptText;
        } Params;
        Params.ScriptText = ConfigJS;

        WebBrowserWidget->ProcessEvent(ExecuteJavascriptFunc, &Params);
    }

    // We've applied the optimizations directly, so we can return now
    return;
}

FString UWebBrowserOptimizer::GetOptimizationJavaScript()
{
    // Try to load the optimization JavaScript from file
    FString OptimizationJS;
    FString JSFilePath = FPaths::Combine(FPaths::ProjectContentDir(), TEXT("WebBrowserOptimizations.js"));

    if (FPaths::FileExists(JSFilePath))
    {
        FFileHelper::LoadFileToString(OptimizationJS, *JSFilePath);
    }
    else
    {
        // Fallback to embedded JavaScript if file doesn't exist
        OptimizationJS = TEXT(R"(
(function() {
    // Configuration (can be overridden by UE4)
    window.UE_WebBrowserConfig = window.UE_WebBrowserConfig || {
        enableHardwareAcceleration: true,
        throttleBackgroundTabs: true,
        limitAnimationFrameRate: true,
        optimizeScrolling: true,
        lazyLoadImages: true,
        disableAnimationsWhenHidden: true,
        maxFPS: 30
    };

    // Force hardware acceleration if enabled
    if (window.UE_WebBrowserConfig.enableHardwareAcceleration) {
        document.body.style.transform = 'translateZ(0)';
        document.body.style.backfaceVisibility = 'hidden';
    }

    // Optimize scrolling
    if (window.UE_WebBrowserConfig.optimizeScrolling) {
        // Use passive event listeners for scroll events
        const supportsPassive = (function() {
            let passive = false;
            try {
                const opts = Object.defineProperty({}, 'passive', {
                    get: function() { passive = true; }
                });
                window.addEventListener('test', null, opts);
                window.removeEventListener('test', null, opts);
            } catch (e) {}
            return passive;
        })();

        // Add passive scroll listeners to improve scrolling performance
        if (supportsPassive) {
            const preventDefault = function(e) { e.preventDefault(); };
            document.addEventListener('touchstart', preventDefault, { passive: false });
            document.addEventListener('touchmove', preventDefault, { passive: false });
        }
    }

    // Throttle animations in background tabs
    if (window.UE_WebBrowserConfig.throttleBackgroundTabs) {
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // Reduce animation frame rate when tab is not visible
                if (window._originalRAF && !window._throttledRAF) {
                    window._throttledRAF = true;
                    window.requestAnimationFrame = function(callback) {
                        return setTimeout(callback, 100); // Throttle to ~10fps when hidden
                    };
                }
            } else {
                // Restore original requestAnimationFrame when tab becomes visible
                if (window._originalRAF && window._throttledRAF) {
                    window._throttledRAF = false;
                    window.requestAnimationFrame = window._originalRAF;
                }
            }
        });
    }

    // Limit animation frame rate for better performance
    if (window.UE_WebBrowserConfig.limitAnimationFrameRate && window.requestAnimationFrame) {
        // Store original requestAnimationFrame
        window._originalRAF = window.requestAnimationFrame;

        // Calculate frame interval based on maxFPS
        const frameInterval = 1000 / window.UE_WebBrowserConfig.maxFPS;
        let lastFrameTime = 0;

        // Override requestAnimationFrame to limit frame rate
        window.requestAnimationFrame = function(callback) {
            return window._originalRAF(function(timestamp) {
                const elapsed = timestamp - lastFrameTime;
                if (elapsed >= frameInterval) {
                    lastFrameTime = timestamp - (elapsed % frameInterval);
                    callback(timestamp);
                } else {
                    window.requestAnimationFrame(callback);
                }
            });
        };
    }

    // Disable animations when page is not visible
    if (window.UE_WebBrowserConfig.disableAnimationsWhenHidden) {
        document.addEventListener('visibilitychange', function() {
            const style = document.createElement('style');
            if (document.hidden) {
                style.textContent = '* { animation-play-state: paused !important; transition: none !important; }';
            } else {
                style.textContent = '';
            }
            document.head.appendChild(style);

            // Remove the style element after it's been applied
            setTimeout(function() {
                if (style.parentNode) {
                    style.parentNode.removeChild(style);
                }
            }, 100);
        });
    }

    // Expose API to UE4
    window.UE_WebBrowserOptimizations = {
        setConfig: function(config) {
            window.UE_WebBrowserConfig = Object.assign(window.UE_WebBrowserConfig, config);
        },
        enableHardwareAcceleration: function(enable) {
            window.UE_WebBrowserConfig.enableHardwareAcceleration = enable;
            document.body.style.transform = enable ? 'translateZ(0)' : 'none';
        },
        setMaxFPS: function(fps) {
            window.UE_WebBrowserConfig.maxFPS = fps;
        }
    };
})();
        )");
    }

    return OptimizationJS;
}


