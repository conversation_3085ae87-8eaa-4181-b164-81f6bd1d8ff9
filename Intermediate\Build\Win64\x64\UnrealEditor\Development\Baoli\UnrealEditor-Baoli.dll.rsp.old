/MANIFEST:EMBED
/MANIFESTINPUT:"..\Build\Windows\Resources\Default-Win64.manifest"
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"E:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\lib\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\um\x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\BaoliEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\AI_CharacterBase.gen.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Almirah.gen.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Baoli.init.gen.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\BaoliGameUserSettings.gen.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Baoli_Character.gen.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Baoli_Controller.gen.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Bed.gen.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\ChestActorPawn.gen.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\KettleActorPawn.gen.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\MyAIController.gen.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Baoli.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\BaoliGameUserSettings.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\AI_CharacterBase.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\MyAIController.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\ChestActorPawn.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\KettleActorPawn.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Almirah.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Bed.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Baoli_Character.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Baoli_Controller.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\PerModuleInline.gen.cpp.obj"
"H:\P4\dev\Baoli\Intermediate\Build\Win64\x64\UnrealEditor\Development\Baoli\Default.rc2.res"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Engine\UnrealEditor-Engine.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\AIModule\UnrealEditor-AIModule.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Core\UnrealEditor-Core.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\CoreUObject\UnrealEditor-CoreUObject.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\InputCore\UnrealEditor-InputCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\UMG\UnrealEditor-UMG.lib"
"..\Plugins\EnhancedInput\Intermediate\Build\Win64\x64\UnrealEditor\Development\EnhancedInput\UnrealEditor-EnhancedInput.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\AnimGraphRuntime\UnrealEditor-AnimGraphRuntime.lib"
"..\Plugins\Animation\MotionWarping\Intermediate\Build\Win64\x64\UnrealEditor\Development\MotionWarping\UnrealEditor-MotionWarping.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\NavigationSystem\UnrealEditor-NavigationSystem.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
/OUT:"H:\P4\dev\Baoli\Binaries\Win64\UnrealEditor-Baoli.dll"
/PDB:"H:\P4\dev\Baoli\Binaries\Win64\UnrealEditor-Baoli.pdb"
/ignore:4078