// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Baoli/BaoliGameUserSettings.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeBaoliGameUserSettings() {}

// Begin Cross Module References
BAOLI_API UClass* Z_Construct_UClass_UBaoliGameUserSettings();
BAOLI_API UClass* Z_Construct_UClass_UBaoliGameUserSettings_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UGameUserSettings();
UPackage* Z_Construct_UPackage__Script_Baoli();
// End Cross Module References

// Begin Class UBaoliGameUserSettings Function GetBaoliGameUserSettings
struct Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics
{
	struct BaoliGameUserSettings_eventGetBaoliGameUserSettings_Parms
	{
		UBaoliGameUserSettings* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "BaoliGameUserSettings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaoliGameUserSettings_eventGetBaoliGameUserSettings_Parms, ReturnValue), Z_Construct_UClass_UBaoliGameUserSettings_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UBaoliGameUserSettings, nullptr, "GetBaoliGameUserSettings", nullptr, nullptr, Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::BaoliGameUserSettings_eventGetBaoliGameUserSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::BaoliGameUserSettings_eventGetBaoliGameUserSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UBaoliGameUserSettings::execGetBaoliGameUserSettings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UBaoliGameUserSettings**)Z_Param__Result=UBaoliGameUserSettings::GetBaoliGameUserSettings();
	P_NATIVE_END;
}
// End Class UBaoliGameUserSettings Function GetBaoliGameUserSettings

// Begin Class UBaoliGameUserSettings Function SetCustomVariable
struct Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics
{
	struct BaoliGameUserSettings_eventSetCustomVariable_Parms
	{
		int32 MIS;
		int32 LRS;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "BaoliGameUserSettings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MIS;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LRS;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::NewProp_MIS = { "MIS", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaoliGameUserSettings_eventSetCustomVariable_Parms, MIS), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::NewProp_LRS = { "LRS", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(BaoliGameUserSettings_eventSetCustomVariable_Parms, LRS), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::NewProp_MIS,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::NewProp_LRS,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UBaoliGameUserSettings, nullptr, "SetCustomVariable", nullptr, nullptr, Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::PropPointers), sizeof(Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::BaoliGameUserSettings_eventSetCustomVariable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::Function_MetaDataParams), Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::BaoliGameUserSettings_eventSetCustomVariable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UBaoliGameUserSettings::execSetCustomVariable)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MIS);
	P_GET_PROPERTY(FIntProperty,Z_Param_LRS);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCustomVariable(Z_Param_MIS,Z_Param_LRS);
	P_NATIVE_END;
}
// End Class UBaoliGameUserSettings Function SetCustomVariable

// Begin Class UBaoliGameUserSettings
void UBaoliGameUserSettings::StaticRegisterNativesUBaoliGameUserSettings()
{
	UClass* Class = UBaoliGameUserSettings::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetBaoliGameUserSettings", &UBaoliGameUserSettings::execGetBaoliGameUserSettings },
		{ "SetCustomVariable", &UBaoliGameUserSettings::execSetCustomVariable },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UBaoliGameUserSettings);
UClass* Z_Construct_UClass_UBaoliGameUserSettings_NoRegister()
{
	return UBaoliGameUserSettings::StaticClass();
}
struct Z_Construct_UClass_UBaoliGameUserSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * \n */" },
#endif
		{ "IncludePath", "BaoliGameUserSettings.h" },
		{ "ModuleRelativePath", "BaoliGameUserSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonitorIndexSetting_MetaData[] = {
		{ "Category", "GameUserSettings" },
		{ "ModuleRelativePath", "BaoliGameUserSettings.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LumenReflectionSetting_MetaData[] = {
		{ "Category", "GameUserSettings" },
		{ "ModuleRelativePath", "BaoliGameUserSettings.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MonitorIndexSetting;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LumenReflectionSetting;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UBaoliGameUserSettings_GetBaoliGameUserSettings, "GetBaoliGameUserSettings" }, // 682934833
		{ &Z_Construct_UFunction_UBaoliGameUserSettings_SetCustomVariable, "SetCustomVariable" }, // 3215554031
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UBaoliGameUserSettings>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UBaoliGameUserSettings_Statics::NewProp_MonitorIndexSetting = { "MonitorIndexSetting", nullptr, (EPropertyFlags)0x0020080000004004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UBaoliGameUserSettings, MonitorIndexSetting), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonitorIndexSetting_MetaData), NewProp_MonitorIndexSetting_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UBaoliGameUserSettings_Statics::NewProp_LumenReflectionSetting = { "LumenReflectionSetting", nullptr, (EPropertyFlags)0x0020080000004004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UBaoliGameUserSettings, LumenReflectionSetting), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LumenReflectionSetting_MetaData), NewProp_LumenReflectionSetting_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UBaoliGameUserSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UBaoliGameUserSettings_Statics::NewProp_MonitorIndexSetting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UBaoliGameUserSettings_Statics::NewProp_LumenReflectionSetting,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UBaoliGameUserSettings_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UBaoliGameUserSettings_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameUserSettings,
	(UObject* (*)())Z_Construct_UPackage__Script_Baoli,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UBaoliGameUserSettings_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UBaoliGameUserSettings_Statics::ClassParams = {
	&UBaoliGameUserSettings::StaticClass,
	"GameUserSettings",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UBaoliGameUserSettings_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UBaoliGameUserSettings_Statics::PropPointers),
	0,
	0x409000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UBaoliGameUserSettings_Statics::Class_MetaDataParams), Z_Construct_UClass_UBaoliGameUserSettings_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UBaoliGameUserSettings()
{
	if (!Z_Registration_Info_UClass_UBaoliGameUserSettings.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UBaoliGameUserSettings.OuterSingleton, Z_Construct_UClass_UBaoliGameUserSettings_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UBaoliGameUserSettings.OuterSingleton;
}
template<> BAOLI_API UClass* StaticClass<UBaoliGameUserSettings>()
{
	return UBaoliGameUserSettings::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UBaoliGameUserSettings);
UBaoliGameUserSettings::~UBaoliGameUserSettings() {}
// End Class UBaoliGameUserSettings

// Begin Registration
struct Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UBaoliGameUserSettings, UBaoliGameUserSettings::StaticClass, TEXT("UBaoliGameUserSettings"), &Z_Registration_Info_UClass_UBaoliGameUserSettings, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UBaoliGameUserSettings), 857280799U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_2769269660(TEXT("/Script/Baoli"),
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
