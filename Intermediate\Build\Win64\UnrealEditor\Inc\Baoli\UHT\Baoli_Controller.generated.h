// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Player/Baoli_Controller.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class UInputAction;
enum class ECharacterState : uint8;
#ifdef BAOLI_Baoli_Controller_generated_h
#error "Baoli_Controller.generated.h already included, missing '#pragma once' in Baoli_Controller.h"
#endif
#define BAOLI_Baoli_Controller_generated_h

#define FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_21_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execChangeCharacterState); \
	DECLARE_FUNCTION(execGetCharacterState); \
	DECLARE_FUNCTION(execLineTrace); \
	DECLARE_FUNCTION(execTraceForCamera); \
	DECLARE_FUNCTION(execTraceForInteraction);


#define FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_21_CALLBACK_WRAPPERS
#define FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_21_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesABaoli_Controller(); \
	friend struct Z_Construct_UClass_ABaoli_Controller_Statics; \
public: \
	DECLARE_CLASS(ABaoli_Controller, APlayerController, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Baoli"), NO_API) \
	DECLARE_SERIALIZER(ABaoli_Controller)


#define FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_21_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API ABaoli_Controller(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ABaoli_Controller(ABaoli_Controller&&); \
	ABaoli_Controller(const ABaoli_Controller&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ABaoli_Controller); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ABaoli_Controller); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(ABaoli_Controller) \
	NO_API virtual ~ABaoli_Controller();


#define FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_18_PROLOG
#define FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_21_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_21_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_21_CALLBACK_WRAPPERS \
	FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_21_INCLASS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_21_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BAOLI_API UClass* StaticClass<class ABaoli_Controller>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Baoli_Source_Baoli_Player_Baoli_Controller_h


#define FOREACH_ENUM_ECHARACTERSTATE(op) \
	op(ECharacterState::OverWorld) \
	op(ECharacterState::Mechanic) \
	op(ECharacterState::GameMenu) 

enum class ECharacterState : uint8;
template<> struct TIsUEnumClass<ECharacterState> { enum { Value = true }; };
template<> BAOLI_API UEnum* StaticEnum<ECharacterState>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
