// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for Baoli
#pragma once
#include "H:/P4/dev/Baoli/Intermediate/Build/Win64/x64/BaoliEditor/Development/UnrealEd/SharedDefinitions.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h"
#undef BAOLI_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 1
#define UE_PROJECT_NAME Baoli
#define UE_TARGET_NAME BaoliEditor
#define UE_MODULE_NAME "Baoli"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define AIMODULE_API DLLIMPORT
#define BAOLI_API DLLEXPORT
#define ENHANCEDINPUT_API DLLIMPORT
#define ANIMGRAPHRUNTIME_API DLLIMPORT
#define MOTIONWARPING_API DLLIMPORT
#define ANIMGRAPH_API DLLIMPORT
