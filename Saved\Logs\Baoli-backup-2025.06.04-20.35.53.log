﻿Log file open, 06/05/25 01:42:07
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=28836)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: Baoli
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\P4\dev\Baoli\Baoli.uproject -skipcompile""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.261790
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-3A55774D4EAB744EFDFB22974D5F1D26
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/P4/dev/Baoli/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogConfig: Display: Loading IOS ini files took 0.04 seconds
LogConfig: Display: Loading Android ini files took 0.04 seconds
LogConfig: Display: Loading Mac ini files took 0.04 seconds
LogConfig: Display: Loading Windows ini files took 0.05 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading Unix ini files took 0.05 seconds
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.05 seconds
LogConfig: Display: Loading Linux ini files took 0.05 seconds
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin Chooser
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin JsonBlueprintUtilities
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraFluids
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin GameFeatures
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogAssetRegistry: Display: Asset registry cache read as 106.3 MiB from H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin ModularGameplay
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ScriptableToolsFramework
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebBrowserWidget
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationLocomotionLibrary
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin AnimationWarping
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin BlendStack
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin PoseSearch
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin MotionWarping
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetReferenceRestrictions
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin ScriptableToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin AxFImporter
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin MDLImporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin ActorPalette
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GPULightmass
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin USDImporter
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin AnalyticsBlueprintLibrary
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin Reflex
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin MotionTrajectory
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Project plugin OptimizedWebBrowser
LogPluginManager: Mounting Project plugin PlatformFunctions
LogPluginManager: Mounting Project plugin SnappingHelper
LogPluginManager: Mounting Project plugin Inkpot
LogPluginManager: Mounting Project plugin rdBPtools
SourceControl: Revision control is disabled
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum ********** concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogGPULightmass: GPULightmass module is loaded
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: NVIDIA GeForce RTX 2080 Ti
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: -skipcompile
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 36
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.52ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Chooser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'JsonBlueprintUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.06ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/NiagaraFluids.ini) has wildcard redirect /NiagaraSimulationStages/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataRegistry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameFeatures' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryScripting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ModularGameplay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ScriptableToolsFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebBrowserWidget' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationLocomotionLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationWarping' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayInsights' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PoseSearch' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MotionWarping' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetReferenceRestrictions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ScriptableToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AxFImporter' had been unloaded. Reloading on-demand took 0.06ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/MDLImporter.ini) has wildcard redirect /DatasmithContent/Materials/MDL/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorPalette' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GPULightmass' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'USDImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.37ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnalyticsBlueprintLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Reflex' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MotionTrajectory' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OptimizedWebBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformFunctions' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SnappingHelper' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Inkpot' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'rdBPtools' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.06.04-20.12.07:866][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.04-20.12.07:866][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.04-20.12.07:866][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.04-20.12.07:866][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.04-20.12.07:866][  0]LogConfig: CVar [[r.Mobile.EnableNoPrecomputedLightingCSMShader:1]] deferred - dummy variable created
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.AllowGlobalClipPlane:0]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.CustomDepth:3]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.VirtualTextures:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.AntiAliasingMethod:4]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.ReflectionCaptureResolution:32]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs:0]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.MegaLights.EnableForProject:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.PathTracing:0]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.RayTracing.Shadows:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.RayTracing.UseTextureLod:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject:0]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.CustomDepthTemporalAAJitter:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default:75.000000]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default.Desktop.Mode:0]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.Shadow.CSMCaching:0]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.MSAACount:8]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileSize:32]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileBorderSize:2]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.MaxTextureSize:4096]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.UseCompression:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.VT.TileBorderSize:4]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.DefaultTexelsPerVertex:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.AllowStaticLighting:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.Shadow.UnbuiltPreviewInGame:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.NormalMapsForStaticLighting:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing.LightingMode:0]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.VirtualTexturedLightmaps:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing:1]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.Lumen.ScreenTracingSource:0]]
[2025.06.04-20.12.07:866][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure:0]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Bias:0.000000]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Method:2]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[r.vt.rvt.HighQualityPerPixelHeight:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.04-20.12.07:867][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.04-20.12.07:867][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.04-20.12.07:867][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.04-20.12.07:867][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.04-20.12.07:870][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.04-20.12.07:870][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.04-20.12.07:870][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.04-20.12.07:870][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.04-20.12.07:871][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.06.04-20.12.07:871][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.04-20.12.07:871][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.04-20.12.07:871][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.04-20.12.07:871][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.04-20.12.07:875][  0]LogRHI: Using Default RHI: D3D12
[2025.06.04-20.12.07:875][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.04-20.12.07:875][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.04-20.12.07:886][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.04-20.12.07:886][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.04-20.12.08:022][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.04-20.12.08:022][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.04-20.12.08:022][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 3 output[s]
[2025.06.04-20.12.08:022][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.04-20.12.08:022][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.04-20.12.08:180][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.04-20.12.08:180][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.04-20.12.08:180][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.04-20.12.08:180][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.04-20.12.08:180][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.04-20.12.08:185][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.04-20.12.08:185][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.04-20.12.08:185][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.04-20.12.08:305][  0]LogD3D12RHI: Found D3D12 adapter 3: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.04-20.12.08:305][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.04-20.12.08:305][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.04-20.12.08:305][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.04-20.12.08:305][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.04-20.12.08:305][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.04-20.12.08:305][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.04-20.12.08:308][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.04-20.12.08:308][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.04-20.12.08:308][  0]LogHAL: Display: Platform has ~ 64 GB [68630138880 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.04-20.12.08:308][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.04-20.12.08:308][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.04-20.12.08:308][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.04-20.12.08:308][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.04-20.12.08:308][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.04-20.12.08:309][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.04-20.12.08:309][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.04-20.12.08:309][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.04-20.12.08:309][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.04-20.12.08:309][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.04-20.12.08:309][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.04-20.12.08:309][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.04-20.12.08:309][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.06.04-20.12.08:309][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/P4/dev/Baoli/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.04-20.12.08:309][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.06.04-20.12.08:309][  0]LogInit: User: Shashank
[2025.06.04-20.12.08:309][  0]LogInit: CPU Page size=4096, Cores=16
[2025.06.04-20.12.08:309][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.04-20.12.09:092][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.06.04-20.12.09:092][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.04-20.12.09:092][  0]LogMemory: Process Physical Memory: 692.64 MB used, 727.19 MB peak
[2025.06.04-20.12.09:092][  0]LogMemory: Process Virtual Memory: 768.44 MB used, 771.82 MB peak
[2025.06.04-20.12.09:092][  0]LogMemory: Physical Memory: 24061.53 MB used,  41389.27 MB free, 65450.80 MB total
[2025.06.04-20.12.09:092][  0]LogMemory: Virtual Memory: 29984.63 MB used,  39562.17 MB free, 69546.80 MB total
[2025.06.04-20.12.09:092][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.04-20.12.09:095][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.04-20.12.09:103][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.04-20.12.09:103][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.04-20.12.09:106][  0]LogInit: Using OS detected language (en-GB).
[2025.06.04-20.12.09:106][  0]LogInit: Using OS detected locale (en-IN).
[2025.06.04-20.12.09:108][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.04-20.12.09:109][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.04-20.12.09:352][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.04-20.12.09:352][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.06.04-20.12.09:352][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.04-20.12.09:365][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.04-20.12.09:365][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.04-20.12.09:481][  0]LogRHI: Using Default RHI: D3D12
[2025.06.04-20.12.09:481][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.04-20.12.09:481][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.04-20.12.09:481][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.04-20.12.09:481][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.04-20.12.09:481][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.04-20.12.09:482][  0]LogWindows: Attached monitors:
[2025.06.04-20.12.09:482][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.06.04-20.12.09:482][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.06.04-20.12.09:482][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.06.04-20.12.09:482][  0]LogWindows: Found 3 attached monitors.
[2025.06.04-20.12.09:482][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.04-20.12.09:482][  0]LogRHI: RHI Adapter Info:
[2025.06.04-20.12.09:482][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.06.04-20.12.09:482][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.04-20.12.09:482][  0]LogRHI:      Driver Date: 4-25-2025
[2025.06.04-20.12.09:482][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.06.04-20.12.09:509][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.04-20.12.09:575][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.04-20.12.09:575][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.04-20.12.09:662][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: Raster order views are supported
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.04-20.12.09:662][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.04-20.12.09:687][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000008E42BB35300)
[2025.06.04-20.12.09:687][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000008E42BB35580)
[2025.06.04-20.12.09:688][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000008E42BB35800)
[2025.06.04-20.12.09:688][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.04-20.12.09:688][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.04-20.12.09:688][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.06.04-20.12.09:688][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.06.04-20.12.09:688][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.04-20.12.09:688][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.04-20.12.09:699][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.04-20.12.09:703][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.04-20.12.09:711][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all'
[2025.06.04-20.12.09:711][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all" ]
[2025.06.04-20.12.09:733][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.04-20.12.09:733][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.04-20.12.09:733][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.04-20.12.09:733][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.04-20.12.09:733][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.04-20.12.09:733][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.06.04-20.12.09:733][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.04-20.12.09:733][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.04-20.12.09:733][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.04-20.12.09:758][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.04-20.12.09:758][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.04-20.12.09:758][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.04-20.12.09:758][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.04-20.12.09:758][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.04-20.12.09:758][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.04-20.12.09:758][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.04-20.12.09:758][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.04-20.12.09:758][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.04-20.12.09:758][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.04-20.12.09:773][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.04-20.12.09:773][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.04-20.12.09:788][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.04-20.12.09:788][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.04-20.12.09:788][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.04-20.12.09:788][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.04-20.12.09:803][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.06.04-20.12.09:803][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.06.04-20.12.09:803][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.06.04-20.12.09:817][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.04-20.12.09:818][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.04-20.12.09:818][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.04-20.12.09:818][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.04-20.12.09:832][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.04-20.12.09:832][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.04-20.12.09:849][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.04-20.12.09:849][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.04-20.12.09:849][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.04-20.12.09:849][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.04-20.12.09:849][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.04-20.12.09:894][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_METAL
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.04-20.12.09:897][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.04-20.12.09:897][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.06.04-20.12.09:897][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.06.04-20.12.09:899][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.04-20.12.09:899][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/P4/dev/Baoli/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.04-20.12.09:899][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.04-20.12.09:899][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/P4/dev/Baoli/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.04-20.12.09:899][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.04-20.12.09:959][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.06.04-20.12.09:959][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.04-20.12.09:959][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.04-20.12.09:960][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.04-20.12.09:960][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.04-20.12.09:961][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.04-20.12.09:961][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.06.04-20.12.09:961][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 32896 --child-id Zen_32896_Startup'
[2025.06.04-20.12.10:030][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.04-20.12.10:031][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.070 seconds
[2025.06.04-20.12.10:032][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.04-20.12.10:036][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.06.04-20.12.10:036][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.05ms. RandomReadSpeed=1234.81MBs, RandomWriteSpeed=340.15MBs. Assigned SpeedClass 'Local'
[2025.06.04-20.12.10:036][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.04-20.12.10:036][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.04-20.12.10:036][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.04-20.12.10:036][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.04-20.12.10:036][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.04-20.12.10:036][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.04-20.12.10:036][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.04-20.12.10:037][  0]LogShaderCompilers: Guid format shader working directory is 33 characters bigger than the processId version (H:/P4/dev/Baoli/Intermediate/Shaders/WorkingDirectory/32896/).
[2025.06.04-20.12.10:037][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/411124C344F4B57F1660C09FA13ADABC/'.
[2025.06.04-20.12.10:038][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.06.04-20.12.10:038][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.06.04-20.12.10:039][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/P4/dev/Baoli/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.04-20.12.10:039][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.04-20.12.10:455][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.04-20.12.11:025][  0]LogSlate: Using FreeType 2.10.0
[2025.06.04-20.12.11:025][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.04-20.12.11:025][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.04-20.12.11:025][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.04-20.12.11:026][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.04-20.12.11:026][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.04-20.12.11:026][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.04-20.12.11:026][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.04-20.12.11:052][  0]LogAssetRegistry: FAssetRegistry took 0.0028 seconds to start up
[2025.06.04-20.12.11:053][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.04-20.12.11:058][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.04-20.12.11:216][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.04-20.12.11:219][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.04-20.12.11:219][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.04-20.12.11:219][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.04-20.12.11:239][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.04-20.12.11:239][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.04-20.12.11:275][  0]LogDeviceProfileManager: Active device profile: [000008E445E36A00][000008E43C62C000 66] WindowsEditor
[2025.06.04-20.12.11:275][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.04-20.12.11:276][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.04-20.12.11:279][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.06.04-20.12.11:279][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.06.04-20.12.11:307][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:308][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.06.04-20.12.11:308][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.04-20.12.11:308][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:308][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.06.04-20.12.11:308][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.04-20.12.11:308][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:308][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.06.04-20.12.11:308][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.04-20.12.11:308][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:308][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.06.04-20.12.11:308][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.04-20.12.11:309][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:309][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:309][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.06.04-20.12.11:309][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.04-20.12.11:309][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:309][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.06.04-20.12.11:309][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.04-20.12.11:309][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.04-20.12.11:310][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.04-20.12.11:311][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.06.04-20.12.11:311][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.04-20.12.11:312][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:312][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.06.04-20.12.11:313][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.04-20.12.11:313][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.06.04-20.12.11:313][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.04-20.12.11:313][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:313][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.06.04-20.12.11:313][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.04-20.12.11:313][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.06.04-20.12.11:313][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.04-20.12.11:313][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.11:313][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.06.04-20.12.11:313][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.04-20.12.11:472][  0]LogMeshReduction: Display: Mesh reduction module (r.MeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.04-20.12.11:473][  0]LogMeshReduction: Display: Skeletal mesh reduction module (r.SkeletalMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.04-20.12.11:473][  0]LogMeshReduction: Display: HLOD mesh reduction module (r.ProxyLODMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.04-20.12.11:484][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.04-20.12.11:484][  0]LogMeshReduction: Display: Using InstaLODMeshReduction for automatic skeletal mesh reduction
[2025.06.04-20.12.11:484][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.04-20.12.11:484][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.04-20.12.11:484][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.04-20.12.11:605][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.04-20.12.11:623][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.04-20.12.11:633][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.04-20.12.11:634][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.04-20.12.11:793][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.04-20.12.11:793][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.04-20.12.11:797][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.04-20.12.11:797][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.04-20.12.11:798][  0]LogLiveCoding: Display: First instance in process group "UE_Baoli_0x736adef1", spawning console
[2025.06.04-20.12.11:801][  0]LogLiveCoding: Display: Waiting for server
[2025.06.04-20.12.11:833][  0]LogSlate: Border
[2025.06.04-20.12.11:833][  0]LogSlate: BreadcrumbButton
[2025.06.04-20.12.11:833][  0]LogSlate: Brushes.Title
[2025.06.04-20.12.11:833][  0]LogSlate: Default
[2025.06.04-20.12.11:833][  0]LogSlate: Icons.Save
[2025.06.04-20.12.11:833][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.04-20.12.11:833][  0]LogSlate: ListView
[2025.06.04-20.12.11:833][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.04-20.12.11:833][  0]LogSlate: SoftwareCursor_Grab
[2025.06.04-20.12.11:833][  0]LogSlate: TableView.DarkRow
[2025.06.04-20.12.11:833][  0]LogSlate: TableView.Row
[2025.06.04-20.12.11:833][  0]LogSlate: TreeView
[2025.06.04-20.12.11:907][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.04-20.12.12:189][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.04-20.12.12:191][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.659 ms
[2025.06.04-20.12.12:225][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.43ms
[2025.06.04-20.12.12:276][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.04-20.12.12:276][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.04-20.12.12:276][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.04-20.12.12:276][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.06.04-20.12.12:435][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: EF8F5208BF604159800000000000C100 | Instance: F38E7ABB4543FF6C506FF0BF5523F2CF (DESKTOP-E41IK6R-32896).
[2025.06.04-20.12.13:158][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.04-20.12.14:019][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.04-20.12.14:019][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.04-20.12.14:032][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.04-20.12.14:348][  0]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.06.04-20.12.14:349][  0]LogCEFBrowser: CEF GPU acceleration enabled
[2025.06.04-20.12.14:851][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.55ms
[2025.06.04-20.12.14:963][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.04-20.12.14:963][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.04-20.12.14:964][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:57241'.
[2025.06.04-20.12.14:966][  0]LogUdpMessaging: Display: Added local interface '172.22.192.1' to multicast group '230.0.0.1:6666'
[2025.06.04-20.12.14:966][  0]LogUdpMessaging: Display: Added local interface '172.25.160.1' to multicast group '230.0.0.1:6666'
[2025.06.04-20.12.14:966][  0]LogUdpMessaging: Display: Added local interface '192.168.1.12' to multicast group '230.0.0.1:6666'
[2025.06.04-20.12.14:979][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.04-20.12.15:159][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.04-20.12.15:159][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.06.04-20.12.15:159][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.06.04-20.12.15:159][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.04-20.12.15:159][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.04-20.12.15:228][  0]LogTemp: Warning: ✓ AI Perception system enabled and events bound
[2025.06.04-20.12.15:293][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.06.04-20.12.15:379][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.04-20.12.15:379][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.04-20.12.15:439][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.04-20.12.15:457][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.04-20.12.15:457][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.04-20.12.15:628][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.06.04-20.12.15:628][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.06.04-20.12.15:629][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.06.04-20.12.15:629][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.06.04-20.12.15:629][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.06.04-20.12.15:629][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.06.04-20.12.15:630][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.06.04-20.12.15:630][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.06.04-20.12.15:630][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.06.04-20.12.15:630][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.06.04-20.12.15:631][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.06.04-20.12.15:631][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.06.04-20.12.15:631][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.06.04-20.12.15:631][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.06.04-20.12.15:632][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.06.04-20.12.15:633][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.06.04-20.12.15:633][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.06.04-20.12.15:633][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.06.04-20.12.15:633][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.06.04-20.12.15:634][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.06.04-20.12.15:634][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.06.04-20.12.15:634][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.06.04-20.12.15:634][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.06.04-20.12.15:634][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.06.04-20.12.15:634][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.06.04-20.12.15:635][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.06.04-20.12.15:635][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.06.04-20.12.15:635][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.06.04-20.12.15:635][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.06.04-20.12.15:635][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.06.04-20.12.15:636][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.06.04-20.12.15:637][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.06.04-20.12.15:637][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.06.04-20.12.15:637][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.06.04-20.12.15:638][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.06.04-20.12.16:012][  0]LogTimingProfiler: Initialize
[2025.06.04-20.12.16:012][  0]LogTimingProfiler: OnSessionChanged
[2025.06.04-20.12.16:012][  0]LoadingProfiler: Initialize
[2025.06.04-20.12.16:012][  0]LoadingProfiler: OnSessionChanged
[2025.06.04-20.12.16:012][  0]LogNetworkingProfiler: Initialize
[2025.06.04-20.12.16:012][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.04-20.12.16:012][  0]LogMemoryProfiler: Initialize
[2025.06.04-20.12.16:012][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.04-20.12.16:236][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.46ms
[2025.06.04-20.12.16:310][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.48ms
[2025.06.04-20.12.17:609][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.04-20.12.17:642][  0]LogCollectionManager: Loaded 1 collections in 0.000665 seconds
[2025.06.04-20.12.17:644][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Saved/Collections/' took 0.00s
[2025.06.04-20.12.17:646][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Developers/Shashank/Collections/' took 0.00s
[2025.06.04-20.12.17:648][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Collections/' took 0.00s
[2025.06.04-20.12.17:709][  0]LogConfig: Branch 'Plugins' had been unloaded. Reloading on-demand took 0.44ms
[2025.06.04-20.12.17:711][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.04-20.12.17:712][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.04-20.12.17:713][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.04-20.12.17:713][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.04-20.12.17:713][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.04-20.12.17:713][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.04-20.12.17:738][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.04-20.12.17:738][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.04-20.12.17:788][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.04-20.12.17:788][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.04-20.12.17:790][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.04-20.12.17:790][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.04-20.12.17:790][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.04-20.12.17:790][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.04-20.12.17:814][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.04-20.12.17:814][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.04-20.12.17:830][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-06-04T20:12:17.830Z using C
[2025.06.04-20.12.17:830][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=Baoli, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.04-20.12.17:830][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.04-20.12.17:831][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.04-20.12.17:835][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.04-20.12.17:835][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.04-20.12.17:835][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.04-20.12.17:835][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000050
[2025.06.04-20.12.17:835][  0]LogFab: Display: Logging in using persist
[2025.06.04-20.12.17:836][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.06.04-20.12.17:923][  0]LogUObjectArray: 47651 objects as part of root set at end of initial load.
[2025.06.04-20.12.17:923][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.04-20.12.17:934][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 40254 public script object entries (1077.02 KB)
[2025.06.04-20.12.17:934][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.06.04-20.12.18:007][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MetasoundFrontend.MetaSoundFrontendDocumentBuilder. Time(ms): 6.8
[2025.06.04-20.12.18:047][  0]LogEngine: Initializing Engine...
[2025.06.04-20.12.18:048][  0]LogGameFeatures: Initializing game features subsystem
[2025.06.04-20.12.18:057][  0]InkPlusPlus: FStory::FStory 000008E461A5CC10
[2025.06.04-20.12.18:057][  0]InkPlusPlus: Warning: WARNING: Version of ink used to build story doesn't match current version of engine. Non-critical, but recommend synchronising.
[2025.06.04-20.12.18:059][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.04-20.12.18:082][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.04-20.12.18:167][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.04-20.12.18:178][  0]LogGameFeatures: Scanning for built-in game feature plugins
[2025.06.04-20.12.18:178][  0]LogGameFeatures: Loading 233 builtins
[2025.06.04-20.12.18:178][  0]LogGameFeatures: Display: Total built in plugin load time 0.0007s
[2025.06.04-20.12.18:178][  0]LogStats: BuiltInGameFeaturePlugins loaded. -  0.001 s
[2025.06.04-20.12.18:179][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.04-20.12.18:194][  0]LogNetVersion: Set ProjectVersion to Alpha. Version Checksum will be recalculated on next use.
[2025.06.04-20.12.18:194][  0]LogInit: Texture streaming: Enabled
[2025.06.04-20.12.18:203][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.06.04-20.12.18:208][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.04-20.12.18:213][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.04-20.12.18:213][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.04-20.12.18:214][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.04-20.12.18:214][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.04-20.12.18:214][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.04-20.12.18:214][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.04-20.12.18:214][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.04-20.12.18:214][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.04-20.12.18:214][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.04-20.12.18:214][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.04-20.12.18:214][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.04-20.12.18:214][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.04-20.12.18:214][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.04-20.12.18:214][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.04-20.12.18:214][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.04-20.12.18:217][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.04-20.12.18:274][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.04-20.12.18:274][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.04-20.12.18:275][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.04-20.12.18:275][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.04-20.12.18:275][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.04-20.12.18:275][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.04-20.12.18:278][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.04-20.12.18:278][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.04-20.12.18:278][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.04-20.12.18:278][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.04-20.12.18:278][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.04-20.12.18:306][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.04-20.12.18:309][  0]LogInit: Undo buffer set to 256 MB
[2025.06.04-20.12.18:309][  0]LogInit: Transaction tracking system initialized
[2025.06.04-20.12.18:404][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.51ms
[2025.06.04-20.12.18:406][  0]LocalizationService: Localization service is disabled
[2025.06.04-20.12.18:666][  0]LogPython: Using Python 3.11.8
[2025.06.04-20.12.18:854][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/' took 0.28s
[2025.06.04-20.12.19:670][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.06.04-20.12.19:702][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (0 permutations).
[2025.06.04-20.12.19:861][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.04-20.12.19:862][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.04-20.12.19:870][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.04-20.12.19:920][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.04-20.12.19:920][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.04-20.12.19:921][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.04-20.12.19:921][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.04-20.12.19:921][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.04-20.12.19:921][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.04-20.12.19:945][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.04-20.12.19:945][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.04-20.12.19:951][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.04-20.12.19:951][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.04-20.12.19:981][  0]LogEditorDataStorage: Initializing
[2025.06.04-20.12.19:988][  0]LogEditorDataStorage: Initialized
[2025.06.04-20.12.20:013][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.06.04-20.12.20:017][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/MHI.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:017][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performances/MHP_Scene1_01.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:017][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performance/MHP_Baoli.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:017][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:018][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.04-20.12.20:019][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.04-20.12.20:040][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.04-20.12.20:069][  0]LogUnrealEdMisc: Loading editor; pre map load, took 12.964
[2025.06.04-20.12.20:070][  0]Cmd: MAP LOAD FILE="H:/P4/dev/Baoli/Content/Levels/DefaultLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.04-20.12.20:072][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.12.20:072][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.12.20:081][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.04-20.12.20:082][  0]InkPlusPlus: FStory::~FStory 000008E461A5CC10
[2025.06.04-20.12.20:082][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.50ms
[2025.06.04-20.12.20:149][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.04-20.12.20:149][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Glossy (0x2CCA389A36D3E860)
[2025.06.04-20.12.20:149][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/material (0x38FB08B605AA9364)
[2025.06.04-20.12.20:149][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.04-20.12.20:149][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Dark (0xC267FEC07D768F2)
[2025.06.04-20.12.20:189][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.04-20.12.20:189][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVback (0x8B181E584DB8A471) /Game/Assets/TV/TVback (0x8B181E584DB8A471) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.04-20.12.20:396][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS (0xF21345B7066A3DF7)
[2025.06.04-20.12.20:883][  0]LogLinker: Warning: [AssetLog] H:\P4\dev\Baoli\Content\BaoliAssets\BrickInstances\Brick_low_001.uasset: VerifyImport: Failed to find script package for import object 'Package /Script/rdInst'
[2025.06.04-20.12.21:171][  0]LogAssetRegistry: Display: Asset registry cache written as 106.3 MiB to H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin
[2025.06.04-20.12.22:599][  0]LogEditorDomain: Display: Class /Script/rdInst.rdInstAssetUserData is imported by a package but does not exist in memory. EditorDomain keys for packages using it will be invalid if it still exists.
	To clear this message, resave packages that use the deleted class, or load its module earlier than the packages that use it are referenced.
[2025.06.04-20.12.23:783][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: WaitingForIo) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.23:786][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 334 to allow recursive sync load to finish
[2025.06.04-20.12.23:786][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.04-20.12.23:787][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: ExportsDone) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.23:787][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 335 to allow recursive sync load to finish
[2025.06.04-20.12.23:787][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.04-20.12.24:368][  0]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\CBP_SandboxCharacter.uasset: [Compiler] Input pin  Debug Session Unique Identifier  specifying non-default value no longer exists on node  Motion Match . Please refresh node or reset pin to default value to remove pin.
[2025.06.04-20.12.25:461][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: WaitingForIo) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.25:462][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 336 to allow recursive sync load to finish
[2025.06.04-20.12.25:462][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.04-20.12.25:462][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: ExportsDone) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.04-20.12.25:462][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 337 to allow recursive sync load to finish
[2025.06.04-20.12.25:462][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.04-20.12.25:582][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:583][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Handplant' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Handplant.MSS_FoleySound_Handplant' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:584][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Jump' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Jump.MSS_FoleySound_Jump' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:585][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Land' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Land.MSS_FoleySound_Land' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:587][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Run' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Run.MSS_FoleySound_Run' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:587][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunBackwards.MSS_FoleySound_RunBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:589][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunStrafe' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunStrafe.MSS_FoleySound_RunStrafe' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:590][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Scuff' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Scuff.MSS_FoleySound_Scuff' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:591][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffPivot' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffPivot.MSS_FoleySound_ScuffPivot' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:592][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffWall' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffWall.MSS_FoleySound_ScuffWall' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:593][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Tumble' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Tumble.MSS_FoleySound_Tumble' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:593][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Walk' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Walk.MSS_FoleySound_Walk' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.25:595][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_WalkBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_WalkBackwards.MSS_FoleySound_WalkBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.04-20.12.27:215][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.06.04-20.12.27:285][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.06.04-20.12.27:297][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_High...
[2025.06.04-20.12.27:309][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_High...
[2025.06.04-20.12.27:689][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.27:692][  0]LogSkeletalMesh: Built Skeletal Mesh [0.40s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High
[2025.06.04-20.12.27:702][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.06.04-20.12.28:323][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants_High...
[2025.06.04-20.12.28:671][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.28:673][  0]LogSkeletalMesh: Built Skeletal Mesh [0.35s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High
[2025.06.04-20.12.28:756][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.28:759][  0]LogSkeletalMesh: Built Skeletal Mesh [1.46s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High
[2025.06.04-20.12.29:046][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_High...
[2025.06.04-20.12.29:063][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_Cap_01_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.04-20.12.29:063][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelNut_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.04-20.12.29:064][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelLeaf_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.04-20.12.29:411][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.29:413][  0]LogSkeletalMesh: Built Skeletal Mesh [0.37s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High
[2025.06.04-20.12.30:808][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_hoodie_nrm_High...
[2025.06.04-20.12.31:231][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.31:234][  0]LogSkeletalMesh: Built Skeletal Mesh [0.43s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High
[2025.06.04-20.12.31:293][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Foley.SM_Foley.
[2025.06.04-20.12.31:293][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.04-20.12.31:293][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Ambient.SM_Ambient.
[2025.06.04-20.12.31:293][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.04-20.12.31:293][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Reverb.SM_Reverb.
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Jumps
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Stops
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.04-20.12.32:957][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.04-20.12.32:958][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.04-20.12.32:959][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.04-20.12.32:959][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.04-20.12.32:959][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.04-20.12.32:959][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.04-20.12.32:959][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.04-20.12.32:959][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.04-20.12.33:101][  0]LogPoseSearch: 25abbe71a7361c623bd3755ff5e1fbe7a56e7f94 - PSD_Dense_Jumps BeginCache
[2025.06.04-20.12.33:102][  0]LogPoseSearch: 25abbe71a7361c623bd3755ff5e1fbe7a56e7f94 - PSD_Dense_Jumps BuildIndex From Cache
[2025.06.04-20.12.33:113][  0]LogPoseSearch: 52bcfc861296fe46e902c3e1977b2a893663552c - PSD_Dense_Jumps_Far BeginCache
[2025.06.04-20.12.33:113][  0]LogPoseSearch: 52bcfc861296fe46e902c3e1977b2a893663552c - PSD_Dense_Jumps_Far BuildIndex From Cache
[2025.06.04-20.12.33:121][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.04-20.12.33:121][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.04-20.12.33:121][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.04-20.12.33:121][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.04-20.12.33:121][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.04-20.12.33:121][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.04-20.12.33:121][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.04-20.12.33:121][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Stops
[2025.06.04-20.12.33:121][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.04-20.12.33:121][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.04-20.12.33:122][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.04-20.12.33:122][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.04-20.12.33:122][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.04-20.12.33:123][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.04-20.12.33:123][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.04-20.12.33:171][  0]LogPoseSearch: c903d208ce6ce6d6d57c16e8029907a718a6c7e9 - PSD_Dense_Stand_Idle_Lands_Heavy BeginCache
[2025.06.04-20.12.33:172][  0]LogPoseSearch: c903d208ce6ce6d6d57c16e8029907a718a6c7e9 - PSD_Dense_Stand_Idle_Lands_Heavy BuildIndex From Cache
[2025.06.04-20.12.33:190][  0]LogPoseSearch: 2c155ec5c459344cf515e7737ccfee7b0c910810 - PSD_Dense_Stand_Run_Lands_Heavy BeginCache
[2025.06.04-20.12.33:191][  0]LogPoseSearch: 2c155ec5c459344cf515e7737ccfee7b0c910810 - PSD_Dense_Stand_Run_Lands_Heavy BuildIndex From Cache
[2025.06.04-20.12.33:217][  0]LogPoseSearch: 0647c6013483ddb2053168a7d66d3f931f221b6f - PSD_Dense_Stand_Walk_Lands_Heavy BeginCache
[2025.06.04-20.12.33:217][  0]LogPoseSearch: 0647c6013483ddb2053168a7d66d3f931f221b6f - PSD_Dense_Stand_Walk_Lands_Heavy BuildIndex From Cache
[2025.06.04-20.12.33:320][  0]LogPoseSearch: b9ad6da2b4015614aa861a663ab0dc2c7376fcdd - PSD_Traversal BeginCache
[2025.06.04-20.12.33:321][  0]LogPoseSearch: b9ad6da2b4015614aa861a663ab0dc2c7376fcdd - PSD_Traversal BuildIndex From Cache
[2025.06.04-20.12.33:330][  0]LogPoseSearch: 1f816991d0c81fa165c9258a64c0e110cb545466 - PSD_Dense_Jumps_FromTraversal BeginCache
[2025.06.04-20.12.33:331][  0]LogPoseSearch: 1f816991d0c81fa165c9258a64c0e110cb545466 - PSD_Dense_Jumps_FromTraversal BuildIndex From Cache
[2025.06.04-20.12.33:338][  0]LogPoseSearch: 44dfcd0439dfacb33784494a07f81e5c4107264f - PSD_Dense_Stand_Walk_FromTraversal BeginCache
[2025.06.04-20.12.33:338][  0]LogPoseSearch: 44dfcd0439dfacb33784494a07f81e5c4107264f - PSD_Dense_Stand_Walk_FromTraversal BuildIndex From Cache
[2025.06.04-20.12.33:339][  0]LogPoseSearch: 2ef3b5bccd181d28d29d656af18a9552f7025b60 - PSD_Dense_Stand_Run_SpinTransition BeginCache
[2025.06.04-20.12.33:339][  0]LogPoseSearch: 2ef3b5bccd181d28d29d656af18a9552f7025b60 - PSD_Dense_Stand_Run_SpinTransition BuildIndex From Cache
[2025.06.04-20.12.33:340][  0]LogPoseSearch: c3b27113a33ed0195cb86def79fac28ef88b955a - PSD_Dense_Stand_Walk_SpinTransition BeginCache
[2025.06.04-20.12.33:340][  0]LogPoseSearch: c3b27113a33ed0195cb86def79fac28ef88b955a - PSD_Dense_Stand_Walk_SpinTransition BuildIndex From Cache
[2025.06.04-20.12.33:350][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_btm_shorts_nrm...
[2025.06.04-20.12.33:351][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_top_crewneckt_nrm...
[2025.06.04-20.12.33:353][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.06.04-20.12.33:353][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants...
[2025.06.04-20.12.33:353][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm...
[2025.06.04-20.12.33:356][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.06.04-20.12.33:356][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_shs_flipflops...
[2025.06.04-20.12.33:441][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.33:443][  0]LogSkeletalMesh: Built Skeletal Mesh [0.09s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops
[2025.06.04-20.12.33:511][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.04-20.12.33:532][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.33:534][  0]LogSkeletalMesh: Built Skeletal Mesh [0.18s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm
[2025.06.04-20.12.33:534][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_casualsneakers...
[2025.06.04-20.12.33:545][  0]LogPoseSearch: 2fb236583b053e85334fffc7f28ad671a7ba6fcd - PSD_Dense_Crouch_Idle BeginCache
[2025.06.04-20.12.33:545][  0]LogPoseSearch: 2fb236583b053e85334fffc7f28ad671a7ba6fcd - PSD_Dense_Crouch_Idle BuildIndex From Cache
[2025.06.04-20.12.33:569][  0]LogPoseSearch: 3eaff9342445c24b7281b43f7e167cf00b79697d - PSD_Dense_Crouch_Loop BeginCache
[2025.06.04-20.12.33:569][  0]LogPoseSearch: 3eaff9342445c24b7281b43f7e167cf00b79697d - PSD_Dense_Crouch_Loop BuildIndex From Cache
[2025.06.04-20.12.33:607][  0]LogPoseSearch: e5bb22fa3f0832f6b1de4740e665f8b8a0cc4a40 - PSD_Dense_Crouch_Pivot BeginCache
[2025.06.04-20.12.33:608][  0]LogPoseSearch: e5bb22fa3f0832f6b1de4740e665f8b8a0cc4a40 - PSD_Dense_Crouch_Pivot BuildIndex From Cache
[2025.06.04-20.12.33:646][  0]LogPoseSearch: 10463b9b5d9a2019be2ed12e5f4f9c3378a2c9df - PSD_Dense_Crouch_Start BeginCache
[2025.06.04-20.12.33:646][  0]LogPoseSearch: 10463b9b5d9a2019be2ed12e5f4f9c3378a2c9df - PSD_Dense_Crouch_Start BuildIndex From Cache
[2025.06.04-20.12.33:685][  0]LogPoseSearch: fb459193d12be90ae89d82deeb58b955bd5ded69 - PSD_Dense_Crouch_Stops BeginCache
[2025.06.04-20.12.33:686][  0]LogPoseSearch: fb459193d12be90ae89d82deeb58b955bd5ded69 - PSD_Dense_Crouch_Stops BuildIndex From Cache
[2025.06.04-20.12.33:723][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.33:726][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.33:727][  0]LogSkeletalMesh: Built Skeletal Mesh [0.22s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.04-20.12.33:728][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.06.04-20.12.33:729][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants
[2025.06.04-20.12.33:730][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_MC_FaceMesh...
[2025.06.04-20.12.33:763][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.33:767][  0]LogSkeletalMesh: Built Skeletal Mesh [0.41s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.06.04-20.12.33:769][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_Child2_FaceMesh...
[2025.06.04-20.12.33:821][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.33:823][  0]LogSkeletalMesh: Built Skeletal Mesh [0.29s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers
[2025.06.04-20.12.33:825][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.06.04-20.12.34:158][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.34:162][  0]LogSkeletalMesh: Built Skeletal Mesh [0.43s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.06.04-20.12.34:163][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.04-20.12.34:193][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.06.04-20.12.34:330][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.34:332][  0]LogSkeletalMesh: Built Skeletal Mesh [0.17s] /Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.04-20.12.34:333][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.04-20.12.34:588][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.34:593][  0]LogSkeletalMesh: Built Skeletal Mesh [0.26s] /Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.04-20.12.34:668][  0]LogSkeletalMesh: Building Skeletal Mesh Kellan_FaceMesh...
[2025.06.04-20.12.34:876][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.34:880][  0]LogSkeletalMesh: Built Skeletal Mesh [1.53s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm
[2025.06.04-20.12.34:883][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.34:887][  0]LogSkeletalMesh: Built Skeletal Mesh [1.53s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.06.04-20.12.34:911][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.34:914][  0]LogSkeletalMesh: Built Skeletal Mesh [1.56s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm
[2025.06.04-20.12.34:954][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.34:958][  0]LogSkeletalMesh: Built Skeletal Mesh [0.29s] /Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh
[2025.06.04-20.12.35:090][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'DefaultLevel'.
[2025.06.04-20.12.35:090][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.04-20.12.35:141][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.04-20.12.44:607][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.44:621][  0]LogSkeletalMesh: Built Skeletal Mesh [10.89s] /Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh
[2025.06.04-20.12.44:674][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.44:688][  0]LogSkeletalMesh: Built Skeletal Mesh [10.92s] /Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh
[2025.06.04-20.12.44:748][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.04-20.12.44:760][  0]LogSkeletalMesh: Built Skeletal Mesh [10.94s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.06.04-20.12.44:831][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.04-20.12.45:027][  0]LogUObjectHash: Compacting FUObjectHashTables data took   2.61ms
[2025.06.04-20.12.45:030][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.04-20.12.45:031][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.098ms to complete.
[2025.06.04-20.12.45:040][  0]LogUnrealEdMisc: Total Editor Startup Time, took 37.936
[2025.06.04-20.12.45:236][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.06.04-20.12.45:347][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.12.45:407][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.12.45:464][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.12.45:524][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.12.45:581][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.04-20.12.45:582][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.04-20.12.45:582][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.04-20.12.45:583][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.04-20.12.45:583][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.04-20.12.45:583][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.04-20.12.45:583][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.04-20.12.45:583][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.04-20.12.45:584][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.04-20.12.45:584][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.04-20.12.45:584][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.04-20.12.45:584][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.04-20.12.45:584][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.04-20.12.45:584][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.04-20.12.45:584][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.04-20.12.45:585][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.04-20.12.45:585][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.04-20.12.45:585][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.04-20.12.45:585][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.04-20.12.45:585][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.04-20.12.45:668][  0]LogSlate: Took 0.000211 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.04-20.12.45:716][  0]LogSlate: Took 0.002094 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/NotoColorEmoji.ttf' (7610K)
[2025.06.04-20.12.45:716][  0]LogSlate: Took 0.000205 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.04-20.12.45:717][  0]LogSlate: Took 0.000727 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansFallback.ttf' (3848K)
[2025.06.04-20.12.45:905][  0]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.12.45:924][  0]LogStall: Startup...
[2025.06.04-20.12.45:927][  0]LogStall: Startup complete.
[2025.06.04-20.12.45:967][  0]LogLoad: (Engine Initialization) Total time: 38.86 seconds
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.12.46:288][  0]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.12.46:289][  0]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.12.46:289][  0]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.12.46:289][  0]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.12.46:289][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.04-20.12.46:289][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.04-20.12.46:299][  0]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Config/DefaultEngine.ini'
[2025.06.04-20.12.46:527][  0]LogSlate: Took 0.000091 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.04-20.12.46:592][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.06.04-20.12.46:592][  0]LogStreaming: Display: FlushAsyncLoading(342): 1 QueuedPackages, 0 AsyncPackages
[2025.06.04-20.12.46:594][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.06.04-20.12.46:594][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.06.04-20.12.46:594][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.04-20.12.46:685][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.04-20.12.46:685][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.06.04-20.12.46:686][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.06.04-20.12.46:686][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.06.04-20.12.46:686][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.04-20.12.46:742][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.04-20.12.46:743][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.06.04-20.12.47:121][  0]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.04-20.12.47:183][  0]LogD3D12RHI: Creating RTPSO with 24 shaders (0 cached, 24 new) took 11.54 ms. Compile time 7.77 ms, link time 3.73 ms.
[2025.06.04-20.12.47:187][  0]LogD3D12RHI: Creating RTPSO with 197 shaders (0 cached, 197 new) took 56.18 ms. Compile time 48.87 ms, link time 7.13 ms.
[2025.06.04-20.12.47:247][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.12.47:270][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.06.04-20.12.47:270][  0]LogFab: Display: Logging in using exchange code
[2025.06.04-20.12.47:270][  0]LogFab: Display: Reading exchange code from commandline
[2025.06.04-20.12.47:270][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.06.04-20.12.47:271][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... started...
[2025.06.04-20.12.47:271][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... took 199 us
[2025.06.04-20.12.47:271][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.04-20.12.47:327][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.04-20.12.47:338][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 66.855 ms (total: 67.054 ms)
[2025.06.04-20.12.47:338][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.04-20.12.47:338][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.04-20.12.47:338][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.04-20.12.47:338][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_Glossy was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Glossy has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.04-20.12.47:338][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/material which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.04-20.12.47:338][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/material was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/material has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/material.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.04-20.12.47:338][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.04-20.12.47:338][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.04-20.12.47:338][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.04-20.12.47:338][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_Dark was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Dark has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.04-20.12.47:338][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.04-20.12.47:338][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVfront, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.04-20.12.47:339][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.04-20.12.47:339][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVback, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.04-20.12.47:339][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.04-20.12.47:339][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Washingmachine/steel, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.04-20.12.47:339][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BaoliEditor Win64 Development
[2025.06.04-20.12.47:520][  1]LogAssetRegistry: AssetRegistryGather time 0.1655s: AssetDataDiscovery 0.0251s, AssetDataGather 0.0286s, StoreResults 0.1119s. Wall time 36.4710s.
	NumCachedDirectories 0. NumUncachedDirectories 3058. NumCachedFiles 16166. NumUncachedFiles 5.
	BackgroundTickInterruptions 92.
[2025.06.04-20.12.47:583][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.06.04-20.12.47:588][  1]LogCollectionManager: Fixed up redirectors for 1 collections in 0.000026 seconds (updated 1 objects)
[2025.06.04-20.12.47:742][  1]LogMaterial: Display: Material /InterchangeAssets/gltf/M_Default.M_Default needed to have new flag set bUsedWithNanite !
[2025.06.04-20.12.47:797][  1]MapCheck: Warning: M_Default Material /InterchangeAssets/gltf/M_Default.M_Default was missing the usage flag bUsedWithNanite. If the material asset is not re-saved, it may not render correctly when run outside the editor. Fix
[2025.06.04-20.12.47:908][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.06.04-20.12.48:087][  2]LogSourceControl: Uncontrolled asset enumeration finished in 0.502932 seconds (Found 7981 uncontrolled assets)
[2025.06.04-20.12.48:411][  3]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 30.456406
[2025.06.04-20.12.48:413][  3]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.04-20.12.48:414][  3]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 30.576115
[2025.06.04-20.12.48:738][ 31]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.04-20.12.48:964][ 55]LogPoseSearch: 50dc2d6fc66385f69c3f4316520b5453bbe11082 - PSD_Sparse_Stand_Walk_Stops BeginCache
[2025.06.04-20.12.48:964][ 55]LogPoseSearch: 50dc2d6fc66385f69c3f4316520b5453bbe11082 - PSD_Sparse_Stand_Walk_Stops BuildIndex From Cache
[2025.06.04-20.12.48:965][ 55]LogPoseSearch: 7bd24e04e6106f1973227ab2f5dd916a0214b232 - PSD_Sparse_Stand_Walk_Starts BeginCache
[2025.06.04-20.12.48:966][ 55]LogPoseSearch: 7bd24e04e6106f1973227ab2f5dd916a0214b232 - PSD_Sparse_Stand_Walk_Starts BuildIndex From Cache
[2025.06.04-20.12.48:967][ 55]LogPoseSearch: 6c818e5f72e0650bcaac1c26c2a2af0097681f03 - PSD_Sparse_Stand_Walk_Pivots BeginCache
[2025.06.04-20.12.48:968][ 55]LogPoseSearch: 6c818e5f72e0650bcaac1c26c2a2af0097681f03 - PSD_Sparse_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.04-20.12.48:968][ 55]LogPoseSearch: a53f5d87ef31e2efb2cffa95e177eb2de30ea68b - PSD_Sparse_Stand_Walk_Loops BeginCache
[2025.06.04-20.12.48:968][ 55]LogPoseSearch: a53f5d87ef31e2efb2cffa95e177eb2de30ea68b - PSD_Sparse_Stand_Walk_Loops BuildIndex From Cache
[2025.06.04-20.12.48:969][ 55]LogPoseSearch: 64befbf5d961ed5ac476d534110da8895e69df96 - PSD_Sparse_Stand_Run_Stops BeginCache
[2025.06.04-20.12.48:970][ 55]LogPoseSearch: 64befbf5d961ed5ac476d534110da8895e69df96 - PSD_Sparse_Stand_Run_Stops BuildIndex From Cache
[2025.06.04-20.12.48:970][ 55]LogPoseSearch: 2a285cc36d3969680553c96c4cc33908d8d326c8 - PSD_Sparse_Stand_Run_Starts BeginCache
[2025.06.04-20.12.48:971][ 55]LogPoseSearch: 2a285cc36d3969680553c96c4cc33908d8d326c8 - PSD_Sparse_Stand_Run_Starts BuildIndex From Cache
[2025.06.04-20.12.48:971][ 55]LogPoseSearch: df8620db2cd0e21552cf91b1d3cd5aa199f8e4ba - PSD_Sparse_Stand_Run_Pivots BeginCache
[2025.06.04-20.12.48:971][ 55]LogPoseSearch: df8620db2cd0e21552cf91b1d3cd5aa199f8e4ba - PSD_Sparse_Stand_Run_Pivots BuildIndex From Cache
[2025.06.04-20.12.48:973][ 55]LogPoseSearch: 20bcf12fcf57878594cfd98261e8196a850d3275 - PSD_Sparse_Stand_Run_Loops BeginCache
[2025.06.04-20.12.48:973][ 55]LogPoseSearch: 20bcf12fcf57878594cfd98261e8196a850d3275 - PSD_Sparse_Stand_Run_Loops BuildIndex From Cache
[2025.06.04-20.12.48:974][ 55]LogPoseSearch: 093bf032f877970de263de3430d86e0291f0b843 - PSD_Dense_Stand_Walk_Stops BeginCache
[2025.06.04-20.12.48:974][ 55]LogPoseSearch: 093bf032f877970de263de3430d86e0291f0b843 - PSD_Dense_Stand_Walk_Stops BuildIndex From Cache
[2025.06.04-20.12.48:974][ 55]LogPoseSearch: 58635c6253bdbf281b54ac2883681f632df4ac5a - PSD_Dense_Stand_TurnInPlace BeginCache
[2025.06.04-20.12.48:974][ 55]LogPoseSearch: 58635c6253bdbf281b54ac2883681f632df4ac5a - PSD_Dense_Stand_TurnInPlace BuildIndex From Cache
[2025.06.04-20.12.48:975][ 55]LogPoseSearch: aa8f1551ead5ebb7649a9c0cf3f329785019c495 - PSD_Dense_Stand_Run_FromTraversal BeginCache
[2025.06.04-20.12.48:975][ 55]LogPoseSearch: aa8f1551ead5ebb7649a9c0cf3f329785019c495 - PSD_Dense_Stand_Run_FromTraversal BuildIndex From Cache
[2025.06.04-20.12.48:976][ 55]LogPoseSearch: 3c18bdf4eafddb521f27fde8b603d69f34423c6e - PSD_Dense_Stand_Walk_Starts BeginCache
[2025.06.04-20.12.48:977][ 55]LogPoseSearch: 3c18bdf4eafddb521f27fde8b603d69f34423c6e - PSD_Dense_Stand_Walk_Starts BuildIndex From Cache
[2025.06.04-20.12.48:977][ 55]LogPoseSearch: 75620188eb8a82eb61257a3e37062d404180d1eb - PSD_Dense_Stand_Walk_Pivots BeginCache
[2025.06.04-20.12.48:978][ 55]LogPoseSearch: 75620188eb8a82eb61257a3e37062d404180d1eb - PSD_Dense_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.04-20.12.48:978][ 55]LogPoseSearch: ba3a46564c5498cbb68507c1ccbbd585a3fc84d4 - PSD_Dense_Stand_Walk_Loops BeginCache
[2025.06.04-20.12.48:979][ 55]LogPoseSearch: ba3a46564c5498cbb68507c1ccbbd585a3fc84d4 - PSD_Dense_Stand_Walk_Loops BuildIndex From Cache
[2025.06.04-20.12.48:979][ 55]LogPoseSearch: 54f0f90cd23005d37fa1e725d1b27c86bd11c9b3 - PSD_Dense_Stand_Walk_Lands_Light BeginCache
[2025.06.04-20.12.48:980][ 55]LogPoseSearch: 54f0f90cd23005d37fa1e725d1b27c86bd11c9b3 - PSD_Dense_Stand_Walk_Lands_Light BuildIndex From Cache
[2025.06.04-20.12.48:980][ 55]LogPoseSearch: d9aafe8d3e39205b5f2061763c086eddbfb05fac - PSD_Dense_Stand_Run_Stops BeginCache
[2025.06.04-20.12.48:981][ 55]LogPoseSearch: d9aafe8d3e39205b5f2061763c086eddbfb05fac - PSD_Dense_Stand_Run_Stops BuildIndex From Cache
[2025.06.04-20.12.48:981][ 55]LogPoseSearch: d8d5fe007588aee9a3da79e9f1a75f582c1b9f99 - PSD_Dense_Stand_Run_Starts BeginCache
[2025.06.04-20.12.48:982][ 55]LogPoseSearch: d8d5fe007588aee9a3da79e9f1a75f582c1b9f99 - PSD_Dense_Stand_Run_Starts BuildIndex From Cache
[2025.06.04-20.12.48:982][ 55]LogPoseSearch: b53a49cb986c56c8a7f2626bb2d2b7429fbf1afb - PSD_Dense_Stand_Run_Pivots BeginCache
[2025.06.04-20.12.48:983][ 55]LogPoseSearch: 570ea0e9de87396e5797a333746ddd45125a84d2 - PSD_Dense_Stand_Run_Loops BeginCache
[2025.06.04-20.12.48:983][ 55]LogPoseSearch: 570ea0e9de87396e5797a333746ddd45125a84d2 - PSD_Dense_Stand_Run_Loops BuildIndex From Cache
[2025.06.04-20.12.48:983][ 55]LogPoseSearch: 360967ed92817bbce2fc65a7f0c26e89ee4b6373 - PSD_Dense_Stand_Run_Lands_Light BeginCache
[2025.06.04-20.12.48:983][ 55]LogPoseSearch: b53a49cb986c56c8a7f2626bb2d2b7429fbf1afb - PSD_Dense_Stand_Run_Pivots BuildIndex From Cache
[2025.06.04-20.12.48:984][ 55]LogPoseSearch: 360967ed92817bbce2fc65a7f0c26e89ee4b6373 - PSD_Dense_Stand_Run_Lands_Light BuildIndex From Cache
[2025.06.04-20.12.48:985][ 55]LogPoseSearch: 3faefe98948c1fcc929efd51f12856b3b9f3b947 - PSD_Dense_Stand_Idles BeginCache
[2025.06.04-20.12.48:985][ 55]LogPoseSearch: 3faefe98948c1fcc929efd51f12856b3b9f3b947 - PSD_Dense_Stand_Idles BuildIndex From Cache
[2025.06.04-20.12.48:986][ 55]LogPoseSearch: 914c12febe09d1d5c60f3a968f6f99ac6ee0695d - PSD_Dense_Stand_Idle_Lands_Light BeginCache
[2025.06.04-20.12.48:986][ 55]LogPoseSearch: 914c12febe09d1d5c60f3a968f6f99ac6ee0695d - PSD_Dense_Stand_Idle_Lands_Light BuildIndex From Cache
[2025.06.04-20.12.49:232][ 73]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 31.372021
[2025.06.04-20.12.49:235][ 73]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 348165457
[2025.06.04-20.12.49:235][ 73]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 31.372021, Update Interval: 333.317047
[2025.06.04-20.12.59:498][967]LogAssetEditorSubsystem: Opening Asset editor for AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter
[2025.06.04-20.12.59:499][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.04-20.12.59:536][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.04-20.12.59:543][967]LogStreaming: Display: FlushAsyncLoading(356): 1 QueuedPackages, 0 AsyncPackages
[2025.06.04-20.12.59:563][967]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.12.59:564][967]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.12.59:564][967]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.12.59:586][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.02:662][967]LogBlueprintEditor: Perf: 3.1 total seconds to load all 13 blueprint libraries in project. Avoid references to content in blueprint libraries to shorten this time.
[2025.06.04-20.13.02:662][967]LogBlueprintEditor: Perf: 3.0 seconds loading: /Game/UltraDynamicSky/Blueprints/Functions/UltraDynamicWeather_Functions
[2025.06.04-20.13.03:725][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.04-20.13.03:761][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.03:894][967]LogAssetEditorSubsystem: Opening Asset editor for ControlRigBlueprint /Game/Characters/BaoliMC/CR_HeadManager.CR_HeadManager
[2025.06.04-20.13.03:894][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.04-20.13.03:921][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.03:955][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.04-20.13.03:965][967]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_4:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.13.03:965][967]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_4:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.13.04:226][967]LogUObjectHash: Compacting FUObjectHashTables data took   2.64ms
[2025.06.04-20.13.04:476][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.04:511][967]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter
[2025.06.04-20.13.04:512][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.04-20.13.04:528][967]LogTemp: Display: rdBPTools: Failed to load rdBPTools config ini file
[2025.06.04-20.13.04:528][967]LogTemp: Display: Handle AssetOpenedInEditor - BlueprintEditor...
[2025.06.04-20.13.04:532][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.04:608][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.04:644][967]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/Characters/Animations/CasRetarget/Cas_Skeleton.Cas_Skeleton
[2025.06.04-20.13.04:662][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.06.04-20.13.04:700][967]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_6:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.13.04:752][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.04:962][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.06.04-20.13.04:994][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.05:038][967]LogAssetEditorSubsystem: Opening Asset editor for BlendSpace /Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.BS_Neutral_AO_Stand
[2025.06.04-20.13.05:038][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.06.04-20.13.05:054][967]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.13.05:117][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.05:318][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.06.04-20.13.05:355][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.05:400][967]LogAssetEditorSubsystem: Opening Asset editor for ChooserTable /Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Dense.CHT_PoseSearchDatabases_Dense
[2025.06.04-20.13.05:419][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.05:427][967]LogAssetEditorSubsystem: Opening Asset editor for InputMappingContext /Game/Input/IMC_GDCMotionMatching.IMC_GDCMotionMatching
[2025.06.04-20.13.05:443][967]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.05:843][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.06.04-20.13.05:886][967]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.05:886][967]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.05:936][967]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.06:361][967]LogD3D12RHI: Creating RTPSO with 228 shaders (224 cached, 4 new) took 445.34 ms. Compile time 5.52 ms, link time 439.77 ms.
[2025.06.04-20.13.06:460][968]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.06:489][968]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.06:552][969]LogD3D12RHI: Creating RTPSO with 229 shaders (0 cached, 1 new) took 64.91 ms. Compile time 1.26 ms, link time 63.61 ms.
[2025.06.04-20.13.07:000][969]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.06.04-20.13.07:010][969]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.07:010][969]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.07:010][969]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.07:010][969]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.07:010][969]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.07:010][969]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.07:010][969]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.07:010][969]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.07:010][969]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.07:011][969]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.07:011][969]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.07:011][969]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.07:011][969]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.07:011][969]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.07:011][969]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.07:011][969]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.07:076][970]LogD3D12RHI: Creating RTPSO with 230 shaders (0 cached, 1 new) took 64.22 ms. Compile time 1.18 ms, link time 63.01 ms.
[2025.06.04-20.13.07:086][970]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.06.04-20.13.07:096][970]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.07:096][970]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.07:097][970]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.07:102][970]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.07:165][970]LogD3D12RHI: Creating RTPSO with 232 shaders (0 cached, 2 new) took 63.27 ms. Compile time 1.20 ms, link time 62.03 ms.
[2025.06.04-20.13.07:183][971]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.06.04-20.13.07:190][971]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.07:195][971]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.07:202][972]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.07:207][972]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.07:208][972]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.07:213][973]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.07:219][973]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.07:225][974]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.07:230][974]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.07:237][975]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.07:241][975]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.07:242][975]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.07:242][975]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.07:242][975]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.07:242][975]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.07:249][976]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.07:254][976]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.07:261][977]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.07:265][977]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.07:273][978]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.07:277][978]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.07:284][979]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_1:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.14:693][407]LogSlate: Took 0.000155 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.04-20.13.14:767][407]LogD3D12RHI: Creating RTPSO with 24 shaders (24 cached, 0 new) took 68.68 ms. Compile time 0.01 ms, link time 68.65 ms.
[2025.06.04-20.13.14:770][407]LogD3D12RHI: Creating RTPSO with 233 shaders (0 cached, 1 new) took 71.80 ms. Compile time 3.11 ms, link time 68.63 ms.
[2025.06.04-20.13.15:059][412]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.15:060][412]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.22:914][773]LogSlate: Took 0.000123 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.04-20.13.31:997][279]LogSlate: Took 0.000145 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.04-20.13.32:122][287]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.32:122][287]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.32:171][287]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.06.04-20.13.32:180][287]LogActor: Warning: BP_Almirah_C /Engine/Transient.World_15:PersistentLevel.BP_Almirah_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.04-20.13.32:225][288]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.06.04-20.13.32:283][289]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.06.04-20.13.32:291][289]LogActor: Warning: BP_Bed_C /Engine/Transient.World_17:PersistentLevel.BP_Bed_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.04-20.13.32:324][290]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.06.04-20.13.32:369][291]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.06.04-20.13.32:445][293]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.32:446][293]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.32:545][295]LogD3D12RHI: Creating RTPSO with 234 shaders (0 cached, 1 new) took 64.68 ms. Compile time 1.14 ms, link time 63.50 ms.
[2025.06.04-20.13.32:689][299]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.06.04-20.13.32:822][303]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.32:823][303]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.42:775][141]LogSlate: Window 'CHT_PoseSearchDatabases_Dense' being destroyed
[2025.06.04-20.13.42:778][141]LogSlate: Window 'CHT_PoseSearchDatabases_Dense' being destroyed
[2025.06.04-20.13.42:787][141]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.13.42:787][141]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.13.42:798][141]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.04-20.13.42:807][141]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.13.42:807][141]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.13.42:821][141]LogSlate: Window 'Baoli - Unreal Editor' being destroyed
[2025.06.04-20.13.42:836][141]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.13.42:836][141]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.13.42:850][141]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.13.42:850][141]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.13.42:866][141]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.04-20.13.42:875][141]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.13.42:875][141]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.13.42:895][141]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.13.42:895][141]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.13.42:907][141]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.13.42:907][141]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.13.42:932][141]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.04-20.13.42:945][141]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.13.42:945][141]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.13.42:979][141]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.13.42:979][141]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.13.42:989][141]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.04-20.13.42:999][141]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.13.42:999][141]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.13.43:224][141]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.13.43:273][141]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.13.43:321][141]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.13.43:370][141]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.13.43:413][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.43:457][141]LogAssetEditorSubsystem: Opening Asset editor for AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter
[2025.06.04-20.13.43:457][141]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.06.04-20.13.43:465][141]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_22
[2025.06.04-20.13.43:473][141]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_22:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.13.43:489][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.43:754][141]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_23
[2025.06.04-20.13.43:787][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.43:915][141]LogAssetEditorSubsystem: Opening Asset editor for ControlRigBlueprint /Game/Characters/BaoliMC/CR_HeadManager.CR_HeadManager
[2025.06.04-20.13.43:915][141]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_24
[2025.06.04-20.13.43:946][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.43:965][141]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_25
[2025.06.04-20.13.43:981][141]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_25:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.13.43:981][141]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_25:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.13.44:218][141]LogUObjectHash: Compacting FUObjectHashTables data took   1.98ms
[2025.06.04-20.13.44:489][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.44:514][141]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter
[2025.06.04-20.13.44:514][141]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_26
[2025.06.04-20.13.44:531][141]LogConfig: Branch 'rdBPToolsConfig' had been unloaded. Reloading on-demand took 0.66ms
[2025.06.04-20.13.44:531][141]LogTemp: Display: Handle AssetOpenedInEditor - BlueprintEditor...
[2025.06.04-20.13.44:535][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.44:600][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.44:637][141]LogAssetEditorSubsystem: Opening Asset editor for Skeleton /Game/Characters/Animations/CasRetarget/Cas_Skeleton.Cas_Skeleton
[2025.06.04-20.13.44:637][141]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_27
[2025.06.04-20.13.44:650][141]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_27:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.13.44:680][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.44:889][141]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_28
[2025.06.04-20.13.44:915][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.44:962][141]LogAssetEditorSubsystem: Opening Asset editor for BlendSpace /Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.BS_Neutral_AO_Stand
[2025.06.04-20.13.44:963][141]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_29
[2025.06.04-20.13.44:980][141]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_29:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.13.45:032][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.45:221][141]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_30
[2025.06.04-20.13.45:257][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.45:300][141]LogAssetEditorSubsystem: Opening Asset editor for ChooserTable /Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Dense.CHT_PoseSearchDatabases_Dense
[2025.06.04-20.13.45:318][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.45:326][141]LogAssetEditorSubsystem: Opening Asset editor for InputMappingContext /Game/Input/IMC_GDCMotionMatching.IMC_GDCMotionMatching
[2025.06.04-20.13.45:342][141]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.04-20.13.45:966][141]LogUObjectHash: Compacting FUObjectHashTables data took   1.73ms
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.45:991][141]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:050][142]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:063][143]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.46:064][143]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:072][143]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:085][144]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:095][145]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:096][145]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:107][146]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:119][147]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:130][148]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:130][148]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:130][148]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:130][148]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:131][148]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:142][149]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:154][150]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:165][151]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:165][151]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:165][151]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:166][151]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:177][152]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:177][152]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:177][152]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:178][152]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 01:38:30
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.04-20.13.46:190][153]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.04-20.13.52:868][507]LogStatusBar: Looking status bar with open content browser drawer...
[2025.06.04-20.13.52:868][507]LogStatusBar: StatusBar: GenericEditorApp_13 was content browser was not opened
[2025.06.04-20.13.52:868][507]LogStatusBar: StatusBar: ChooserEditorApp_12 was content browser was not opened
[2025.06.04-20.13.52:868][507]LogStatusBar: StatusBar: AnimationEditorApp_11 was content browser was not opened
[2025.06.04-20.13.52:868][507]LogStatusBar: StatusBar: SkeletonEditorApp_10 was content browser was not opened
[2025.06.04-20.13.52:868][507]LogStatusBar: StatusBar: BlueprintEditorApp_9 was content browser was not opened
[2025.06.04-20.13.52:868][507]LogStatusBar: StatusBar: ControlRigEditorApp_8 was content browser was not opened
[2025.06.04-20.13.52:868][507]LogStatusBar: StatusBar: AnimationBlueprintEditorApp_7 was content browser was not opened
[2025.06.04-20.13.52:868][507]LogStatusBar: Using status bar: LevelEditor.StatusBar to dock content browser
[2025.06.04-20.14.08:073][232]LogSlate: Took 0.000152 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.06.04-20.14.08:311][236]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.14.08:311][236]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.14.09:116][262]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X+135_Y+90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X+135_Y-90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X+135_Y0.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X+45_Y+90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X+45_Y-90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X+45_Y0.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X+90_Y+90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X+90_Y-90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X+90_Y0.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X-135_Y+90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X-135_Y-90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X-135_Y0.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X-45_Y+90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X-45_Y-90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X-45_Y0.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X-90_Y+90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X-90_Y-90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X-90_Y0.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X0_Y+90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X0_Y-90.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/M_Neutral_AO_Stand_X0_Y0.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/Crouch_WIP/M_Neutral_Crouch_Loop_B.uasset'
[2025.06.04-20.14.10:042][298]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.04-20.14.57:191][ 95]LogSlate: Took 0.000179 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.06.04-20.15.01:941][306]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.04-20.15.02:933][353]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.04-20.15.05:569][483]LogEditorTransaction: Undo Create Pin Link
[2025.06.04-20.15.06:071][483]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_22:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.15.06:081][483]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_27:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.15.06:103][483]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_29:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.04-20.15.11:821][751]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.04-20.15.12:817][798]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.04-20.15.14:080][847]LogUObjectHash: Compacting FUObjectHashTables data took   2.07ms
[2025.06.04-20.15.14:137][847]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_10
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.15.14:137][847]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_10
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.15.15:609][895]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.04-20.15.15:615][895]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.04-20.15.15:620][895]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.06.04-20.15.15:620][895]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.06.04-20.15.15:620][895]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.15.15:677][895]LogPlayLevel: PIE: StaticDuplicateObject took: (0.057216s)
[2025.06.04-20.15.15:677][895]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.057269s)
[2025.06.04-20.15.15:711][895]LogUObjectHash: Compacting FUObjectHashTables data took   1.78ms
[2025.06.04-20.15.15:718][895]r.RayTracing.Culling = "0"
[2025.06.04-20.15.15:718][895]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.04-20.15.15:718][895]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.04-20.15.15:719][895]LogPlayLevel: PIE: World Init took: (0.001629s)
[2025.06.04-20.15.15:720][895]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.06.04-20.15.15:720][895]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.04-20.15.15:721][895]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.04-20.15.15:721][895]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.04-20.15.15:721][895]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.04-20.15.15:721][895]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.04-20.15.15:721][895]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.04-20.15.15:721][895]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.04-20.15.15:721][895]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.04-20.15.15:721][895]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.04-20.15.15:721][895]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.04-20.15.15:721][895]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.04-20.15.15:723][895]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.04-20.15.15:757][895]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.04-20.15.15:757][895]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.04-20.15.15:757][895]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.04-20.15.15:757][895]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.04-20.15.15:759][895]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.06.04-20.15.15:759][895]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.06.04-20.15.15:760][895]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.06.04-20.15.15:760][895]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.06.04-20.15.15:760][895]LogInit: FAudioDevice initialized with ID 2.
[2025.06.04-20.15.15:760][895]LogAudio: Display: Audio Device (ID: 2) registered with world 'DefaultLevel'.
[2025.06.04-20.15.15:761][895]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.06.04-20.15.15:761][895]LogWindows: WindowsPlatformFeatures enabled
[2025.06.04-20.15.15:761][895]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.04-20.15.15:763][895]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.04-20.15.15:790][895]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.04-20.15.15:797][895]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-01.45.15
[2025.06.04-20.15.15:804][895]LogWorld: Bringing up level for play took: 0.039703
[2025.06.04-20.15.15:806][895]LogOnline: OSS: Created online subsystem instance for: :Context_32
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.04-20.15.15:822][895]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.04-20.15.15:834][895]PIE: Server logged in
[2025.06.04-20.15.15:835][895]PIE: Play in editor total start time 0.22 seconds.
[2025.06.04-20.15.16:106][895]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.04-20.15.16:221][897]LogD3D12RHI: Creating RTPSO with 243 shaders (0 cached, 12 new) took 83.45 ms. Compile time 5.47 ms, link time 77.89 ms.
[2025.06.04-20.15.16:602][902]LogD3D12RHI: Creating RTPSO with 258 shaders (23 cached, 0 new) took 62.14 ms. Compile time 0.03 ms, link time 62.06 ms.
[2025.06.04-20.15.16:638][902]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_11
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.15.16:639][902]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_11
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.15.16:802][906]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'UE.Wave Player.Stereo (v1.0)': Interface change detected.
[2025.06.04-20.15.16:802][906]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'Array.Random Get.WaveAsset:Array': Newer version 'v1.1' found.
[2025.06.04-20.15.16:956][911]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.04-20.15.17:150][918]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.04-20.15.17:353][925]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.04-20.15.17:562][932]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.04-20.15.17:769][939]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.04-20.15.17:932][945]PIE: Error: No database assets provided for motion matching.
[2025.06.04-20.15.18:683][968]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.15.18:684][968]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.15.18:886][974]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.04-20.15.33:064][476]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.15.33:064][476]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.15.33:068][476]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.04-20.15.33:068][476]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.04-20.15.33:068][476]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.15.33:071][476]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.15.33:075][476]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.04-20.15.33:087][476]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.15.33:120][476]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.04-20.15.33:144][476]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.06.04-20.15.33:144][476]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.04-20.15.33:146][476]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.04-20.15.33:150][476]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.15.33:157][476]LogUObjectHash: Compacting FUObjectHashTables data took   2.12ms
[2025.06.04-20.15.33:269][477]LogPlayLevel: Display: Destroying online subsystem :Context_32
[2025.06.04-20.16.02:802][518]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.16.03:781][549]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.16.04:333][569]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.04-20.16.04:349][569]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.04-20.16.04:350][569]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.16.04:408][569]LogPlayLevel: PIE: StaticDuplicateObject took: (0.058271s)
[2025.06.04-20.16.04:408][569]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.058335s)
[2025.06.04-20.16.04:440][569]LogUObjectHash: Compacting FUObjectHashTables data took   1.76ms
[2025.06.04-20.16.04:443][569]r.RayTracing.Culling = "0"
[2025.06.04-20.16.04:443][569]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.04-20.16.04:443][569]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.04-20.16.04:445][569]LogPlayLevel: PIE: World Init took: (0.001574s)
[2025.06.04-20.16.04:445][569]LogAudio: Display: Creating Audio Device:                 Id: 3, Scope: Unique, Realtime: True
[2025.06.04-20.16.04:445][569]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.04-20.16.04:445][569]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.04-20.16.04:445][569]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.04-20.16.04:445][569]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.04-20.16.04:445][569]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.04-20.16.04:445][569]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.04-20.16.04:445][569]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.04-20.16.04:445][569]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.04-20.16.04:445][569]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.04-20.16.04:445][569]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.04-20.16.04:445][569]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.04-20.16.04:448][569]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.04-20.16.04:482][569]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.04-20.16.04:483][569]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.04-20.16.04:483][569]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.04-20.16.04:483][569]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.04-20.16.04:484][569]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=3
[2025.06.04-20.16.04:484][569]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=3
[2025.06.04-20.16.04:486][569]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=3
[2025.06.04-20.16.04:486][569]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=3
[2025.06.04-20.16.04:486][569]LogInit: FAudioDevice initialized with ID 3.
[2025.06.04-20.16.04:486][569]LogAudio: Display: Audio Device (ID: 3) registered with world 'DefaultLevel'.
[2025.06.04-20.16.04:486][569]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 3
[2025.06.04-20.16.04:486][569]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.04-20.16.04:489][569]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.04-20.16.04:515][569]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.04-20.16.04:522][569]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-01.46.04
[2025.06.04-20.16.04:529][569]LogWorld: Bringing up level for play took: 0.039705
[2025.06.04-20.16.04:531][569]LogOnline: OSS: Created online subsystem instance for: :Context_33
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.04-20.16.04:545][569]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.04-20.16.04:557][569]PIE: Server logged in
[2025.06.04-20.16.04:558][569]PIE: Play in editor total start time 0.212 seconds.
[2025.06.04-20.16.04:620][570]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.04-20.16.06:099][617]PIE: Error: No database assets provided for motion matching.
[2025.06.04-20.16.13:324][873]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.16.13:324][873]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.16.13:328][873]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.04-20.16.13:328][873]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.04-20.16.13:328][873]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.16.13:331][873]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.16.13:335][873]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.04-20.16.13:346][873]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.16.13:400][873]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.16.13:402][873]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 3
[2025.06.04-20.16.13:403][873]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.04-20.16.13:405][873]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.04-20.16.13:415][873]LogUObjectHash: Compacting FUObjectHashTables data took   1.97ms
[2025.06.04-20.16.13:507][874]LogPlayLevel: Display: Destroying online subsystem :Context_33
[2025.06.04-20.16.17:705][  9]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.04-20.16.17:716][  9]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.04-20.16.17:716][  9]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.16.17:775][  9]LogPlayLevel: PIE: StaticDuplicateObject took: (0.058053s)
[2025.06.04-20.16.17:775][  9]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.058094s)
[2025.06.04-20.16.17:806][  9]LogUObjectHash: Compacting FUObjectHashTables data took   1.79ms
[2025.06.04-20.16.17:809][  9]r.RayTracing.Culling = "0"
[2025.06.04-20.16.17:809][  9]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.04-20.16.17:809][  9]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.04-20.16.17:810][  9]LogPlayLevel: PIE: World Init took: (0.001616s)
[2025.06.04-20.16.17:811][  9]LogAudio: Display: Creating Audio Device:                 Id: 4, Scope: Unique, Realtime: True
[2025.06.04-20.16.17:811][  9]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.04-20.16.17:811][  9]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.04-20.16.17:811][  9]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.04-20.16.17:811][  9]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.04-20.16.17:811][  9]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.04-20.16.17:811][  9]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.04-20.16.17:811][  9]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.04-20.16.17:811][  9]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.04-20.16.17:811][  9]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.04-20.16.17:811][  9]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.04-20.16.17:811][  9]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.04-20.16.17:814][  9]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.04-20.16.17:848][  9]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.04-20.16.17:849][  9]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.04-20.16.17:849][  9]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.04-20.16.17:849][  9]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.04-20.16.17:849][  9]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=4
[2025.06.04-20.16.17:849][  9]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=4
[2025.06.04-20.16.17:852][  9]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=4
[2025.06.04-20.16.17:852][  9]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=4
[2025.06.04-20.16.17:852][  9]LogInit: FAudioDevice initialized with ID 4.
[2025.06.04-20.16.17:852][  9]LogAudio: Display: Audio Device (ID: 4) registered with world 'DefaultLevel'.
[2025.06.04-20.16.17:852][  9]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 4
[2025.06.04-20.16.17:852][  9]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.04-20.16.17:855][  9]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.04-20.16.17:879][  9]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.04-20.16.17:887][  9]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-01.46.17
[2025.06.04-20.16.17:893][  9]LogWorld: Bringing up level for play took: 0.038318
[2025.06.04-20.16.17:896][  9]LogOnline: OSS: Created online subsystem instance for: :Context_34
[2025.06.04-20.16.17:910][  9]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.04-20.16.17:910][  9]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.04-20.16.17:910][  9]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.04-20.16.17:910][  9]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.04-20.16.17:910][  9]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.04-20.16.17:910][  9]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.04-20.16.17:911][  9]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.04-20.16.17:911][  9]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.04-20.16.17:911][  9]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.04-20.16.17:911][  9]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.04-20.16.17:911][  9]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.04-20.16.17:911][  9]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.04-20.16.17:911][  9]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.04-20.16.17:911][  9]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.04-20.16.17:911][  9]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.04-20.16.17:911][  9]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.04-20.16.17:922][  9]PIE: Server logged in
[2025.06.04-20.16.17:923][  9]PIE: Play in editor total start time 0.21 seconds.
[2025.06.04-20.16.17:985][ 10]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.04-20.16.20:776][106]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.16.20:784][106]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.16.21:571][135]PIE: Error: No database assets provided for motion matching.
[2025.06.04-20.16.33:804][560]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.16.33:804][560]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.16.33:807][560]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.04-20.16.33:807][560]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.04-20.16.33:808][560]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.16.33:810][560]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.16.33:815][560]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.04-20.16.33:826][560]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.16.33:881][560]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.16.33:883][560]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 4
[2025.06.04-20.16.33:884][560]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4
[2025.06.04-20.16.33:886][560]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4
[2025.06.04-20.16.33:897][560]LogUObjectHash: Compacting FUObjectHashTables data took   2.02ms
[2025.06.04-20.16.34:010][561]LogPlayLevel: Display: Destroying online subsystem :Context_34
[2025.06.04-20.16.43:775][891]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.16.43:797][891]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.04-20.16.43:881][891]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand] ([1] browsable assets)...
[2025.06.04-20.16.43:881][891]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_31
[2025.06.04-20.16.43:897][891]OBJ SavePackage:     Rendered thumbnail for [BlendSpace /Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.BS_Neutral_AO_Stand]
[2025.06.04-20.16.43:898][891]OBJ SavePackage: Finished generating thumbnails for package [/Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand]
[2025.06.04-20.16.43:901][891]LogSavePackage: Moving output files for package: /Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand
[2025.06.04-20.16.43:901][891]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/BS_Neutral_AO_StandC1D9675548D2E19244760AB5E6314EBA.tmp' to 'H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.16.43:912][891]LogFileHelpers: InternalPromptForCheckoutAndSave took 113.966 ms
[2025.06.04-20.16.43:992][891]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.04-20.16.43:992][891]LogContentValidation: Enabled validators:
[2025.06.04-20.16.43:992][891]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.04-20.16.43:992][891]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.04-20.16.43:992][891]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.04-20.16.43:992][891]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.04-20.16.43:992][891]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.04-20.16.43:992][891]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.04-20.16.43:992][891]LogContentValidation: 	/Script/GameFeaturesEditor.IllegalPluginDependenciesValidator
[2025.06.04-20.16.43:992][891]LogContentValidation: 	/Script/AssetReferenceRestrictions.EditorValidator_PluginAssetReferences
[2025.06.04-20.16.43:992][891]LogContentValidation: 	/Script/AssetReferenceRestrictions.AssetValidator_AssetReferenceRestrictions
[2025.06.04-20.16.43:993][891]AssetCheck: /Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand Validating asset
[2025.06.04-20.18.22:553][366]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 364.718506
[2025.06.04-20.18.23:074][393]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.04-20.18.23:074][393]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 365.220245, Update Interval: 313.127228
[2025.06.04-20.18.25:732][529]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.18.25:741][529]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.18.44:038][483]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.18.45:022][535]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.20.12:856][ 40]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.04-20.20.27:341][856]LogStreaming: Display: FlushAsyncLoading(561): 1 QueuedPackages, 0 AsyncPackages
[2025.06.04-20.21.56:935][632]LogUObjectHash: Compacting FUObjectHashTables data took   2.17ms
[2025.06.04-20.21.56:985][632]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_30
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.21.56:985][632]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_30
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.22.04:792][ 45]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.04-20.22.04:803][ 45]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.04-20.22.04:804][ 45]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.22.04:863][ 45]LogPlayLevel: PIE: StaticDuplicateObject took: (0.059091s)
[2025.06.04-20.22.04:863][ 45]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.059137s)
[2025.06.04-20.22.04:897][ 45]LogUObjectHash: Compacting FUObjectHashTables data took   1.92ms
[2025.06.04-20.22.04:908][ 45]r.RayTracing.Culling = "0"
[2025.06.04-20.22.04:908][ 45]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.04-20.22.04:908][ 45]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.04-20.22.04:909][ 45]LogPlayLevel: PIE: World Init took: (0.001614s)
[2025.06.04-20.22.04:910][ 45]LogAudio: Display: Creating Audio Device:                 Id: 5, Scope: Unique, Realtime: True
[2025.06.04-20.22.04:910][ 45]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.04-20.22.04:910][ 45]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.04-20.22.04:910][ 45]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.04-20.22.04:910][ 45]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.04-20.22.04:910][ 45]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.04-20.22.04:910][ 45]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.04-20.22.04:910][ 45]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.04-20.22.04:910][ 45]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.04-20.22.04:910][ 45]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.04-20.22.04:910][ 45]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.04-20.22.04:910][ 45]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.04-20.22.04:912][ 45]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.04-20.22.04:947][ 45]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.04-20.22.04:947][ 45]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.04-20.22.04:947][ 45]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.04-20.22.04:947][ 45]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.04-20.22.04:948][ 45]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=5
[2025.06.04-20.22.04:948][ 45]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=5
[2025.06.04-20.22.04:950][ 45]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=5
[2025.06.04-20.22.04:950][ 45]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=5
[2025.06.04-20.22.04:950][ 45]LogInit: FAudioDevice initialized with ID 5.
[2025.06.04-20.22.04:950][ 45]LogAudio: Display: Audio Device (ID: 5) registered with world 'DefaultLevel'.
[2025.06.04-20.22.04:950][ 45]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 5
[2025.06.04-20.22.04:950][ 45]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.04-20.22.04:953][ 45]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.04-20.22.04:980][ 45]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.04-20.22.04:988][ 45]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-01.52.04
[2025.06.04-20.22.04:994][ 45]LogWorld: Bringing up level for play took: 0.040520
[2025.06.04-20.22.04:996][ 45]LogOnline: OSS: Created online subsystem instance for: :Context_36
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.04-20.22.05:012][ 45]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.04-20.22.05:024][ 45]PIE: Server logged in
[2025.06.04-20.22.05:025][ 45]PIE: Play in editor total start time 0.224 seconds.
[2025.06.04-20.22.05:091][ 46]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.04-20.22.06:874][105]PIE: Error: No database assets provided for motion matching.
[2025.06.04-20.22.15:294][410]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.22.15:294][410]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.22.15:297][410]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.04-20.22.15:297][410]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.04-20.22.15:298][410]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.22.15:300][410]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.22.15:322][410]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.04-20.22.15:334][410]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.22.15:368][410]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.04-20.22.15:392][410]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 5
[2025.06.04-20.22.15:392][410]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5
[2025.06.04-20.22.15:394][410]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5
[2025.06.04-20.22.15:398][410]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.22.15:405][410]LogUObjectHash: Compacting FUObjectHashTables data took   2.22ms
[2025.06.04-20.22.15:527][411]LogPlayLevel: Display: Destroying online subsystem :Context_36
[2025.06.04-20.22.22:589][793]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.22.22:598][793]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.22.38:078][667]LogUObjectHash: Compacting FUObjectHashTables data took   1.94ms
[2025.06.04-20.22.38:104][667]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_33
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.22.38:104][667]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_33
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.22.39:354][715]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.04-20.22.39:364][715]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.04-20.22.39:364][715]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.22.39:423][715]LogPlayLevel: PIE: StaticDuplicateObject took: (0.058339s)
[2025.06.04-20.22.39:423][715]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.058382s)
[2025.06.04-20.22.39:457][715]LogUObjectHash: Compacting FUObjectHashTables data took   1.86ms
[2025.06.04-20.22.39:464][715]r.RayTracing.Culling = "0"
[2025.06.04-20.22.39:464][715]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.04-20.22.39:464][715]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.04-20.22.39:465][715]LogPlayLevel: PIE: World Init took: (0.001571s)
[2025.06.04-20.22.39:466][715]LogAudio: Display: Creating Audio Device:                 Id: 6, Scope: Unique, Realtime: True
[2025.06.04-20.22.39:466][715]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.04-20.22.39:466][715]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.04-20.22.39:466][715]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.04-20.22.39:466][715]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.04-20.22.39:466][715]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.04-20.22.39:466][715]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.04-20.22.39:466][715]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.04-20.22.39:466][715]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.04-20.22.39:466][715]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.04-20.22.39:466][715]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.04-20.22.39:466][715]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.04-20.22.39:468][715]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.04-20.22.39:504][715]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.04-20.22.39:504][715]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.04-20.22.39:504][715]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.04-20.22.39:504][715]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.04-20.22.39:504][715]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=6
[2025.06.04-20.22.39:504][715]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=6
[2025.06.04-20.22.39:507][715]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=6
[2025.06.04-20.22.39:507][715]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=6
[2025.06.04-20.22.39:507][715]LogInit: FAudioDevice initialized with ID 6.
[2025.06.04-20.22.39:507][715]LogAudio: Display: Audio Device (ID: 6) registered with world 'DefaultLevel'.
[2025.06.04-20.22.39:507][715]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 6
[2025.06.04-20.22.39:507][715]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.04-20.22.39:510][715]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.04-20.22.39:536][715]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.04-20.22.39:543][715]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-01.52.39
[2025.06.04-20.22.39:549][715]LogWorld: Bringing up level for play took: 0.039135
[2025.06.04-20.22.39:552][715]LogOnline: OSS: Created online subsystem instance for: :Context_37
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.04-20.22.39:567][715]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.04-20.22.39:578][715]PIE: Server logged in
[2025.06.04-20.22.39:579][715]PIE: Play in editor total start time 0.218 seconds.
[2025.06.04-20.22.39:645][716]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.04-20.22.41:173][764]PIE: Error: No database assets provided for motion matching.
[2025.06.04-20.22.45:953][938]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.22.45:954][938]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.22.45:957][938]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.04-20.22.45:957][938]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.04-20.22.45:957][938]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.22.45:959][938]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.22.45:964][938]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.04-20.22.45:974][938]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.22.46:008][938]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.04-20.22.46:032][938]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 6
[2025.06.04-20.22.46:032][938]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=6
[2025.06.04-20.22.46:034][938]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=6
[2025.06.04-20.22.46:038][938]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.22.46:045][938]LogUObjectHash: Compacting FUObjectHashTables data took   2.14ms
[2025.06.04-20.22.46:157][939]LogPlayLevel: Display: Destroying online subsystem :Context_37
[2025.06.04-20.22.48:888][ 66]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.22.48:898][ 66]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.22.53:234][291]LogUObjectHash: Compacting FUObjectHashTables data took   1.84ms
[2025.06.04-20.22.53:258][291]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_36
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.22.53:258][291]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_36
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.22.54:425][337]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.04-20.22.54:434][337]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.04-20.22.54:434][337]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.22.54:493][337]LogPlayLevel: PIE: StaticDuplicateObject took: (0.058446s)
[2025.06.04-20.22.54:493][337]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.058490s)
[2025.06.04-20.22.54:527][337]LogUObjectHash: Compacting FUObjectHashTables data took   1.79ms
[2025.06.04-20.22.54:533][337]r.RayTracing.Culling = "0"
[2025.06.04-20.22.54:533][337]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.04-20.22.54:533][337]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.04-20.22.54:534][337]LogPlayLevel: PIE: World Init took: (0.001571s)
[2025.06.04-20.22.54:535][337]LogAudio: Display: Creating Audio Device:                 Id: 7, Scope: Unique, Realtime: True
[2025.06.04-20.22.54:535][337]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.04-20.22.54:535][337]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.04-20.22.54:535][337]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.04-20.22.54:535][337]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.04-20.22.54:535][337]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.04-20.22.54:535][337]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.04-20.22.54:535][337]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.04-20.22.54:535][337]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.04-20.22.54:535][337]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.04-20.22.54:535][337]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.04-20.22.54:535][337]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.04-20.22.54:538][337]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.04-20.22.54:572][337]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.04-20.22.54:572][337]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.04-20.22.54:572][337]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.04-20.22.54:572][337]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.04-20.22.54:573][337]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=7
[2025.06.04-20.22.54:573][337]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=7
[2025.06.04-20.22.54:576][337]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=7
[2025.06.04-20.22.54:576][337]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=7
[2025.06.04-20.22.54:576][337]LogInit: FAudioDevice initialized with ID 7.
[2025.06.04-20.22.54:576][337]LogAudio: Display: Audio Device (ID: 7) registered with world 'DefaultLevel'.
[2025.06.04-20.22.54:576][337]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 7
[2025.06.04-20.22.54:576][337]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.04-20.22.54:578][337]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.04-20.22.54:604][337]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.04-20.22.54:612][337]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-01.52.54
[2025.06.04-20.22.54:619][337]LogWorld: Bringing up level for play took: 0.039918
[2025.06.04-20.22.54:621][337]LogOnline: OSS: Created online subsystem instance for: :Context_38
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.04-20.22.54:636][337]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.04-20.22.54:648][337]PIE: Server logged in
[2025.06.04-20.22.54:650][337]PIE: Play in editor total start time 0.219 seconds.
[2025.06.04-20.22.54:714][338]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.04-20.22.57:445][430]PIE: Error: No database assets provided for motion matching.
[2025.06.04-20.22.58:444][463]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.22.58:444][463]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.22.58:448][463]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.04-20.22.58:448][463]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.04-20.22.58:448][463]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.22.58:451][463]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.22.58:455][463]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.04-20.22.58:466][463]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.22.58:498][463]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.04-20.22.58:523][463]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 7
[2025.06.04-20.22.58:523][463]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=7
[2025.06.04-20.22.58:525][463]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=7
[2025.06.04-20.22.58:529][463]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.22.58:536][463]LogUObjectHash: Compacting FUObjectHashTables data took   2.10ms
[2025.06.04-20.22.58:652][464]LogPlayLevel: Display: Destroying online subsystem :Context_38
[2025.06.04-20.23.05:889][815]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.23.05:898][815]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.23.19:341][561]LogUObjectHash: Compacting FUObjectHashTables data took   1.85ms
[2025.06.04-20.23.19:367][561]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_39
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.23.19:367][561]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_39
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.23.20:503][601]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.04-20.23.20:513][601]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.04-20.23.20:513][601]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.23.20:571][601]LogPlayLevel: PIE: StaticDuplicateObject took: (0.057572s)
[2025.06.04-20.23.20:571][601]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.057617s)
[2025.06.04-20.23.20:606][601]LogUObjectHash: Compacting FUObjectHashTables data took   1.77ms
[2025.06.04-20.23.20:612][601]r.RayTracing.Culling = "0"
[2025.06.04-20.23.20:612][601]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.04-20.23.20:612][601]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.04-20.23.20:614][601]LogPlayLevel: PIE: World Init took: (0.001681s)
[2025.06.04-20.23.20:615][601]LogAudio: Display: Creating Audio Device:                 Id: 8, Scope: Unique, Realtime: True
[2025.06.04-20.23.20:615][601]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.04-20.23.20:615][601]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.04-20.23.20:615][601]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.04-20.23.20:615][601]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.04-20.23.20:615][601]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.04-20.23.20:615][601]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.04-20.23.20:615][601]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.04-20.23.20:615][601]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.04-20.23.20:615][601]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.04-20.23.20:615][601]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.04-20.23.20:615][601]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.04-20.23.20:616][601]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.04-20.23.20:652][601]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.04-20.23.20:652][601]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.04-20.23.20:652][601]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.04-20.23.20:652][601]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.04-20.23.20:653][601]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=8
[2025.06.04-20.23.20:653][601]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=8
[2025.06.04-20.23.20:655][601]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=8
[2025.06.04-20.23.20:655][601]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=8
[2025.06.04-20.23.20:655][601]LogInit: FAudioDevice initialized with ID 8.
[2025.06.04-20.23.20:655][601]LogAudio: Display: Audio Device (ID: 8) registered with world 'DefaultLevel'.
[2025.06.04-20.23.20:655][601]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 8
[2025.06.04-20.23.20:656][601]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.04-20.23.20:657][601]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.04-20.23.20:683][601]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.04-20.23.20:689][601]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-01.53.20
[2025.06.04-20.23.20:696][601]LogWorld: Bringing up level for play took: 0.038208
[2025.06.04-20.23.20:699][601]LogOnline: OSS: Created online subsystem instance for: :Context_39
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.04-20.23.20:713][601]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.04-20.23.20:724][601]PIE: Server logged in
[2025.06.04-20.23.20:726][601]PIE: Play in editor total start time 0.215 seconds.
[2025.06.04-20.23.20:791][602]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.04-20.23.23:789][704]PIE: Error: No database assets provided for motion matching.
[2025.06.04-20.23.27:124][824]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.23.27:124][824]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.23.27:128][824]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.04-20.23.27:128][824]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.04-20.23.27:129][824]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.23.27:130][824]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.23.27:140][824]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.04-20.23.27:151][824]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.23.27:184][824]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.04-20.23.27:209][824]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 8
[2025.06.04-20.23.27:209][824]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=8
[2025.06.04-20.23.27:211][824]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=8
[2025.06.04-20.23.27:214][824]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.23.27:221][824]LogUObjectHash: Compacting FUObjectHashTables data took   2.15ms
[2025.06.04-20.23.27:340][825]LogPlayLevel: Display: Destroying online subsystem :Context_39
[2025.06.04-20.23.31:073][  1]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_32
[2025.06.04-20.23.31:147][  3]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_33
[2025.06.04-20.23.32:964][105]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.23.32:972][105]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.23.38:943][454]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 681.110596
[2025.06.04-20.23.39:245][472]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.04-20.23.39:245][472]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 681.393677, Update Interval: 341.989197
[2025.06.04-20.23.42:883][675]LogUObjectHash: Compacting FUObjectHashTables data took   1.85ms
[2025.06.04-20.23.42:908][675]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_42
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.23.42:909][675]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_42
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.23.43:972][712]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.04-20.23.43:981][712]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.04-20.23.43:981][712]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.23.44:040][712]LogPlayLevel: PIE: StaticDuplicateObject took: (0.058378s)
[2025.06.04-20.23.44:040][712]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.058421s)
[2025.06.04-20.23.44:074][712]LogUObjectHash: Compacting FUObjectHashTables data took   1.76ms
[2025.06.04-20.23.44:080][712]r.RayTracing.Culling = "0"
[2025.06.04-20.23.44:080][712]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.04-20.23.44:080][712]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.04-20.23.44:082][712]LogPlayLevel: PIE: World Init took: (0.001655s)
[2025.06.04-20.23.44:083][712]LogAudio: Display: Creating Audio Device:                 Id: 9, Scope: Unique, Realtime: True
[2025.06.04-20.23.44:083][712]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.04-20.23.44:083][712]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.04-20.23.44:083][712]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.04-20.23.44:083][712]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.04-20.23.44:083][712]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.04-20.23.44:083][712]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.04-20.23.44:083][712]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.04-20.23.44:083][712]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.04-20.23.44:083][712]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.04-20.23.44:083][712]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.04-20.23.44:083][712]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.04-20.23.44:086][712]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.04-20.23.44:120][712]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.04-20.23.44:120][712]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.04-20.23.44:120][712]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.04-20.23.44:120][712]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.04-20.23.44:122][712]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=9
[2025.06.04-20.23.44:122][712]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=9
[2025.06.04-20.23.44:123][712]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=9
[2025.06.04-20.23.44:123][712]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=9
[2025.06.04-20.23.44:123][712]LogInit: FAudioDevice initialized with ID 9.
[2025.06.04-20.23.44:123][712]LogAudio: Display: Audio Device (ID: 9) registered with world 'DefaultLevel'.
[2025.06.04-20.23.44:123][712]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 9
[2025.06.04-20.23.44:123][712]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.04-20.23.44:126][712]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.04-20.23.44:153][712]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.04-20.23.44:161][712]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-01.53.44
[2025.06.04-20.23.44:167][712]LogWorld: Bringing up level for play took: 0.040649
[2025.06.04-20.23.44:169][712]LogOnline: OSS: Created online subsystem instance for: :Context_42
[2025.06.04-20.23.44:184][712]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.04-20.23.44:184][712]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.04-20.23.44:184][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.04-20.23.44:184][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.04-20.23.44:184][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.04-20.23.44:185][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.04-20.23.44:185][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.04-20.23.44:185][712]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.04-20.23.44:185][712]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.04-20.23.44:185][712]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.04-20.23.44:185][712]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.04-20.23.44:185][712]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.04-20.23.44:185][712]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.04-20.23.44:185][712]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.04-20.23.44:185][712]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.04-20.23.44:185][712]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.04-20.23.44:196][712]PIE: Server logged in
[2025.06.04-20.23.44:198][712]PIE: Play in editor total start time 0.219 seconds.
[2025.06.04-20.23.44:263][713]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.04-20.23.45:069][733]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.23.45:706][757]PIE: Error: No database assets provided for motion matching.
[2025.06.04-20.23.56:244][135]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.23.56:245][135]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.23.56:249][135]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.04-20.23.56:249][135]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.04-20.23.56:249][135]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.23.56:252][135]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.23.56:261][135]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.04-20.23.56:272][135]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.23.56:305][135]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.04-20.23.56:329][135]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 9
[2025.06.04-20.23.56:329][135]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=9
[2025.06.04-20.23.56:331][135]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=9
[2025.06.04-20.23.56:336][135]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.23.56:343][135]LogUObjectHash: Compacting FUObjectHashTables data took   2.09ms
[2025.06.04-20.23.56:455][136]LogPlayLevel: Display: Destroying online subsystem :Context_42
[2025.06.04-20.24.06:036][505]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.24.06:046][505]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.25.12:885][409]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.04-20.26.08:550][513]LogSelectionDetails: Warning: NavigateToFunctionSource:  Unable to find source file and line number for 'UKismetMathLibrary::FClamp' [Element not found.]
[2025.06.04-20.26.29:755][669]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.04-20.26.29:768][669]LogPlayLevel: [PlayLevel] Compiling ABP_SandboxCharacter before play...
[2025.06.04-20.26.29:969][669]LogUObjectHash: Compacting FUObjectHashTables data took   1.90ms
[2025.06.04-20.26.29:974][669]LogPlayLevel: PlayLevel: Blueprint regeneration took 209 ms (1 blueprints)
[2025.06.04-20.26.29:974][669]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.26.30:031][669]LogPlayLevel: PIE: StaticDuplicateObject took: (0.057220s)
[2025.06.04-20.26.30:031][669]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.057265s)
[2025.06.04-20.26.30:064][669]LogUObjectHash: Compacting FUObjectHashTables data took   1.77ms
[2025.06.04-20.26.30:066][669]r.RayTracing.Culling = "0"
[2025.06.04-20.26.30:067][669]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.04-20.26.30:067][669]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.04-20.26.30:068][669]LogPlayLevel: PIE: World Init took: (0.001622s)
[2025.06.04-20.26.30:068][669]LogAudio: Display: Creating Audio Device:                 Id: 10, Scope: Unique, Realtime: True
[2025.06.04-20.26.30:068][669]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.04-20.26.30:068][669]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.04-20.26.30:068][669]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.04-20.26.30:068][669]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.04-20.26.30:068][669]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.04-20.26.30:069][669]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.04-20.26.30:069][669]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.04-20.26.30:069][669]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.04-20.26.30:069][669]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.04-20.26.30:069][669]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.04-20.26.30:069][669]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.04-20.26.30:071][669]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.04-20.26.30:105][669]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.04-20.26.30:105][669]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.04-20.26.30:106][669]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.04-20.26.30:106][669]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.04-20.26.30:107][669]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=10
[2025.06.04-20.26.30:107][669]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=10
[2025.06.04-20.26.30:109][669]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=10
[2025.06.04-20.26.30:109][669]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=10
[2025.06.04-20.26.30:109][669]LogInit: FAudioDevice initialized with ID 10.
[2025.06.04-20.26.30:109][669]LogAudio: Display: Audio Device (ID: 10) registered with world 'DefaultLevel'.
[2025.06.04-20.26.30:109][669]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 10
[2025.06.04-20.26.30:109][669]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.04-20.26.30:112][669]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.04-20.26.30:140][669]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.04-20.26.30:147][669]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-01.56.30
[2025.06.04-20.26.30:154][669]LogWorld: Bringing up level for play took: 0.042140
[2025.06.04-20.26.30:156][669]LogOnline: OSS: Created online subsystem instance for: :Context_43
[2025.06.04-20.26.30:171][669]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.04-20.26.30:171][669]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.04-20.26.30:172][669]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.04-20.26.30:183][669]PIE: Server logged in
[2025.06.04-20.26.30:184][669]PIE: Play in editor total start time 0.423 seconds.
[2025.06.04-20.26.30:251][670]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.04-20.26.30:337][670]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_55
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.26.30:338][670]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_55
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.26.31:752][717]PIE: Error: No database assets provided for motion matching.
[2025.06.04-20.26.33:483][776]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.26.33:484][776]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.26.33:487][776]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.04-20.26.33:487][776]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.04-20.26.33:487][776]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.26.33:490][776]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.26.33:494][776]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.04-20.26.33:505][776]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.26.33:562][776]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.26.33:565][776]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 10
[2025.06.04-20.26.33:565][776]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=10
[2025.06.04-20.26.33:567][776]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=10
[2025.06.04-20.26.33:578][776]LogUObjectHash: Compacting FUObjectHashTables data took   2.13ms
[2025.06.04-20.26.33:698][777]LogPlayLevel: Display: Destroying online subsystem :Context_43
[2025.06.04-20.26.35:953][878]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.26.35:962][878]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.28.45:126][410]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.28.56:624][ 63]LogUObjectHash: Compacting FUObjectHashTables data took   1.77ms
[2025.06.04-20.28.56:630][ 63]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Levels/DefaultLevel' took 0.043
[2025.06.04-20.28.56:630][ 63]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.04-20.28.56:638][ 63]OBJ SavePackage:     Rendered thumbnail for [AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter]
[2025.06.04-20.28.56:638][ 63]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.04-20.28.56:638][ 63]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto1.uasset" SILENT=false AUTOSAVING=true
[2025.06.04-20.28.56:788][ 63]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto1
[2025.06.04-20.28.56:788][ 63]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter_Auto144D810144837225A561A55AC351C889A.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto1.uasset'
[2025.06.04-20.28.56:789][ 63]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand] ([1] browsable assets)...
[2025.06.04-20.28.56:795][ 63]OBJ SavePackage:     Rendered thumbnail for [BlendSpace /Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.BS_Neutral_AO_Stand]
[2025.06.04-20.28.56:795][ 63]OBJ SavePackage: Finished generating thumbnails for package [/Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand]
[2025.06.04-20.28.56:798][ 63]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand_Auto1
[2025.06.04-20.28.56:798][ 63]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/BS_Neutral_AO_Stand_Auto123ECA1494DF8F98BD41A55893F9E01C1.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand_Auto1.uasset'
[2025.06.04-20.28.56:799][ 63]LogFileHelpers: Auto-saving content packages took 0.168
[2025.06.04-20.28.57:828][119]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.04-20.29.28:069][842]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1030.238892
[2025.06.04-20.29.28:357][859]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.04-20.29.28:357][859]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1030.510010, Update Interval: 353.265167
[2025.06.04-20.31.06:544][514]LogUObjectHash: Compacting FUObjectHashTables data took   1.33ms
[2025.06.04-20.31.08:297][514]LogSlate: Window 'Save Content' being destroyed
[2025.06.04-20.31.08:320][514]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.31.08:337][514]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.04-20.31.08:376][514]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.04-20.31.08:377][514]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.04-20.31.08:377][514]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset" SILENT=true
[2025.06.04-20.31.08:506][514]LogSavePackage: Moving output files for package: /Game/Blueprints/ABP_SandboxCharacter
[2025.06.04-20.31.08:506][514]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter4E76A8B54DFBCCD7D1ED56A2B3190DC2.tmp' to 'H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.04-20.31.08:507][514]OBJ SavePackage: Generating thumbnails for [0] asset(s) in package [/Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand] ([1] browsable assets)...
[2025.06.04-20.31.08:507][514]OBJ SavePackage: Finished generating thumbnails for package [/Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand]
[2025.06.04-20.31.08:511][514]LogSavePackage: Moving output files for package: /Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand
[2025.06.04-20.31.08:511][514]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/BS_Neutral_AO_StandC2E240104B3849363E3054AA4F7F1217.tmp' to 'H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.31.08:520][514]LogFileHelpers: InternalPromptForCheckoutAndSave took 183.349 ms (total: 297.316 ms)
[2025.06.04-20.31.08:557][514]LogContentValidation: Display: Starting to validate 2 assets
[2025.06.04-20.31.08:557][514]LogContentValidation: Enabled validators:
[2025.06.04-20.31.08:557][514]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.04-20.31.08:557][514]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.04-20.31.08:557][514]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.04-20.31.08:557][514]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.04-20.31.08:557][514]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.04-20.31.08:557][514]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.04-20.31.08:557][514]LogContentValidation: 	/Script/GameFeaturesEditor.IllegalPluginDependenciesValidator
[2025.06.04-20.31.08:557][514]LogContentValidation: 	/Script/AssetReferenceRestrictions.EditorValidator_PluginAssetReferences
[2025.06.04-20.31.08:557][514]LogContentValidation: 	/Script/AssetReferenceRestrictions.AssetValidator_AssetReferenceRestrictions
[2025.06.04-20.31.08:557][514]AssetCheck: /Game/Blueprints/ABP_SandboxCharacter Validating asset
[2025.06.04-20.31.08:558][514]AssetCheck: /Game/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand Validating asset
[2025.06.04-20.31.39:747][283]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.31.40:747][343]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/UEFN_Mannequin/Animations/AimOffset/BS_Neutral_AO_Stand.uasset'
[2025.06.04-20.33.43:931][296]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_34
[2025.06.04-20.33.48:678][548]LogUObjectHash: Compacting FUObjectHashTables data took   1.86ms
[2025.06.04-20.33.48:704][548]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_64
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.33.48:705][548]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_64
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1311
[2025.06.04-20.33.50:033][600]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.04-20.33.50:043][600]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.04-20.33.50:043][600]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.33.50:102][600]LogPlayLevel: PIE: StaticDuplicateObject took: (0.059659s)
[2025.06.04-20.33.50:102][600]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.059705s)
[2025.06.04-20.33.50:136][600]LogUObjectHash: Compacting FUObjectHashTables data took   1.80ms
[2025.06.04-20.33.50:143][600]r.RayTracing.Culling = "0"
[2025.06.04-20.33.50:143][600]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.04-20.33.50:143][600]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.04-20.33.50:144][600]LogPlayLevel: PIE: World Init took: (0.001586s)
[2025.06.04-20.33.50:145][600]LogAudio: Display: Creating Audio Device:                 Id: 11, Scope: Unique, Realtime: True
[2025.06.04-20.33.50:145][600]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.04-20.33.50:145][600]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.04-20.33.50:145][600]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.04-20.33.50:145][600]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.04-20.33.50:145][600]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.04-20.33.50:145][600]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.04-20.33.50:145][600]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.04-20.33.50:145][600]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.04-20.33.50:145][600]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.04-20.33.50:145][600]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.04-20.33.50:145][600]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.04-20.33.50:147][600]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.04-20.33.50:183][600]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.04-20.33.50:183][600]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.04-20.33.50:183][600]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.04-20.33.50:183][600]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.04-20.33.50:184][600]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=11
[2025.06.04-20.33.50:184][600]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=11
[2025.06.04-20.33.50:186][600]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=11
[2025.06.04-20.33.50:186][600]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=11
[2025.06.04-20.33.50:186][600]LogInit: FAudioDevice initialized with ID 11.
[2025.06.04-20.33.50:186][600]LogAudio: Display: Audio Device (ID: 11) registered with world 'DefaultLevel'.
[2025.06.04-20.33.50:186][600]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 11
[2025.06.04-20.33.50:186][600]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.04-20.33.50:189][600]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.04-20.33.50:214][600]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.04-20.33.50:221][600]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-02.03.50
[2025.06.04-20.33.50:227][600]LogWorld: Bringing up level for play took: 0.038124
[2025.06.04-20.33.50:230][600]LogOnline: OSS: Created online subsystem instance for: :Context_45
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.04-20.33.50:245][600]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.04-20.33.50:257][600]PIE: Server logged in
[2025.06.04-20.33.50:258][600]PIE: Play in editor total start time 0.219 seconds.
[2025.06.04-20.33.50:323][601]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.04-20.33.51:799][648]PIE: Error: No database assets provided for motion matching.
[2025.06.04-20.34.03:454][ 66]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.34.03:454][ 66]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.04-20.34.03:458][ 66]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.04-20.34.03:458][ 66]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.04-20.34.03:459][ 66]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.34.03:460][ 66]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.34.03:473][ 66]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.04-20.34.03:484][ 66]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.34.03:517][ 66]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.04-20.34.03:541][ 66]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 11
[2025.06.04-20.34.03:541][ 66]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=11
[2025.06.04-20.34.03:543][ 66]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=11
[2025.06.04-20.34.03:547][ 66]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.04-20.34.03:553][ 66]LogUObjectHash: Compacting FUObjectHashTables data took   2.08ms
[2025.06.04-20.34.03:666][ 67]LogPlayLevel: Display: Destroying online subsystem :Context_45
[2025.06.04-20.35.21:551][820]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.35.21:589][820]LogSlate: Window 'Message Log' being destroyed
[2025.06.04-20.35.31:904][237]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1394.074097
[2025.06.04-20.35.32:194][254]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.04-20.35.32:194][254]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1394.347168, Update Interval: 356.570343
[2025.06.04-20.35.48:211][190]LogUObjectHash: Compacting FUObjectHashTables data took   1.75ms
[2025.06.04-20.35.50:095][190]LogSlate: Window 'Save Content' being destroyed
[2025.06.04-20.35.50:148][190]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.04-20.35.50:159][190]LogWorld: UWorld::CleanupWorld for World_30, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:159][190]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:169][190]LogWorld: UWorld::CleanupWorld for World_29, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:169][190]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:190][190]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.04-20.35.50:199][190]LogWorld: UWorld::CleanupWorld for World_28, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:199][190]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:208][190]LogWorld: UWorld::CleanupWorld for World_27, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:208][190]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:230][190]LogWorld: UWorld::CleanupWorld for World_26, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:231][190]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:287][190]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.04-20.35.50:301][190]LogWorld: UWorld::CleanupWorld for World_25, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:301][190]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:309][190]LogWorld: UWorld::CleanupWorld for World_24, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:309][190]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:340][190]LogSlate: Window 'CHT_PoseSearchDatabases_Dense' being destroyed
[2025.06.04-20.35.50:369][190]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.04-20.35.50:378][190]LogWorld: UWorld::CleanupWorld for World_23, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:378][190]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:395][190]LogWorld: UWorld::CleanupWorld for World_22, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:395][190]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:405][190]LogWorld: UWorld::CleanupWorld for World_21, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:405][190]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:437][190]LogSlate: Window 'Baoli - Unreal Editor' being destroyed
[2025.06.04-20.35.50:553][190]LogUObjectHash: Compacting FUObjectHashTables data took   1.77ms
[2025.06.04-20.35.50:587][190]Cmd: QUIT_EDITOR
[2025.06.04-20.35.50:587][191]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.06.04-20.35.50:590][191]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.06.04-20.35.50:591][191]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.06.04-20.35.50:591][191]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.06.04-20.35.50:596][191]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:596][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:601][191]LogStylusInput: Shutting down StylusInput subsystem.
[2025.06.04-20.35.50:601][191]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.06.04-20.35.50:606][191]LogWorld: UWorld::CleanupWorld for World_33, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:606][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:611][191]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:612][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:615][191]LogWorld: UWorld::CleanupWorld for World_19, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:615][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:619][191]LogWorld: UWorld::CleanupWorld for World_20, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:619][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:623][191]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:623][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:626][191]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:626][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:629][191]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:629][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:632][191]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:632][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:635][191]LogWorld: UWorld::CleanupWorld for World_15, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:635][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:639][191]LogWorld: UWorld::CleanupWorld for World_16, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:639][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:642][191]LogWorld: UWorld::CleanupWorld for World_17, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:642][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:644][191]LogWorld: UWorld::CleanupWorld for World_18, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:644][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:647][191]LogWorld: UWorld::CleanupWorld for World_34, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:647][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:650][191]LogWorld: UWorld::CleanupWorld for World_31, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:650][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:653][191]LogWorld: UWorld::CleanupWorld for World_32, bSessionEnded=true, bCleanupResources=true
[2025.06.04-20.35.50:653][191]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.04-20.35.50:656][191]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.06.04-20.35.50:658][191]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.06.04-20.35.50:658][191]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.06.04-20.35.50:658][191]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.06.04-20.35.51:460][191]LogGameFeatures: Shutting down game features subsystem
[2025.06.04-20.35.51:460][191]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.06.04-20.35.51:460][191]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.06.04-20.35.51:460][191]LogAudio: Display: Audio Device unregistered from world 'DefaultLevel'.
[2025.06.04-20.35.51:460][191]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.06.04-20.35.51:460][191]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.04-20.35.51:463][191]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.04-20.35.51:467][191]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.06.04-20.35.51:467][191]LogAudio: Display: Audio Device Manager Shutdown
[2025.06.04-20.35.51:469][191]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.06.04-20.35.51:470][191]LogExit: Preparing to exit.
[2025.06.04-20.35.51:526][191]LogUObjectHash: Compacting FUObjectHashTables data took   1.81ms
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_null_above_selected_PY.add_null_above_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_all' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_x' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_y' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_z' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_scale' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_name_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/set_bone_reference_pose_PY.set_bone_reference_pose' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/ControlRigWorkflows/workflow_fbik_import_ik_rig_PY.import_ik_rig_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.51:532][191]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.ControlOutputFormat' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.04-20.35.52:597][191]LogEditorDataStorage: Deinitializing
[2025.06.04-20.35.53:736][191]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
