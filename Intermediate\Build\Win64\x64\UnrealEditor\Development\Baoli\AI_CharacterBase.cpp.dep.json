{"Version": "1.2", "Data": {"Source": "h:\\p4\\dev\\baoli\\source\\baoli\\ai\\ai_characterbase.cpp", "ProvidedModule": "", "PCH": "h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\x64\\baolieditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\baoli\\definitions.baoli.h", "h:\\p4\\dev\\baoli\\source\\baoli\\ai\\ai_characterbase.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\character.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\charactermovementreplication.h", "d:\\ue_5.5\\engine\\source\\runtime\\net\\core\\public\\net\\core\\nettoken\\nettokenexportcontext.h", "d:\\ue_5.5\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\serialization\\irisobjectreferencepackagemap.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\iriscore\\uht\\irisobjectreferencepackagemap.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementreplication.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\rootmotionsource.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\rootmotionsource.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\character.generated.h", "h:\\p4\\dev\\baoli\\intermediate\\build\\win64\\unrealeditor\\inc\\baoli\\uht\\ai_characterbase.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystem.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdirtyelement.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationinvokerpriority.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationinvokerpriority.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystemtypes.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigationsystemconfig.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationsystemconfig.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationdata.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdatainterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationdatainterface.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationdata.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigationsystembase.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationsystembase.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationoctree.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationelement.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationoctreecontroller.h", "d:\\ue_5.5\\engine\\source\\runtime\\navigationsystem\\public\\navigationdirtyareascontroller.h", "d:\\ue_5.5\\engine\\source\\runtime\\core\\public\\math\\movingwindowaveragefast.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationbounds.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationsystem.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\charactermovementcomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationavoidancetypes.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationavoidancetypes.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\public\\ai\\rvoavoidanceinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\rvoavoidanceinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\pawnmovementcomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\navmovementcomponent.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\navmovementinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navmovementinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\pathfollowingagentinterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pathfollowingagentinterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\movementcomponent.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\movementcomponent.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navmovementcomponent.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pawnmovementcomponent.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\interfaces\\networkpredictioninterface.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\networkpredictioninterface.generated.h", "d:\\ue_5.5\\engine\\source\\runtime\\engine\\public\\charactermovementcomponentasync.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementcomponentasync.generated.h", "d:\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementcomponent.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}