// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared Definitions for UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3
#pragma once
#define IS_PROGRAM 0
#define UE_EDITOR 1
#define USE_SHADER_COMPILER_WORKER_TRACE 0
#define UE_REFERENCE_COLLECTOR_REQUIRE_OBJECTPTR 1
#define WITH_VERSE_VM 0
#define ENABLE_PGO_PROFILE 0
#define USE_VORBIS_FOR_STREAMING 1
#define USE_XMA2_FOR_STREAMING 1
#define WITH_DEV_AUTOMATION_TESTS 1
#define WITH_PERF_AUTOMATION_TESTS 1
#define WITH_LOW_LEVEL_TESTS 0
#define EXPLICIT_TESTS_TARGET 0
#define WITH_TESTS 1
#define UNICODE 1
#define _UNICODE 1
#define __UNREAL__ 1
#define IS_MONOLITHIC 0
#define IS_MERGEDMODULES 0
#define WITH_ENGINE 1
#define WITH_UNREAL_DEVELOPER_TOOLS 1
#define WITH_UNREAL_TARGET_DEVELOPER_TOOLS 1
#define WITH_APPLICATION_CORE 1
#define WITH_COREUOBJECT 1
#define UE_TRACE_ENABLED 1
#define UE_TRACE_FORCE_ENABLED 0
#define WITH_VERSE 1
#define UE_USE_VERSE_PATHS 1
#define WITH_VERSE_BPVM 1
#define USE_STATS_WITHOUT_ENGINE 0
#define WITH_PLUGIN_SUPPORT 0
#define WITH_ACCESSIBILITY 1
#define WITH_PERFCOUNTERS 1
#define WITH_FIXED_TIME_STEP_SUPPORT 1
#define USE_LOGGING_IN_SHIPPING 0
#define ALLOW_CONSOLE_IN_SHIPPING 0
#define ALLOW_PROFILEGPU_IN_TEST 0
#define ALLOW_PROFILEGPU_IN_SHIPPING 0
#define WITH_LOGGING_TO_MEMORY 0
#define USE_CACHE_FREED_OS_ALLOCS 1
#define USE_CHECKS_IN_SHIPPING 0
#define USE_UTF8_TCHARS 0
#define USE_ESTIMATED_UTCNOW 0
#define UE_ALLOW_EXEC_COMMANDS_IN_SHIPPING 1
#define WITH_EDITOR 1
#define WITH_IOSTORE_IN_EDITOR 1
#define WITH_CLIENT_CODE 1
#define WITH_SERVER_CODE 1
#define UE_FNAME_OUTLINE_NUMBER 0
#define WITH_PUSH_MODEL 1
#define WITH_CEF3 1
#define WITH_LIVE_CODING 1
#define WITH_CPP_MODULES 0
#define WITH_CPP_COROUTINES 0
#define WITH_PROCESS_PRIORITY_CONTROL 0
#define UBT_MODULE_MANIFEST "UnrealEditor.modules"
#define UBT_MODULE_MANIFEST_DEBUGGAME "UnrealEditor-Win64-DebugGame.modules"
#define UBT_COMPILED_PLATFORM Win64
#define UBT_COMPILED_TARGET Editor
#define UE_APP_NAME "UnrealEditor"
#define UE_WARNINGS_AS_ERRORS 0
#define NDIS_MINIPORT_MAJOR_VERSION 0
#define WIN32 1
#define _WIN32_WINNT 0x0601
#define WINVER 0x0601
#define PLATFORM_WINDOWS 1
#define PLATFORM_MICROSOFT 1
#define OVERRIDE_PLATFORM_HEADER_NAME Windows
#define RHI_RAYTRACING 1
#define WINDOWS_MAX_NUM_TLS_SLOTS 2048
#define WINDOWS_MAX_NUM_THREADS_WITH_TLS_SLOTS 512
#define NDEBUG 1
#define UE_BUILD_DEVELOPMENT 1
#define UE_VALIDATE_FORMAT_STRINGS 1
#define WITH_CLOTH_COLLISION_DETECTION 1
#define WITH_CHAOS_VISUAL_DEBUGGER 1
#define WITH_RECAST 1
#define WITH_NAVMESH_SEGMENT_LINKS 1
#define WITH_NAVMESH_CLUSTER_LINKS 1
#define UNREALED_API DLLIMPORT
#define ASSETDEFINITION_API DLLIMPORT
#define UE_MEMORY_TAGS_TRACE_ENABLED 1
#define UE_ENABLE_ICU 1
#define WITH_ADDITIONAL_CRASH_CONTEXTS 1
#define WITH_VS_PERF_PROFILER 1
#define WITH_CONCURRENCYVIEWER_PROFILER 0
#define WITH_DIRECTXMATH 0
#define UE_WITH_IRIS 1
#define PLATFORM_SUPPORTS_PLATFORM_EVENTS 1
#define PLATFORM_SUPPORTS_TRACE_WIN32_VIRTUAL_MEMORY_HOOKS 1
#define PLATFORM_SUPPORTS_TRACE_WIN32_MODULE_DIAGNOSTICS 1
#define PLATFORM_SUPPORTS_TRACE_WIN32_CALLSTACK 1
#define UE_MEMORY_TRACE_AVAILABLE 1
#define WITH_MALLOC_STOMP 1
#define UE_MERGED_MODULES 0
#define CORE_API DLLIMPORT
#define GSL_NO_IOSTREAMS 1
#define TRACELOG_API DLLIMPORT
#define WITH_VERSE_COMPILER 1
#define COREUOBJECT_API DLLIMPORT
#define COREPRECISEFP_API DLLIMPORT
#define ASSETREGISTRY_API DLLIMPORT
#define GPUPARTICLE_LOCAL_VF_ONLY 0
#define WITH_ODSC 0
#define ENGINE_API DLLIMPORT
#define PLATFORM_MAX_LOCAL_PLAYERS 0
#define COREONLINE_API DLLIMPORT
#define FIELDNOTIFICATION_API DLLIMPORT
#define NETCORE_API DLLIMPORT
#define NETCOMMON_API DLLIMPORT
#define IMAGECORE_API DLLIMPORT
#define JSON_API DLLIMPORT
#define JSONUTILITIES_API DLLIMPORT
#define WITH_FREETYPE 1
#define SLATECORE_API DLLIMPORT
#define DEVELOPERSETTINGS_API DLLIMPORT
#define INPUTCORE_API DLLIMPORT
#define UE_WINDOWS_USING_UIA 1
#define APPLICATIONCORE_API DLLIMPORT
#define RHI_NEW_GPU_PROFILER 0
#define WITH_MGPU 1
#define RHI_WANT_RESOURCE_INFO 1
#define RHI_API DLLIMPORT
#define SLATE_API DLLIMPORT
#define WITH_UNREALPNG 1
#define WITH_UNREALJPEG 1
#define WITH_LIBJPEGTURBO 1
#define WITH_UNREALEXR 1
#define WITH_UNREALEXR_MINIMAL 0
#define IMAGEWRAPPER_API DLLIMPORT
#define WITH_LIBTIFF 1
#define MESSAGING_API DLLIMPORT
#define MESSAGINGCOMMON_API DLLIMPORT
#define RENDERCORE_API DLLIMPORT
#define OPENGLDRV_API DLLIMPORT
#define ANALYTICSET_API DLLIMPORT
#define ANALYTICS_API DLLIMPORT
#define SOCKETS_PACKAGE 1
#define SOCKETS_API DLLIMPORT
#define ENGINEMESSAGES_API DLLIMPORT
#define ENGINESETTINGS_API DLLIMPORT
#define SYNTHBENCHMARK_API DLLIMPORT
#define GAMEPLAYTAGS_API DLLIMPORT
#define PACKETHANDLER_API DLLIMPORT
#define RELIABILITYHANDLERCOMPONENT_API DLLIMPORT
#define AUDIOPLATFORMCONFIGURATION_API DLLIMPORT
#define MESHDESCRIPTION_API DLLIMPORT
#define STATICMESHDESCRIPTION_API DLLIMPORT
#define SKELETALMESHDESCRIPTION_API DLLIMPORT
#define ANIMATIONCORE_API DLLIMPORT
#define PAKFILE_API DLLIMPORT
#define RSA_API DLLIMPORT
#define NETWORKREPLAYSTREAMING_API DLLIMPORT
#define PHYSICSCORE_API DLLIMPORT
#define COMPILE_WITHOUT_UNREAL_SUPPORT 0
#define CHAOS_CHECKED 0
#define CHAOS_DEBUG_NAME 1
#define CHAOSCORE_API DLLIMPORT
#define INTEL_ISPC 1
#define CHAOS_MEMORY_TRACKING 0
#define CHAOS_API DLLIMPORT
#define VORONOI_API DLLIMPORT
#define GEOMETRYCORE_API DLLIMPORT
#define CHAOSVDRUNTIME_API DLLIMPORT
#define SIGNALPROCESSING_API DLLIMPORT
#define AUDIOEXTENSIONS_API DLLIMPORT
#define AUDIOMIXERCORE_API DLLIMPORT
#define AUDIOMIXER_API DLLIMPORT
#define TARGETPLATFORM_API DLLIMPORT
#define TEXTUREFORMAT_API DLLIMPORT
#define DESKTOPPLATFORM_API DLLIMPORT
#define AUDIOLINKENGINE_API DLLIMPORT
#define AUDIOLINKCORE_API DLLIMPORT
#define COOKONTHEFLY_API DLLIMPORT
#define NETWORKING_API DLLIMPORT
#define TEXTUREBUILDUTILITIES_API DLLIMPORT
#define HORDE_API DLLIMPORT
#define CLOTHINGSYSTEMRUNTIMEINTERFACE_API DLLIMPORT
#define IRISCORE_API DLLIMPORT
#define MOVIESCENECAPTURE_API DLLIMPORT
#define RENDERER_API DLLIMPORT
#define TYPEDELEMENTFRAMEWORK_API DLLIMPORT
#define TYPEDELEMENTRUNTIME_API DLLIMPORT
#define ANIMATIONDATACONTROLLER_API DLLIMPORT
#define ANIMATIONBLUEPRINTEDITOR_API DLLIMPORT
#define KISMET_API DLLIMPORT
#define PERSONA_API DLLIMPORT
#define SKELETONEDITOR_API DLLIMPORT
#define ANIMATIONWIDGETS_API DLLIMPORT
#define TOOLWIDGETS_API DLLIMPORT
#define TOOLMENUS_API DLLIMPORT
#define ANIMATIONEDITOR_API DLLIMPORT
#define ADVANCEDPREVIEWSCENE_API DLLIMPORT
#define PROPERTYEDITOR_API DLLIMPORT
#define EDITORCONFIG_API DLLIMPORT
#define EDITORFRAMEWORK_API DLLIMPORT
#define EDITORSUBSYSTEM_API DLLIMPORT
#define INTERACTIVETOOLSFRAMEWORK_API DLLIMPORT
#define ACTORPICKERMODE_API DLLIMPORT
#define SCENEDEPTHPICKERMODE_API DLLIMPORT
#define ANIMATIONEDITMODE_API DLLIMPORT
#define INTERCHANGECORE_API DLLIMPORT
#define DIRECTORYWATCHER_API DLLIMPORT
#define DOCUMENTATION_API DLLIMPORT
#define MAINFRAME_API DLLIMPORT
#define READ_TARGET_ENABLED_PLUGINS_FROM_RECEIPT 1
#define LOAD_PLUGINS_FOR_TARGET_PLATFORMS 1
#define PROJECTS_API DLLIMPORT
#define SANDBOXFILE_API DLLIMPORT
#define SOURCE_CONTROL_WITH_SLATE 1
#define SOURCECONTROL_API DLLIMPORT
#define UNCONTROLLEDCHANGELISTS_API DLLIMPORT
#define UNREALEDMESSAGES_API DLLIMPORT
#define BLUEPRINTGRAPH_API DLLIMPORT
#define HTTP_PACKAGE 1
#define CURL_ENABLE_DEBUG_CALLBACK 1
#define CURL_ENABLE_NO_TIMEOUTS_OPTION 1
#define WITH_WINHTTP 1
#define UE_HTTP_CONNECTION_TIMEOUT_MAX_DEVIATION 0.5
#define UE_HTTP_CONNECTION_TIMEOUT_SUPPORT_RETRY 1
#define UE_HTTP_ACTIVITY_TIMER_START_AFTER_RECEIVED_DATA 0
#define UE_HTTP_SUPPORT_LOCAL_SERVER 1
#define UE_HTTP_SUPPORT_UNIX_SOCKET 1
#define HTTP_API DLLIMPORT
#define FUNCTIONALTESTING_API DLLIMPORT
#define AUTOMATIONCONTROLLER_API DLLIMPORT
#define AUTOMATIONTEST_API DLLIMPORT
#define LOCALIZATION_API DLLIMPORT
#define WITH_SNDFILE_IO 1
#define AUDIOEDITOR_API DLLIMPORT
#define LEVELEDITOR_API DLLIMPORT
#define COMMONMENUEXTENSIONS_API DLLIMPORT
#define SETTINGS_API DLLIMPORT
#define VREDITOR_API DLLIMPORT
#define VIEWPORTINTERACTION_API DLLIMPORT
#define HEADMOUNTEDDISPLAY_API DLLIMPORT
#define LANDSCAPE_API DLLIMPORT
#define DETAILCUSTOMIZATIONS_API DLLIMPORT
#define CLASSVIEWER_API DLLIMPORT
#define GRAPHEDITOR_API DLLIMPORT
#define STRUCTVIEWER_API DLLIMPORT
#define UE_CONTENTBROWSER_NEW_STYLE 0
#define CONTENTBROWSER_API DLLIMPORT
#define ASSETTOOLS_API DLLIMPORT
#define MERGE_API DLLIMPORT
#define COLLECTIONMANAGER_API DLLIMPORT
#define CONTENTBROWSERDATA_API DLLIMPORT
#define UELIBSAMPLERATE_API DLLIMPORT
#define NETWORKFILESYSTEM_API DLLIMPORT
#define UMG_API DLLIMPORT
#define MOVIESCENE_API DLLIMPORT
#define TIMEMANAGEMENT_API DLLIMPORT
#define UNIVERSALOBJECTLOCATOR_API DLLIMPORT
#define MOVIESCENETRACKS_API DLLIMPORT
#define CONSTRAINTS_API DLLIMPORT
#define PROPERTYPATH_API DLLIMPORT
#define NAVIGATIONSYSTEM_API DLLIMPORT
#define GEOMETRYCOLLECTIONENGINE_API DLLIMPORT
#define FIELDSYSTEMENGINE_API DLLIMPORT
#define CHAOSSOLVERENGINE_API DLLIMPORT
#define DATAFLOWCORE_API DLLIMPORT
#define DATAFLOWENGINE_API DLLIMPORT
#define DATAFLOWSIMULATION_API DLLIMPORT
#define MESHBUILDER_API DLLIMPORT
#define MESHUTILITIESCOMMON_API DLLIMPORT
#define MATERIALSHADERQUALITYSETTINGS_API DLLIMPORT
#define TOOLMENUSEDITOR_API DLLIMPORT
#define STATUSBAR_API DLLIMPORT
#define INTERCHANGEENGINE_API DLLIMPORT
#define DEVELOPERTOOLSETTINGS_API DLLIMPORT
#define SUBOBJECTDATAINTERFACE_API DLLIMPORT
#define SUBOBJECTEDITOR_API DLLIMPORT
#define PHYSICSUTILITIES_API DLLIMPORT
#define WIDGETREGISTRATION_API DLLIMPORT
#define GAMEPLAYTASKS_API DLLIMPORT
#define AUDIOMIXERXAUDIO2_API DLLIMPORT
#define ASSETTAGSEDITOR_API DLLIMPORT
#define MESHUTILITIES_API DLLIMPORT
#define MESHMERGEUTILITIES_API DLLIMPORT
#define MESHREDUCTIONINTERFACE_API DLLIMPORT
#define RAWMESH_API DLLIMPORT
#define MATERIALUTILITIES_API DLLIMPORT
#define KISMETCOMPILER_API DLLIMPORT
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 1
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_INTERNAL_API 1
