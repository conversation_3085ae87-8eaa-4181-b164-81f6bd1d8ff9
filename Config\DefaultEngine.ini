[/Script/EngineSettings.GameMapsSettings]
EditorStartupMap=/Game/Levels/DefaultLevel.DefaultLevel
GameDefaultMap=/Game/Levels/MainMenuLevel.MainMenuLevel
bUseSplitscreen=False
GlobalDefaultGameMode=/Game/Blueprints/GM_Sandbox.GM_Sandbox_C
GameInstanceClass=/Game/Blueprints/GameState/GI_Baoli.GI_Baoli_C

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
-D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM6
-D3D11TargetedShaderFormats=PCD3D_SM5
+D3D11TargetedShaderFormats=PCD3D_SM5
+VulkanTargetedShaderFormats=SF_VULKAN_SM6
Compiler=Default
AudioSampleRate=48000
AudioCallbackBufferFrameSize=1024
AudioNumBuffersToEnqueue=1
AudioMaxChannels=0
AudioNumSourceWorkers=4
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
CacheSizeKB=65536
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
MaxSampleRate=48000.000000
HighSampleRate=32000.000000
MedSampleRate=24000.000000
LowSampleRate=12000.000000
MinSampleRate=8000.000000
CompressionQualityModifier=1.000000
AutoStreamingThreshold=0.000000
SoundCueCookQualityIndex=-1

[/Script/Engine.RendererSettings]
r.Mobile.EnableNoPrecomputedLightingCSMShader=True
r.GenerateMeshDistanceFields=True
r.DynamicGlobalIlluminationMethod=1
r.ReflectionMethod=1
r.Shadow.Virtual.Enable=1
r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=True
r.DefaultFeature.LocalExposure.HighlightContrastScale=0.8
r.DefaultFeature.LocalExposure.ShadowContrastScale=0.8
r.GPUSkin.Support16BitBoneIndex=True
r.GPUSkin.UnlimitedBoneInfluences=True
r.SkinCache.CompileShaders=True
SkeletalMesh.UseExperimentalChunking=1
r.AllowGlobalClipPlane=False
r.CustomDepth=3
r.VirtualTextures=True
r.AntiAliasingMethod=4
r.ReflectionCaptureResolution=32
r.Lumen.TraceMeshSDFs=0
r.RayTracing=True
r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject=True
r.MegaLights.EnableForProject=True
r.PathTracing=False
r.RayTracing.Shadows=True
r.RayTracing.UseTextureLod=True
r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject=False
r.CustomDepthTemporalAAJitter=True
r.ScreenPercentage.Default=75.000000
r.ScreenPercentage.Default.Desktop.Mode=0
r.Shadow.CSMCaching=False
r.MSAACount=8
r.MeshPaintVirtualTexture.TileSize=32
r.MeshPaintVirtualTexture.TileBorderSize=2
r.MeshPaintVirtualTexture.MaxTextureSize=4096
r.MeshPaintVirtualTexture.UseCompression=True
r.VT.TileBorderSize=4
r.MeshPaintVirtualTexture.DefaultTexelsPerVertex=1
r.AllowStaticLighting=True
r.Shadow.UnbuiltPreviewInGame=True
r.NormalMapsForStaticLighting=True
r.Lumen.HardwareRayTracing.LightingMode=0
r.VirtualTexturedLightmaps=True
r.Lumen.HardwareRayTracing=True
r.Lumen.ScreenTracingSource=0
r.DefaultFeature.AutoExposure=False
r.DefaultFeature.AutoExposure.Bias=0.000000
r.DefaultFeature.AutoExposure.Method=2
r.vt.rvt.HighQualityPerPixelHeight=True

[/Script/LinuxTargetPlatform.LinuxTargetSettings]
-TargetedRHIs=SF_VULKAN_SM5
+TargetedRHIs=SF_VULKAN_SM6

[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/WorldPartitionEditor.WorldPartitionEditorSettings]
CommandletClass=Class'/Script/UnrealEd.WorldPartitionConvertCommandlet'

[/Script/Engine.UserInterfaceSettings]
bAuthorizeAutomaticWidgetVariableCreation=False
FontDPIPreset=Standard
FontDPI=72

[/Script/Engine.Engine]
+ActiveGameNameRedirects=(OldGameName="TP_BlankBP",NewGameName="/Script/GameAnimationSample")
+ActiveGameNameRedirects=(OldGameName="/Script/TP_BlankBP",NewGameName="/Script/GameAnimationSample")
NearClipPlane=0.100000
bUseFixedFrameRate=False
FixedFrameRate=60.000000
GameUserSettingsClassName=/Script/Baoli.BaoliGameUserSettings

[/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings]
bEnablePlugin=True
bAllowNetworkConnection=True
SecurityToken=D5A79AE14B2D01E56CD975A6B086355F
bIncludeInShipping=False
bAllowExternalStartInShipping=False
bCompileAFSProject=False
bUseCompression=False
bLogFiles=False
bReportStats=False
ConnectionType=USBOnly
bUseManualIPAddress=False
ManualIPAddress=

[/Script/Engine.DataDrivenConsoleVariableSettings]
+CVarsArray=(Type=CVarBool,Name="DDCvar.DrawMeshTrajectory",ToolTip="",DefaultValueFloat=0.000000,DefaultValueInt=0,DefaultValueBool=False)
+CVarsArray=(Type=CVarBool,Name="DDCvar.MMDrawQuery",ToolTip="",DefaultValueFloat=0.000000,DefaultValueInt=0,DefaultValueBool=False)
+CVarsArray=(Type=CVarBool,Name="DDCvar.MMDrawMatch",ToolTip="",DefaultValueFloat=0.000000,DefaultValueInt=0,DefaultValueBool=False)
+CVarsArray=(Type=CVarBool,Name="DDCvar.DrawCharacterDebugShapes",ToolTip="",DefaultValueFloat=0.000000,DefaultValueInt=0,DefaultValueBool=False)
+CVarsArray=(Type=CVarBool,Name="DDCvar.MMUseStateMachine",ToolTip="",DefaultValueFloat=0.000000,DefaultValueInt=0,DefaultValueBool=False)
+CVarsArray=(Type=CVarBool,Name="DDCvar.MMUseSets",ToolTip="",DefaultValueFloat=0.000000,DefaultValueInt=0,DefaultValueBool=False)
+CVarsArray=(Type=CVarInt,Name="DDCvar.Traversal.DrawDebugLevel",ToolTip="",DefaultValueFloat=0.000000,DefaultValueInt=0,DefaultValueBool=False)
+CVarsArray=(Type=CVarFloat,Name="DDCvar.Traversal.DrawDebugDuration",ToolTip="",DefaultValueFloat=1.000000,DefaultValueInt=0,DefaultValueBool=False)
+CVarsArray=(Type=CVarInt,Name="DDCvar.MMDatabaseLOD",ToolTip="",DefaultValueFloat=0.000000,DefaultValueInt=0,DefaultValueBool=False)
+CVarsArray=(Type=CVarBool,Name="DDCvar.DrawVisLogShapesForFoleySounds",ToolTip="",DefaultValueFloat=0.000000,DefaultValueInt=0,DefaultValueBool=False)

[/Script/Engine.PhysicsSettings]
bTickPhysicsAsync=False
bSupportUVFromHitResults=True

[URL]
GameName=Baoli

[/Script/FFXFSR3Settings.FFXFSR3Settings]
r.FidelityFX.FSR3.EnabledInEditorViewport=True
r.FidelityFX.FSR3.QualityMode=0
r.FidelityFX.FI.RHIPacingMode=1
r.FidelityFX.FSR3.Sharpness=0.200000
r.FidelityFX.FI.Enabled=True
r.FidelityFX.FSR3.Enabled=True

[/Script/LevelSequence.LevelSequenceProjectSettings]
LevelSequence.DefaultDisplayRate=240

[/Script/IOSRuntimeSettings.IOSRuntimeSettings]
bEnableDynamicMaxFPS=False

[/Script/DLSS.DLSSSettings]
bEnableDLSSInEditorViewports=False
bShowDLSSSDebugOnScreenMessages=False
bEnableDLSSInPlayInEditorViewports=False
bEnableDLSSVulkan=False
bEnableDLSSD3D11=False
bEnableDLSSD3D12=False

[CoreRedirects]
+FunctionRedirects=(OldName="/Script/Baoli.ChestActor.testFunction",NewName="/Script/Baoli.ChestActor.TestFunction")

[/Script/Engine.ProxyLODMeshSimplificationSettings]
r.ProxyLODMeshReductionModule=InstaLODMeshReduction

[/Script/Engine.MeshSimplificationSettings]
r.MeshReductionModule=InstaLODMeshReduction

[/Script/Engine.SkeletalMeshSimplificationSettings]
r.SkeletalMeshReductionModule=InstaLODMeshReduction

[/Script/AndroidRuntimeSettings.AndroidRuntimeSettings]
TargetSDKVersion=33
bPackageDataInsideApk=True
