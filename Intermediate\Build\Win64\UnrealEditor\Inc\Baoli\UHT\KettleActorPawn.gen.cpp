// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Baoli/Interactable/Chest/KettleActorPawn.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeKettleActorPawn() {}

// Begin Cross Module References
BAOLI_API UClass* Z_Construct_UClass_AKettleActorPawn();
BAOLI_API UClass* Z_Construct_UClass_AKettleActorPawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APawn();
ENGINE_API UClass* Z_Construct_UClass_UCameraComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_Baoli();
// End Cross Module References

// Begin Class AKettleActorPawn
void AKettleActorPawn::StaticRegisterNativesAKettleActorPawn()
{
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AKettleActorPawn);
UClass* Z_Construct_UClass_AKettleActorPawn_NoRegister()
{
	return AKettleActorPawn::StaticClass();
}
struct Z_Construct_UClass_AKettleActorPawn_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "Interactable/Chest/KettleActorPawn.h" },
		{ "ModuleRelativePath", "Interactable/Chest/KettleActorPawn.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Root_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Chest/KettleActorPawn.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SM_Kettle_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Chest/KettleActorPawn.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraComponent_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Chest/KettleActorPawn.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Root;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SM_Kettle;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CameraComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AKettleActorPawn>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AKettleActorPawn_Statics::NewProp_Root = { "Root", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AKettleActorPawn, Root), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Root_MetaData), NewProp_Root_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AKettleActorPawn_Statics::NewProp_SM_Kettle = { "SM_Kettle", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AKettleActorPawn, SM_Kettle), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SM_Kettle_MetaData), NewProp_SM_Kettle_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AKettleActorPawn_Statics::NewProp_CameraComponent = { "CameraComponent", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AKettleActorPawn, CameraComponent), Z_Construct_UClass_UCameraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraComponent_MetaData), NewProp_CameraComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AKettleActorPawn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AKettleActorPawn_Statics::NewProp_Root,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AKettleActorPawn_Statics::NewProp_SM_Kettle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AKettleActorPawn_Statics::NewProp_CameraComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AKettleActorPawn_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AKettleActorPawn_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_APawn,
	(UObject* (*)())Z_Construct_UPackage__Script_Baoli,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AKettleActorPawn_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AKettleActorPawn_Statics::ClassParams = {
	&AKettleActorPawn::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_AKettleActorPawn_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_AKettleActorPawn_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AKettleActorPawn_Statics::Class_MetaDataParams), Z_Construct_UClass_AKettleActorPawn_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AKettleActorPawn()
{
	if (!Z_Registration_Info_UClass_AKettleActorPawn.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AKettleActorPawn.OuterSingleton, Z_Construct_UClass_AKettleActorPawn_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AKettleActorPawn.OuterSingleton;
}
template<> BAOLI_API UClass* StaticClass<AKettleActorPawn>()
{
	return AKettleActorPawn::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AKettleActorPawn);
AKettleActorPawn::~AKettleActorPawn() {}
// End Class AKettleActorPawn

// Begin Registration
struct Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Chest_KettleActorPawn_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AKettleActorPawn, AKettleActorPawn::StaticClass, TEXT("AKettleActorPawn"), &Z_Registration_Info_UClass_AKettleActorPawn, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AKettleActorPawn), 1102519077U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Chest_KettleActorPawn_h_43832581(TEXT("/Script/Baoli"),
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Chest_KettleActorPawn_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Chest_KettleActorPawn_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
