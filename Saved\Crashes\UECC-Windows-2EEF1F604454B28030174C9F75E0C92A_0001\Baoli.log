﻿Log file open, 06/05/25 14:45:12
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=34116)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: Baoli
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\P4\dev\Baoli\Baoli.uproject -skipcompile""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.268816
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-2EEF1F604454B28030174C9F75E0C92A
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/P4/dev/Baoli/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogConfig: Display: Loading Android ini files took 0.04 seconds
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogConfig: Display: Loading IOS ini files took 0.04 seconds
LogConfig: Display: Loading Mac ini files took 0.05 seconds
LogConfig: Display: Loading Unix ini files took 0.06 seconds
LogConfig: Display: Loading VisionOS ini files took 0.06 seconds
LogConfig: Display: Loading TVOS ini files took 0.06 seconds
LogConfig: Display: Loading Windows ini files took 0.06 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.06 seconds
LogConfig: Display: Loading Linux ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin Chooser
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin JsonBlueprintUtilities
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GameFeatures
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ModularGameplay
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin ScriptableToolsFramework
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WebBrowserWidget
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationLocomotionLibrary
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogAssetRegistry: Display: Asset registry cache read as 106.3 MiB from H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin AnimationWarping
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin BlendStack
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin MotionWarping
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin PoseSearch
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin AssetReferenceRestrictions
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin ScriptableToolsEditorMode
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin AxFImporter
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin MDLImporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin ActorPalette
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GPULightmass
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin NiagaraFluids
LogPluginManager: Mounting Engine plugin USDImporter
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin MotionTrajectory
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin AnalyticsBlueprintLibrary
LogPluginManager: Mounting Engine plugin Reflex
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Project plugin Inkpot
LogPluginManager: Mounting Project plugin OptimizedWebBrowser
LogPluginManager: Mounting Project plugin rdBPtools
LogPluginManager: Mounting Project plugin PlatformFunctions
LogPluginManager: Mounting Project plugin SnappingHelper
SourceControl: Revision control is disabled
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogGPULightmass: GPULightmass module is loaded
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: NVIDIA GeForce RTX 2080 Ti
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: -skipcompile
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 36
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.57ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Chooser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'JsonBlueprintUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataRegistry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameFeatures' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ModularGameplay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ScriptableToolsFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebBrowserWidget' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationLocomotionLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationWarping' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GameplayInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MotionWarping' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PoseSearch' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetReferenceRestrictions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ScriptableToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AxFImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.08ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/MDLImporter.ini) has wildcard redirect /DatasmithContent/Materials/MDL/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ActorPalette' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GPULightmass' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/NiagaraFluids.ini) has wildcard redirect /NiagaraSimulationStages/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'USDImporter' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MotionTrajectory' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnalyticsBlueprintLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Reflex' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Inkpot' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OptimizedWebBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'rdBPtools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformFunctions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SnappingHelper' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.06.05-09.15.13:326][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.05-09.15.13:326][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.05-09.15.13:326][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.05-09.15.13:326][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.05-09.15.13:326][  0]LogConfig: CVar [[r.Mobile.EnableNoPrecomputedLightingCSMShader:1]] deferred - dummy variable created
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.AllowGlobalClipPlane:0]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.CustomDepth:3]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.VirtualTextures:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.AntiAliasingMethod:4]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.ReflectionCaptureResolution:32]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs:0]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.MegaLights.EnableForProject:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.PathTracing:0]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.RayTracing.Shadows:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.RayTracing.UseTextureLod:1]]
[2025.06.05-09.15.13:326][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject:0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.CustomDepthTemporalAAJitter:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default:75.000000]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default.Desktop.Mode:0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.Shadow.CSMCaching:0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.MSAACount:8]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileSize:32]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileBorderSize:2]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.MaxTextureSize:4096]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.UseCompression:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.VT.TileBorderSize:4]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.DefaultTexelsPerVertex:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.AllowStaticLighting:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.Shadow.UnbuiltPreviewInGame:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.NormalMapsForStaticLighting:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing.LightingMode:0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.VirtualTexturedLightmaps:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.Lumen.ScreenTracingSource:0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure:0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Bias:0.000000]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Method:2]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.vt.rvt.HighQualityPerPixelHeight:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.05-09.15.13:327][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.05-09.15.13:327][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.05-09.15.13:327][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.05-09.15.13:327][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.05-09.15.13:332][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.05-09.15.13:332][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.06.05-09.15.13:332][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.05-09.15.13:332][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.05-09.15.13:332][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.05-09.15.13:332][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.05-09.15.13:336][  0]LogRHI: Using Default RHI: D3D12
[2025.06.05-09.15.13:336][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.05-09.15.13:336][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.05-09.15.13:340][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.05-09.15.13:340][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.05-09.15.13:455][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.05-09.15.13:455][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-09.15.13:455][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 3 output[s]
[2025.06.05-09.15.13:455][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.05-09.15.13:455][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.05-09.15.13:599][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.05-09.15.13:599][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-09.15.13:599][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-09.15.13:600][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.05-09.15.13:600][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.05-09.15.13:606][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.05-09.15.13:606][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.05-09.15.13:606][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-09.15.13:738][  0]LogD3D12RHI: Found D3D12 adapter 3: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.05-09.15.13:738][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-09.15.13:738][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-09.15.13:738][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.05-09.15.13:738][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.05-09.15.13:738][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.05-09.15.13:738][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.05-09.15.13:738][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.05-09.15.13:738][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.05-09.15.13:738][  0]LogHAL: Display: Platform has ~ 64 GB [68630138880 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.05-09.15.13:739][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.05-09.15.13:739][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.05-09.15.13:739][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.05-09.15.13:739][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.05-09.15.13:739][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.05-09.15.13:739][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.05-09.15.13:739][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.05-09.15.13:739][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.05-09.15.13:739][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.05-09.15.13:739][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.05-09.15.13:739][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.05-09.15.13:739][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.05-09.15.13:739][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.06.05-09.15.13:739][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/P4/dev/Baoli/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.05-09.15.13:739][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.06.05-09.15.13:739][  0]LogInit: User: Shashank
[2025.06.05-09.15.13:739][  0]LogInit: CPU Page size=4096, Cores=16
[2025.06.05-09.15.13:739][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.05-09.15.15:273][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.06.05-09.15.15:273][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.05-09.15.15:273][  0]LogMemory: Process Physical Memory: 691.45 MB used, 726.37 MB peak
[2025.06.05-09.15.15:273][  0]LogMemory: Process Virtual Memory: 768.88 MB used, 772.29 MB peak
[2025.06.05-09.15.15:273][  0]LogMemory: Physical Memory: 27594.25 MB used,  37856.55 MB free, 65450.80 MB total
[2025.06.05-09.15.15:273][  0]LogMemory: Virtual Memory: 34583.08 MB used,  34963.72 MB free, 69546.80 MB total
[2025.06.05-09.15.15:273][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.05-09.15.15:277][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.05-09.15.15:286][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.05-09.15.15:286][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.05-09.15.15:287][  0]LogInit: Using OS detected language (en-GB).
[2025.06.05-09.15.15:287][  0]LogInit: Using OS detected locale (en-IN).
[2025.06.05-09.15.15:289][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.05-09.15.15:289][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.05-09.15.15:630][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.05-09.15.15:630][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.06.05-09.15.15:630][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.05-09.15.15:644][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.05-09.15.15:644][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.05-09.15.15:767][  0]LogRHI: Using Default RHI: D3D12
[2025.06.05-09.15.15:767][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.05-09.15.15:767][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.05-09.15.15:767][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.05-09.15.15:767][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.05-09.15.15:767][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.05-09.15.15:767][  0]LogWindows: Attached monitors:
[2025.06.05-09.15.15:767][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.06.05-09.15.15:767][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.06.05-09.15.15:767][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.06.05-09.15.15:767][  0]LogWindows: Found 3 attached monitors.
[2025.06.05-09.15.15:767][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.05-09.15.15:767][  0]LogRHI: RHI Adapter Info:
[2025.06.05-09.15.15:767][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.06.05-09.15.15:767][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.05-09.15.15:767][  0]LogRHI:      Driver Date: 4-25-2025
[2025.06.05-09.15.15:767][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.06.05-09.15.15:798][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.05-09.15.15:872][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.05-09.15.15:872][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.05-09.15.15:963][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: Raster order views are supported
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.05-09.15.15:963][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.05-09.15.15:989][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000A3789B65300)
[2025.06.05-09.15.15:990][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000A3789B65580)
[2025.06.05-09.15.15:990][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000A3789B65800)
[2025.06.05-09.15.15:990][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.05-09.15.15:990][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.05-09.15.15:990][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.06.05-09.15.15:990][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.06.05-09.15.15:990][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.05-09.15.15:990][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.05-09.15.16:001][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.05-09.15.16:005][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.05-09.15.16:014][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all'
[2025.06.05-09.15.16:014][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all" ]
[2025.06.05-09.15.16:037][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.05-09.15.16:038][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.05-09.15.16:038][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.05-09.15.16:038][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.05-09.15.16:038][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.05-09.15.16:038][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.06.05-09.15.16:038][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.05-09.15.16:038][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.05-09.15.16:038][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.05-09.15.16:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.05-09.15.16:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.05-09.15.16:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.05-09.15.16:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.05-09.15.16:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.05-09.15.16:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.05-09.15.16:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.05-09.15.16:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.05-09.15.16:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.05-09.15.16:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.05-09.15.16:080][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.05-09.15.16:080][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.05-09.15.16:097][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.05-09.15.16:097][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.05-09.15.16:097][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.05-09.15.16:097][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.05-09.15.16:112][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.06.05-09.15.16:112][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.06.05-09.15.16:112][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.06.05-09.15.16:127][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.05-09.15.16:127][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.05-09.15.16:127][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.05-09.15.16:127][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.05-09.15.16:142][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.05-09.15.16:142][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.05-09.15.16:161][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.05-09.15.16:161][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.05-09.15.16:161][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.05-09.15.16:161][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.05-09.15.16:161][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.05-09.15.16:206][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_METAL
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.05-09.15.16:210][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.05-09.15.16:210][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.06.05-09.15.16:210][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.06.05-09.15.16:212][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.05-09.15.16:212][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/P4/dev/Baoli/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.05-09.15.16:212][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.05-09.15.16:212][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/P4/dev/Baoli/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.05-09.15.16:212][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.05-09.15.16:273][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.06.05-09.15.16:273][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.05-09.15.16:273][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.05-09.15.16:274][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.05-09.15.16:274][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.05-09.15.16:275][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.05-09.15.16:276][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.06.05-09.15.16:276][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 31456 --child-id Zen_31456_Startup'
[2025.06.05-09.15.16:356][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.05-09.15.16:356][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.082 seconds
[2025.06.05-09.15.16:358][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.05-09.15.16:362][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.06.05-09.15.16:362][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.04ms. RandomReadSpeed=1368.19MBs, RandomWriteSpeed=293.67MBs. Assigned SpeedClass 'Local'
[2025.06.05-09.15.16:363][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.05-09.15.16:363][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.05-09.15.16:363][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.05-09.15.16:363][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.05-09.15.16:363][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.05-09.15.16:363][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.05-09.15.16:363][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.05-09.15.16:364][  0]LogShaderCompilers: Guid format shader working directory is 33 characters bigger than the processId version (H:/P4/dev/Baoli/Intermediate/Shaders/WorkingDirectory/31456/).
[2025.06.05-09.15.16:364][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/D62D070F4B87A7843EE223A487AE418C/'.
[2025.06.05-09.15.16:364][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.06.05-09.15.16:364][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.06.05-09.15.16:366][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/P4/dev/Baoli/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.05-09.15.16:366][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.05-09.15.16:862][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.05-09.15.17:455][  0]LogSlate: Using FreeType 2.10.0
[2025.06.05-09.15.17:456][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.05-09.15.17:456][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-09.15.17:456][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-09.15.17:458][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-09.15.17:458][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-09.15.17:458][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-09.15.17:458][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-09.15.17:482][  0]LogAssetRegistry: FAssetRegistry took 0.0031 seconds to start up
[2025.06.05-09.15.17:484][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.05-09.15.17:488][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.05-09.15.17:657][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-09.15.17:659][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.05-09.15.17:659][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.05-09.15.17:659][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.05-09.15.17:669][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.05-09.15.17:669][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.05-09.15.17:693][  0]LogDeviceProfileManager: Active device profile: [00000A37A3F65600][00000A379AEAC000 66] WindowsEditor
[2025.06.05-09.15.17:693][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.05-09.15.17:694][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.05-09.15.17:697][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.06.05-09.15.17:697][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.06.05-09.15.17:726][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:727][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.06.05-09.15.17:727][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-09.15.17:727][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:727][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.06.05-09.15.17:727][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-09.15.17:727][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:727][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.06.05-09.15.17:727][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-09.15.17:727][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:727][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.06.05-09.15.17:727][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-09.15.17:728][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:729][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-09.15.17:729][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:729][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.06.05-09.15.17:729][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.05-09.15.17:729][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:729][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.06.05-09.15.17:729][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.05-09.15.17:729][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-09.15.17:730][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.06.05-09.15.17:730][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-09.15.17:730][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:730][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.06.05-09.15.17:730][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-09.15.17:730][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.06.05-09.15.17:730][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-09.15.17:731][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:731][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.06.05-09.15.17:731][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-09.15.17:731][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.06.05-09.15.17:731][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-09.15.17:731][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.17:731][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.06.05-09.15.17:731][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-09.15.17:897][  0]LogMeshReduction: Display: Mesh reduction module (r.MeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-09.15.17:897][  0]LogMeshReduction: Display: Skeletal mesh reduction module (r.SkeletalMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-09.15.17:897][  0]LogMeshReduction: Display: HLOD mesh reduction module (r.ProxyLODMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-09.15.17:909][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.05-09.15.17:909][  0]LogMeshReduction: Display: Using InstaLODMeshReduction for automatic skeletal mesh reduction
[2025.06.05-09.15.17:909][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.05-09.15.17:909][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.05-09.15.17:909][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.05-09.15.18:041][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.49ms
[2025.06.05-09.15.18:063][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.50ms
[2025.06.05-09.15.18:075][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.05-09.15.18:076][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.46ms
[2025.06.05-09.15.18:251][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.05-09.15.18:251][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.05-09.15.18:256][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.05-09.15.18:256][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.05-09.15.18:257][  0]LogLiveCoding: Display: First instance in process group "UE_Baoli_0x736adef1", spawning console
[2025.06.05-09.15.18:260][  0]LogLiveCoding: Display: Waiting for server
[2025.06.05-09.15.18:277][  0]LogSlate: Border
[2025.06.05-09.15.18:277][  0]LogSlate: BreadcrumbButton
[2025.06.05-09.15.18:277][  0]LogSlate: Brushes.Title
[2025.06.05-09.15.18:277][  0]LogSlate: Default
[2025.06.05-09.15.18:277][  0]LogSlate: Icons.Save
[2025.06.05-09.15.18:277][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.05-09.15.18:277][  0]LogSlate: ListView
[2025.06.05-09.15.18:277][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.05-09.15.18:277][  0]LogSlate: SoftwareCursor_Grab
[2025.06.05-09.15.18:277][  0]LogSlate: TableView.DarkRow
[2025.06.05-09.15.18:277][  0]LogSlate: TableView.Row
[2025.06.05-09.15.18:277][  0]LogSlate: TreeView
[2025.06.05-09.15.18:364][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.05-09.15.18:369][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.05-09.15.18:370][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.717 ms
[2025.06.05-09.15.18:379][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.05-09.15.18:398][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.05-09.15.18:398][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.05-09.15.18:398][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.05-09.15.18:398][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.06.05-09.15.18:446][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.05-09.15.18:450][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.05-09.15.18:450][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.05-09.15.18:450][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:57972'.
[2025.06.05-09.15.18:453][  0]LogUdpMessaging: Display: Added local interface '192.168.1.5' to multicast group '230.0.0.1:6666'
[2025.06.05-09.15.18:453][  0]LogUdpMessaging: Display: Added local interface '172.24.112.1' to multicast group '230.0.0.1:6666'
[2025.06.05-09.15.18:453][  0]LogUdpMessaging: Display: Added local interface '172.29.240.1' to multicast group '230.0.0.1:6666'
[2025.06.05-09.15.18:569][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.05-09.15.18:569][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.06.05-09.15.18:569][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.06.05-09.15.18:569][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.05-09.15.18:569][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.05-09.15.18:729][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.05-09.15.18:729][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.05-09.15.18:743][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.05-09.15.18:947][  0]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.06.05-09.15.18:948][  0]LogCEFBrowser: CEF GPU acceleration enabled
[2025.06.05-09.15.19:165][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.51ms
[2025.06.05-09.15.19:275][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 784753085E70403F8000000000005000 | Instance: 324349A745497C6F0A2A43BA65FD5DA3 (DESKTOP-E41IK6R-31456).
[2025.06.05-09.15.19:518][  0]LogTemp: Warning: ✓ AI Perception system enabled and events bound
[2025.06.05-09.15.19:579][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.06.05-09.15.19:627][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.05-09.15.19:630][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.05-09.15.19:641][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.05-09.15.19:656][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.05-09.15.19:656][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.05-09.15.19:739][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.06.05-09.15.19:740][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.06.05-09.15.19:740][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.06.05-09.15.19:740][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.06.05-09.15.19:740][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.06.05-09.15.19:740][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.06.05-09.15.19:741][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.06.05-09.15.19:741][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.06.05-09.15.19:741][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.06.05-09.15.19:742][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.06.05-09.15.19:743][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.06.05-09.15.19:743][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.06.05-09.15.19:743][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.06.05-09.15.19:743][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.06.05-09.15.19:744][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.06.05-09.15.19:744][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.06.05-09.15.19:744][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.06.05-09.15.19:744][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.06.05-09.15.19:744][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.06.05-09.15.19:745][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.06.05-09.15.19:746][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.06.05-09.15.19:746][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.06.05-09.15.19:746][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.06.05-09.15.19:746][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.06.05-09.15.19:747][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.06.05-09.15.19:747][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.06.05-09.15.19:747][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.06.05-09.15.19:747][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.06.05-09.15.19:747][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.06.05-09.15.19:748][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.06.05-09.15.19:748][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.06.05-09.15.19:749][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.06.05-09.15.19:749][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.06.05-09.15.19:749][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.06.05-09.15.19:749][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.06.05-09.15.19:795][  0]LogTimingProfiler: Initialize
[2025.06.05-09.15.19:795][  0]LogTimingProfiler: OnSessionChanged
[2025.06.05-09.15.19:795][  0]LoadingProfiler: Initialize
[2025.06.05-09.15.19:795][  0]LoadingProfiler: OnSessionChanged
[2025.06.05-09.15.19:795][  0]LogNetworkingProfiler: Initialize
[2025.06.05-09.15.19:795][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.05-09.15.19:795][  0]LogMemoryProfiler: Initialize
[2025.06.05-09.15.19:795][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.05-09.15.19:902][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.46ms
[2025.06.05-09.15.19:910][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.44ms
[2025.06.05-09.15.20:212][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-09.15.20:212][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-09.15.20:265][  0]LogCollectionManager: Loaded 1 collections in 0.000667 seconds
[2025.06.05-09.15.20:267][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Saved/Collections/' took 0.00s
[2025.06.05-09.15.20:269][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Developers/Shashank/Collections/' took 0.00s
[2025.06.05-09.15.20:271][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Collections/' took 0.00s
[2025.06.05-09.15.20:336][  0]LogConfig: Branch 'Plugins' had been unloaded. Reloading on-demand took 0.44ms
[2025.06.05-09.15.20:338][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-09.15.20:338][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-09.15.20:339][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-09.15.20:339][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-09.15.20:339][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-09.15.20:339][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-09.15.20:364][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-09.15.20:364][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-09.15.20:399][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-09.15.20:399][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-09.15.20:401][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-09.15.20:401][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-09.15.20:401][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-09.15.20:401][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-09.15.20:427][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-09.15.20:427][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-09.15.20:442][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-06-05T09:15:20.442Z using C
[2025.06.05-09.15.20:443][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=Baoli, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.05-09.15.20:443][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.05-09.15.20:443][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.05-09.15.20:448][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.05-09.15.20:448][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.05-09.15.20:448][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.05-09.15.20:448][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000048
[2025.06.05-09.15.20:448][  0]LogFab: Display: Logging in using persist
[2025.06.05-09.15.20:449][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.06.05-09.15.20:480][  0]LogUObjectArray: 47651 objects as part of root set at end of initial load.
[2025.06.05-09.15.20:480][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.05-09.15.20:493][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 40254 public script object entries (1077.02 KB)
[2025.06.05-09.15.20:493][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.06.05-09.15.20:600][  0]LogEngine: Initializing Engine...
[2025.06.05-09.15.20:601][  0]LogGameFeatures: Initializing game features subsystem
[2025.06.05-09.15.20:602][  0]InkPlusPlus: FStory::FStory 00000A37BF6CAB10
[2025.06.05-09.15.20:602][  0]InkPlusPlus: Warning: WARNING: Version of ink used to build story doesn't match current version of engine. Non-critical, but recommend synchronising.
[2025.06.05-09.15.20:604][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.05-09.15.20:604][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.05-09.15.20:705][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.05-09.15.20:716][  0]LogGameFeatures: Scanning for built-in game feature plugins
[2025.06.05-09.15.20:716][  0]LogGameFeatures: Loading 233 builtins
[2025.06.05-09.15.20:717][  0]LogGameFeatures: Display: Total built in plugin load time 0.0008s
[2025.06.05-09.15.20:717][  0]LogStats: BuiltInGameFeaturePlugins loaded. -  0.001 s
[2025.06.05-09.15.20:718][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.05-09.15.20:733][  0]LogNetVersion: Set ProjectVersion to Alpha. Version Checksum will be recalculated on next use.
[2025.06.05-09.15.20:733][  0]LogInit: Texture streaming: Enabled
[2025.06.05-09.15.20:743][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.06.05-09.15.20:749][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.05-09.15.20:755][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.05-09.15.20:755][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.05-09.15.20:755][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.05-09.15.20:755][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.05-09.15.20:755][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-09.15.20:755][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-09.15.20:755][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-09.15.20:755][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-09.15.20:755][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-09.15.20:755][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-09.15.20:755][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-09.15.20:755][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-09.15.20:755][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-09.15.20:755][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-09.15.20:755][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-09.15.20:759][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-09.15.20:816][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-09.15.20:817][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-09.15.20:818][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-09.15.20:818][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-09.15.20:818][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.05-09.15.20:818][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.05-09.15.20:821][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.05-09.15.20:821][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.05-09.15.20:821][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.05-09.15.20:821][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.05-09.15.20:821][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.05-09.15.20:826][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.05-09.15.20:829][  0]LogInit: Undo buffer set to 256 MB
[2025.06.05-09.15.20:829][  0]LogInit: Transaction tracking system initialized
[2025.06.05-09.15.20:883][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.49ms
[2025.06.05-09.15.20:884][  0]LocalizationService: Localization service is disabled
[2025.06.05-09.15.21:098][  0]LogPython: Using Python 3.11.8
[2025.06.05-09.15.21:323][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/' took 0.27s
[2025.06.05-09.15.22:149][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.06.05-09.15.22:159][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (0 permutations).
[2025.06.05-09.15.22:231][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.05-09.15.22:231][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.05-09.15.22:267][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.05-09.15.22:285][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-09.15.22:285][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-09.15.22:286][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-09.15.22:286][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-09.15.22:286][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-09.15.22:286][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-09.15.22:314][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-09.15.22:314][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-09.15.22:319][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.05-09.15.22:319][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.05-09.15.22:341][  0]LogEditorDataStorage: Initializing
[2025.06.05-09.15.22:342][  0]LogEditorDataStorage: Initialized
[2025.06.05-09.15.22:349][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.06.05-09.15.22:351][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/MHI.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performance/MHP_Baoli.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performances/MHP_Scene1_01.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:352][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-09.15.22:353][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.05-09.15.22:367][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.05-09.15.22:381][  0]LogUnrealEdMisc: Loading editor; pre map load, took 9.860
[2025.06.05-09.15.22:382][  0]Cmd: MAP LOAD FILE="H:/P4/dev/Baoli/Content/Levels/DefaultLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.05-09.15.22:384][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.05-09.15.22:384][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-09.15.22:394][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-09.15.22:394][  0]InkPlusPlus: FStory::~FStory 00000A37BF6CAB10
[2025.06.05-09.15.22:396][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.55ms
[2025.06.05-09.15.22:468][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.05-09.15.22:468][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Glossy (0x2CCA389A36D3E860)
[2025.06.05-09.15.22:468][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/material (0x38FB08B605AA9364)
[2025.06.05-09.15.22:469][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.05-09.15.22:469][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Dark (0xC267FEC07D768F2)
[2025.06.05-09.15.22:509][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.05-09.15.22:510][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVback (0x8B181E584DB8A471) /Game/Assets/TV/TVback (0x8B181E584DB8A471) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.05-09.15.22:724][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS (0xF21345B7066A3DF7)
[2025.06.05-09.15.23:220][  0]LogLinker: Warning: [AssetLog] H:\P4\dev\Baoli\Content\BaoliAssets\BrickInstances\Brick_low_001.uasset: VerifyImport: Failed to find script package for import object 'Package /Script/rdInst'
[2025.06.05-09.15.23:514][  0]LogAssetRegistry: Display: Asset registry cache written as 106.3 MiB to H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin
[2025.06.05-09.15.25:055][  0]LogEditorDomain: Display: Class /Script/rdInst.rdInstAssetUserData is imported by a package but does not exist in memory. EditorDomain keys for packages using it will be invalid if it still exists.
	To clear this message, resave packages that use the deleted class, or load its module earlier than the packages that use it are referenced.
[2025.06.05-09.15.26:209][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: WaitingForIo) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.26:213][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 334 to allow recursive sync load to finish
[2025.06.05-09.15.26:213][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.05-09.15.26:213][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: ExportsDone) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.26:213][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 335 to allow recursive sync load to finish
[2025.06.05-09.15.26:213][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.05-09.15.26:814][  0]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\CBP_SandboxCharacter.uasset: [Compiler] Input pin  Debug Session Unique Identifier  specifying non-default value no longer exists on node  Motion Match . Please refresh node or reset pin to default value to remove pin.
[2025.06.05-09.15.27:973][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: WaitingForIo) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.27:974][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 336 to allow recursive sync load to finish
[2025.06.05-09.15.27:975][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.05-09.15.27:975][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: ExportsDone) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-09.15.27:975][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 337 to allow recursive sync load to finish
[2025.06.05-09.15.27:975][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.05-09.15.28:087][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:088][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Handplant' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Handplant.MSS_FoleySound_Handplant' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:089][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Jump' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Jump.MSS_FoleySound_Jump' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:090][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Land' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Land.MSS_FoleySound_Land' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:091][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Run' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Run.MSS_FoleySound_Run' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:092][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunBackwards.MSS_FoleySound_RunBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:093][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunStrafe' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunStrafe.MSS_FoleySound_RunStrafe' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:094][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Scuff' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Scuff.MSS_FoleySound_Scuff' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:095][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffPivot' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffPivot.MSS_FoleySound_ScuffPivot' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:096][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffWall' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffWall.MSS_FoleySound_ScuffWall' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:097][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Tumble' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Tumble.MSS_FoleySound_Tumble' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:099][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Walk' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Walk.MSS_FoleySound_Walk' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.28:099][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_WalkBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_WalkBackwards.MSS_FoleySound_WalkBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-09.15.29:833][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.06.05-09.15.29:868][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.06.05-09.15.29:884][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_High...
[2025.06.05-09.15.29:884][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_High...
[2025.06.05-09.15.30:285][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.30:289][  0]LogSkeletalMesh: Built Skeletal Mesh [0.41s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High
[2025.06.05-09.15.30:298][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.06.05-09.15.30:912][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants_High...
[2025.06.05-09.15.31:262][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.31:264][  0]LogSkeletalMesh: Built Skeletal Mesh [0.35s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High
[2025.06.05-09.15.31:379][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.31:383][  0]LogSkeletalMesh: Built Skeletal Mesh [1.50s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High
[2025.06.05-09.15.31:685][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_High...
[2025.06.05-09.15.31:704][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_Cap_01_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-09.15.31:705][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelNut_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-09.15.31:705][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelLeaf_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-09.15.32:095][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.32:098][  0]LogSkeletalMesh: Built Skeletal Mesh [0.41s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High
[2025.06.05-09.15.33:491][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_hoodie_nrm_High...
[2025.06.05-09.15.33:948][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.33:952][  0]LogSkeletalMesh: Built Skeletal Mesh [0.46s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High
[2025.06.05-09.15.34:020][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Foley.SM_Foley.
[2025.06.05-09.15.34:020][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.05-09.15.34:020][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Ambient.SM_Ambient.
[2025.06.05-09.15.34:020][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.05-09.15.34:020][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Reverb.SM_Reverb.
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Jumps
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Stops
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.05-09.15.35:741][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.05-09.15.35:742][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.05-09.15.35:743][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.05-09.15.35:743][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.05-09.15.35:743][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.05-09.15.35:743][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.05-09.15.35:743][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.05-09.15.35:743][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.05-09.15.35:888][  0]LogPoseSearch: 25abbe71a7361c623bd3755ff5e1fbe7a56e7f94 - PSD_Dense_Jumps BeginCache
[2025.06.05-09.15.35:889][  0]LogPoseSearch: 25abbe71a7361c623bd3755ff5e1fbe7a56e7f94 - PSD_Dense_Jumps BuildIndex From Cache
[2025.06.05-09.15.35:900][  0]LogPoseSearch: 52bcfc861296fe46e902c3e1977b2a893663552c - PSD_Dense_Jumps_Far BeginCache
[2025.06.05-09.15.35:900][  0]LogPoseSearch: 52bcfc861296fe46e902c3e1977b2a893663552c - PSD_Dense_Jumps_Far BuildIndex From Cache
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Stops
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.05-09.15.35:910][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.05-09.15.35:911][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.05-09.15.35:960][  0]LogPoseSearch: c903d208ce6ce6d6d57c16e8029907a718a6c7e9 - PSD_Dense_Stand_Idle_Lands_Heavy BeginCache
[2025.06.05-09.15.35:961][  0]LogPoseSearch: c903d208ce6ce6d6d57c16e8029907a718a6c7e9 - PSD_Dense_Stand_Idle_Lands_Heavy BuildIndex From Cache
[2025.06.05-09.15.35:979][  0]LogPoseSearch: 2c155ec5c459344cf515e7737ccfee7b0c910810 - PSD_Dense_Stand_Run_Lands_Heavy BeginCache
[2025.06.05-09.15.35:980][  0]LogPoseSearch: 2c155ec5c459344cf515e7737ccfee7b0c910810 - PSD_Dense_Stand_Run_Lands_Heavy BuildIndex From Cache
[2025.06.05-09.15.36:006][  0]LogPoseSearch: 0647c6013483ddb2053168a7d66d3f931f221b6f - PSD_Dense_Stand_Walk_Lands_Heavy BeginCache
[2025.06.05-09.15.36:006][  0]LogPoseSearch: 0647c6013483ddb2053168a7d66d3f931f221b6f - PSD_Dense_Stand_Walk_Lands_Heavy BuildIndex From Cache
[2025.06.05-09.15.36:110][  0]LogPoseSearch: b9ad6da2b4015614aa861a663ab0dc2c7376fcdd - PSD_Traversal BeginCache
[2025.06.05-09.15.36:111][  0]LogPoseSearch: b9ad6da2b4015614aa861a663ab0dc2c7376fcdd - PSD_Traversal BuildIndex From Cache
[2025.06.05-09.15.36:119][  0]LogPoseSearch: 1f816991d0c81fa165c9258a64c0e110cb545466 - PSD_Dense_Jumps_FromTraversal BeginCache
[2025.06.05-09.15.36:120][  0]LogPoseSearch: 1f816991d0c81fa165c9258a64c0e110cb545466 - PSD_Dense_Jumps_FromTraversal BuildIndex From Cache
[2025.06.05-09.15.36:128][  0]LogPoseSearch: 44dfcd0439dfacb33784494a07f81e5c4107264f - PSD_Dense_Stand_Walk_FromTraversal BeginCache
[2025.06.05-09.15.36:128][  0]LogPoseSearch: 44dfcd0439dfacb33784494a07f81e5c4107264f - PSD_Dense_Stand_Walk_FromTraversal BuildIndex From Cache
[2025.06.05-09.15.36:129][  0]LogPoseSearch: 2ef3b5bccd181d28d29d656af18a9552f7025b60 - PSD_Dense_Stand_Run_SpinTransition BeginCache
[2025.06.05-09.15.36:129][  0]LogPoseSearch: 2ef3b5bccd181d28d29d656af18a9552f7025b60 - PSD_Dense_Stand_Run_SpinTransition BuildIndex From Cache
[2025.06.05-09.15.36:129][  0]LogPoseSearch: c3b27113a33ed0195cb86def79fac28ef88b955a - PSD_Dense_Stand_Walk_SpinTransition BeginCache
[2025.06.05-09.15.36:130][  0]LogPoseSearch: c3b27113a33ed0195cb86def79fac28ef88b955a - PSD_Dense_Stand_Walk_SpinTransition BuildIndex From Cache
[2025.06.05-09.15.36:142][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_btm_shorts_nrm...
[2025.06.05-09.15.36:144][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_top_crewneckt_nrm...
[2025.06.05-09.15.36:145][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.06.05-09.15.36:145][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants...
[2025.06.05-09.15.36:145][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm...
[2025.06.05-09.15.36:148][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.06.05-09.15.36:329][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.36:331][  0]LogSkeletalMesh: Built Skeletal Mesh [0.19s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm
[2025.06.05-09.15.36:398][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_shs_flipflops...
[2025.06.05-09.15.36:434][  0]LogPoseSearch: 2fb236583b053e85334fffc7f28ad671a7ba6fcd - PSD_Dense_Crouch_Idle BeginCache
[2025.06.05-09.15.36:434][  0]LogPoseSearch: 2fb236583b053e85334fffc7f28ad671a7ba6fcd - PSD_Dense_Crouch_Idle BuildIndex From Cache
[2025.06.05-09.15.36:459][  0]LogPoseSearch: 3eaff9342445c24b7281b43f7e167cf00b79697d - PSD_Dense_Crouch_Loop BeginCache
[2025.06.05-09.15.36:461][  0]LogPoseSearch: 3eaff9342445c24b7281b43f7e167cf00b79697d - PSD_Dense_Crouch_Loop BuildIndex From Cache
[2025.06.05-09.15.36:485][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.36:487][  0]LogSkeletalMesh: Built Skeletal Mesh [0.09s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops
[2025.06.05-09.15.36:487][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-09.15.36:501][  0]LogPoseSearch: e5bb22fa3f0832f6b1de4740e665f8b8a0cc4a40 - PSD_Dense_Crouch_Pivot BeginCache
[2025.06.05-09.15.36:502][  0]LogPoseSearch: e5bb22fa3f0832f6b1de4740e665f8b8a0cc4a40 - PSD_Dense_Crouch_Pivot BuildIndex From Cache
[2025.06.05-09.15.36:510][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.36:512][  0]LogSkeletalMesh: Built Skeletal Mesh [0.37s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants
[2025.06.05-09.15.36:513][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_casualsneakers...
[2025.06.05-09.15.36:542][  0]LogPoseSearch: 10463b9b5d9a2019be2ed12e5f4f9c3378a2c9df - PSD_Dense_Crouch_Start BeginCache
[2025.06.05-09.15.36:543][  0]LogPoseSearch: 10463b9b5d9a2019be2ed12e5f4f9c3378a2c9df - PSD_Dense_Crouch_Start BuildIndex From Cache
[2025.06.05-09.15.36:558][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.36:559][  0]LogSkeletalMesh: Built Skeletal Mesh [0.41s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.06.05-09.15.36:561][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_MC_FaceMesh...
[2025.06.05-09.15.36:595][  0]LogPoseSearch: fb459193d12be90ae89d82deeb58b955bd5ded69 - PSD_Dense_Crouch_Stops BeginCache
[2025.06.05-09.15.36:598][  0]LogPoseSearch: fb459193d12be90ae89d82deeb58b955bd5ded69 - PSD_Dense_Crouch_Stops BuildIndex From Cache
[2025.06.05-09.15.36:738][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.36:743][  0]LogSkeletalMesh: Built Skeletal Mesh [0.26s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-09.15.36:744][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.06.05-09.15.36:778][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.36:780][  0]LogSkeletalMesh: Built Skeletal Mesh [0.27s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers
[2025.06.05-09.15.36:782][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_Child2_FaceMesh...
[2025.06.05-09.15.37:202][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.37:207][  0]LogSkeletalMesh: Built Skeletal Mesh [0.46s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.06.05-09.15.37:209][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.06.05-09.15.37:257][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.06.05-09.15.37:814][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.37:819][  0]LogSkeletalMesh: Built Skeletal Mesh [1.68s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm
[2025.06.05-09.15.37:819][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-09.15.37:859][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.37:864][  0]LogSkeletalMesh: Built Skeletal Mesh [1.72s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm
[2025.06.05-09.15.37:865][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-09.15.38:003][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.38:004][  0]LogSkeletalMesh: Built Skeletal Mesh [0.19s] /Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-09.15.38:052][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.38:060][  0]LogSkeletalMesh: Built Skeletal Mesh [1.91s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.06.05-09.15.38:064][  0]LogSkeletalMesh: Building Skeletal Mesh Kellan_FaceMesh...
[2025.06.05-09.15.38:154][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.38:157][  0]LogSkeletalMesh: Built Skeletal Mesh [0.29s] /Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-09.15.38:272][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'DefaultLevel'.
[2025.06.05-09.15.38:272][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-09.15.38:327][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.05-09.15.38:433][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.38:444][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh
[2025.06.05-09.15.48:954][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.48:972][  0]LogSkeletalMesh: Built Skeletal Mesh [12.19s] /Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh
[2025.06.05-09.15.48:975][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.48:997][  0]LogSkeletalMesh: Built Skeletal Mesh [12.44s] /Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh
[2025.06.05-09.15.49:167][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-09.15.49:186][  0]LogSkeletalMesh: Built Skeletal Mesh [11.98s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.06.05-09.15.49:263][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.05-09.15.49:507][  0]LogUObjectHash: Compacting FUObjectHashTables data took   2.76ms
[2025.06.05-09.15.49:510][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.05-09.15.49:510][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.121ms to complete.
[2025.06.05-09.15.49:522][  0]LogUnrealEdMisc: Total Editor Startup Time, took 37.000
[2025.06.05-09.15.49:665][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.06.05-09.15.49:764][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-09.15.49:830][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-09.15.49:889][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-09.15.49:951][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-09.15.50:002][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-09.15.50:002][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.05-09.15.50:002][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-09.15.50:002][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.05-09.15.50:002][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-09.15.50:003][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.05-09.15.50:003][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-09.15.50:004][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.05-09.15.50:004][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-09.15.50:004][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.05-09.15.50:004][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-09.15.50:005][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.05-09.15.50:005][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-09.15.50:005][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.05-09.15.50:005][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-09.15.50:005][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.05-09.15.50:005][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-09.15.50:005][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.05-09.15.50:006][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-09.15.50:006][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.05-09.15.50:188][  0]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-09.15.50:206][  0]LogStall: Startup...
[2025.06.05-09.15.50:209][  0]LogStall: Startup complete.
[2025.06.05-09.15.50:239][  0]LogLoad: (Engine Initialization) Total time: 37.72 seconds
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 14:45:00
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-09.15.50:584][  0]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-09.15.50:584][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.05-09.15.50:584][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.05-09.15.50:594][  0]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Config/DefaultEngine.ini'
[2025.06.05-09.15.50:842][  0]LogSlate: Took 0.000287 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.05-09.15.50:845][  0]LogSlate: Took 0.000335 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.05-09.15.50:848][  0]LogSlate: Took 0.000163 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.05-09.15.50:849][  0]LogSlate: Took 0.000141 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.05-09.15.50:914][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.06.05-09.15.50:914][  0]LogStreaming: Display: FlushAsyncLoading(342): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-09.15.50:917][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.06.05-09.15.50:917][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.06.05-09.15.50:917][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.05-09.15.50:985][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.05-09.15.50:985][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.06.05-09.15.50:986][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.06.05-09.15.50:986][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.06.05-09.15.50:986][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.05-09.15.51:044][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.05-09.15.51:044][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.06.05-09.15.51:092][  0]LogSlate: Took 0.001176 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.05-09.15.51:605][  0]LogD3D12RHI: Creating RTPSO with 24 shaders (0 cached, 24 new) took 10.75 ms. Compile time 7.94 ms, link time 2.77 ms.
[2025.06.05-09.15.51:612][  0]LogD3D12RHI: Creating RTPSO with 197 shaders (0 cached, 197 new) took 58.28 ms. Compile time 50.93 ms, link time 7.19 ms.
[2025.06.05-09.15.51:685][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-09.15.51:694][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.06.05-09.15.51:694][  0]LogFab: Display: Logging in using exchange code
[2025.06.05-09.15.51:694][  0]LogFab: Display: Reading exchange code from commandline
[2025.06.05-09.15.51:694][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.06.05-09.15.51:696][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... started...
[2025.06.05-09.15.51:696][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... took 198 us
[2025.06.05-09.15.51:696][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.05-09.15.51:730][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.05-09.15.51:735][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 40.297 ms (total: 40.496 ms)
[2025.06.05-09.15.51:736][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-09.15.51:736][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-09.15.51:736][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-09.15.51:736][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_Glossy was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Glossy has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-09.15.51:736][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/material which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-09.15.51:736][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/material was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/material has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/material.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-09.15.51:737][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-09.15.51:737][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-09.15.51:737][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-09.15.51:737][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_Dark was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Dark has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-09.15.51:737][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-09.15.51:737][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVfront, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-09.15.51:737][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-09.15.51:737][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVback, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-09.15.51:737][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-09.15.51:737][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Washingmachine/steel, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-09.15.51:737][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BaoliEditor Win64 Development
[2025.06.05-09.15.51:889][  1]LogAssetRegistry: AssetRegistryGather time 0.1833s: AssetDataDiscovery 0.0249s, AssetDataGather 0.0310s, StoreResults 0.1274s. Wall time 34.4090s.
	NumCachedDirectories 0. NumUncachedDirectories 3058. NumCachedFiles 16169. NumUncachedFiles 4.
	BackgroundTickInterruptions 32.
[2025.06.05-09.15.51:967][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.06.05-09.15.51:973][  1]LogCollectionManager: Fixed up redirectors for 1 collections in 0.000034 seconds (updated 1 objects)
[2025.06.05-09.15.52:094][  1]LogMaterial: Display: Material /InterchangeAssets/gltf/M_Default.M_Default needed to have new flag set bUsedWithNanite !
[2025.06.05-09.15.52:118][  1]MapCheck: Warning: M_Default Material /InterchangeAssets/gltf/M_Default.M_Default was missing the usage flag bUsedWithNanite. If the material asset is not re-saved, it may not render correctly when run outside the editor. Fix
[2025.06.05-09.15.52:338][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.06.05-09.15.52:444][  2]LogSourceControl: Uncontrolled asset enumeration finished in 0.476296 seconds (Found 7976 uncontrolled assets)
[2025.06.05-09.15.52:501][  2]LogPoseSearch: 50dc2d6fc66385f69c3f4316520b5453bbe11082 - PSD_Sparse_Stand_Walk_Stops BeginCache
[2025.06.05-09.15.52:502][  2]LogPoseSearch: 7bd24e04e6106f1973227ab2f5dd916a0214b232 - PSD_Sparse_Stand_Walk_Starts BeginCache
[2025.06.05-09.15.52:504][  2]LogPoseSearch: 6c818e5f72e0650bcaac1c26c2a2af0097681f03 - PSD_Sparse_Stand_Walk_Pivots BeginCache
[2025.06.05-09.15.52:505][  2]LogPoseSearch: a53f5d87ef31e2efb2cffa95e177eb2de30ea68b - PSD_Sparse_Stand_Walk_Loops BeginCache
[2025.06.05-09.15.52:505][  2]LogPoseSearch: 50dc2d6fc66385f69c3f4316520b5453bbe11082 - PSD_Sparse_Stand_Walk_Stops BuildIndex From Cache
[2025.06.05-09.15.52:505][  2]LogPoseSearch: 7bd24e04e6106f1973227ab2f5dd916a0214b232 - PSD_Sparse_Stand_Walk_Starts BuildIndex From Cache
[2025.06.05-09.15.52:506][  2]LogPoseSearch: 6c818e5f72e0650bcaac1c26c2a2af0097681f03 - PSD_Sparse_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.05-09.15.52:506][  2]LogPoseSearch: a53f5d87ef31e2efb2cffa95e177eb2de30ea68b - PSD_Sparse_Stand_Walk_Loops BuildIndex From Cache
[2025.06.05-09.15.52:506][  2]LogPoseSearch: 64befbf5d961ed5ac476d534110da8895e69df96 - PSD_Sparse_Stand_Run_Stops BeginCache
[2025.06.05-09.15.52:508][  2]LogPoseSearch: 64befbf5d961ed5ac476d534110da8895e69df96 - PSD_Sparse_Stand_Run_Stops BuildIndex From Cache
[2025.06.05-09.15.52:508][  2]LogPoseSearch: 2a285cc36d3969680553c96c4cc33908d8d326c8 - PSD_Sparse_Stand_Run_Starts BeginCache
[2025.06.05-09.15.52:509][  2]LogPoseSearch: 2a285cc36d3969680553c96c4cc33908d8d326c8 - PSD_Sparse_Stand_Run_Starts BuildIndex From Cache
[2025.06.05-09.15.52:509][  2]LogPoseSearch: df8620db2cd0e21552cf91b1d3cd5aa199f8e4ba - PSD_Sparse_Stand_Run_Pivots BeginCache
[2025.06.05-09.15.52:511][  2]LogPoseSearch: df8620db2cd0e21552cf91b1d3cd5aa199f8e4ba - PSD_Sparse_Stand_Run_Pivots BuildIndex From Cache
[2025.06.05-09.15.52:511][  2]LogPoseSearch: 20bcf12fcf57878594cfd98261e8196a850d3275 - PSD_Sparse_Stand_Run_Loops BeginCache
[2025.06.05-09.15.52:512][  2]LogPoseSearch: 20bcf12fcf57878594cfd98261e8196a850d3275 - PSD_Sparse_Stand_Run_Loops BuildIndex From Cache
[2025.06.05-09.15.52:512][  2]LogPoseSearch: 093bf032f877970de263de3430d86e0291f0b843 - PSD_Dense_Stand_Walk_Stops BeginCache
[2025.06.05-09.15.52:514][  2]LogPoseSearch: 58635c6253bdbf281b54ac2883681f632df4ac5a - PSD_Dense_Stand_TurnInPlace BeginCache
[2025.06.05-09.15.52:515][  2]LogPoseSearch: aa8f1551ead5ebb7649a9c0cf3f329785019c495 - PSD_Dense_Stand_Run_FromTraversal BeginCache
[2025.06.05-09.15.52:517][  2]LogPoseSearch: 58635c6253bdbf281b54ac2883681f632df4ac5a - PSD_Dense_Stand_TurnInPlace BuildIndex From Cache
[2025.06.05-09.15.52:517][  2]LogPoseSearch: 093bf032f877970de263de3430d86e0291f0b843 - PSD_Dense_Stand_Walk_Stops BuildIndex From Cache
[2025.06.05-09.15.52:517][  2]LogPoseSearch: aa8f1551ead5ebb7649a9c0cf3f329785019c495 - PSD_Dense_Stand_Run_FromTraversal BuildIndex From Cache
[2025.06.05-09.15.52:517][  2]LogPoseSearch: 3c18bdf4eafddb521f27fde8b603d69f34423c6e - PSD_Dense_Stand_Walk_Starts BeginCache
[2025.06.05-09.15.52:518][  2]LogPoseSearch: 3c18bdf4eafddb521f27fde8b603d69f34423c6e - PSD_Dense_Stand_Walk_Starts BuildIndex From Cache
[2025.06.05-09.15.52:519][  2]LogPoseSearch: 75620188eb8a82eb61257a3e37062d404180d1eb - PSD_Dense_Stand_Walk_Pivots BeginCache
[2025.06.05-09.15.52:520][  2]LogPoseSearch: 75620188eb8a82eb61257a3e37062d404180d1eb - PSD_Dense_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.05-09.15.52:520][  2]LogPoseSearch: ba3a46564c5498cbb68507c1ccbbd585a3fc84d4 - PSD_Dense_Stand_Walk_Loops BeginCache
[2025.06.05-09.15.52:521][  2]LogPoseSearch: ba3a46564c5498cbb68507c1ccbbd585a3fc84d4 - PSD_Dense_Stand_Walk_Loops BuildIndex From Cache
[2025.06.05-09.15.52:521][  2]LogPoseSearch: 54f0f90cd23005d37fa1e725d1b27c86bd11c9b3 - PSD_Dense_Stand_Walk_Lands_Light BeginCache
[2025.06.05-09.15.52:522][  2]LogPoseSearch: 54f0f90cd23005d37fa1e725d1b27c86bd11c9b3 - PSD_Dense_Stand_Walk_Lands_Light BuildIndex From Cache
[2025.06.05-09.15.52:523][  2]LogPoseSearch: d9aafe8d3e39205b5f2061763c086eddbfb05fac - PSD_Dense_Stand_Run_Stops BeginCache
[2025.06.05-09.15.52:524][  2]LogPoseSearch: d9aafe8d3e39205b5f2061763c086eddbfb05fac - PSD_Dense_Stand_Run_Stops BuildIndex From Cache
[2025.06.05-09.15.52:524][  2]LogPoseSearch: d8d5fe007588aee9a3da79e9f1a75f582c1b9f99 - PSD_Dense_Stand_Run_Starts BeginCache
[2025.06.05-09.15.52:526][  2]LogPoseSearch: d8d5fe007588aee9a3da79e9f1a75f582c1b9f99 - PSD_Dense_Stand_Run_Starts BuildIndex From Cache
[2025.06.05-09.15.52:526][  2]LogPoseSearch: b53a49cb986c56c8a7f2626bb2d2b7429fbf1afb - PSD_Dense_Stand_Run_Pivots BeginCache
[2025.06.05-09.15.52:528][  2]LogPoseSearch: 570ea0e9de87396e5797a333746ddd45125a84d2 - PSD_Dense_Stand_Run_Loops BeginCache
[2025.06.05-09.15.52:529][  2]LogPoseSearch: 570ea0e9de87396e5797a333746ddd45125a84d2 - PSD_Dense_Stand_Run_Loops BuildIndex From Cache
[2025.06.05-09.15.52:529][  2]LogPoseSearch: 360967ed92817bbce2fc65a7f0c26e89ee4b6373 - PSD_Dense_Stand_Run_Lands_Light BeginCache
[2025.06.05-09.15.52:530][  2]LogPoseSearch: b53a49cb986c56c8a7f2626bb2d2b7429fbf1afb - PSD_Dense_Stand_Run_Pivots BuildIndex From Cache
[2025.06.05-09.15.52:530][  2]LogPoseSearch: 360967ed92817bbce2fc65a7f0c26e89ee4b6373 - PSD_Dense_Stand_Run_Lands_Light BuildIndex From Cache
[2025.06.05-09.15.52:531][  2]LogPoseSearch: 3faefe98948c1fcc929efd51f12856b3b9f3b947 - PSD_Dense_Stand_Idles BeginCache
[2025.06.05-09.15.52:532][  2]LogPoseSearch: 3faefe98948c1fcc929efd51f12856b3b9f3b947 - PSD_Dense_Stand_Idles BuildIndex From Cache
[2025.06.05-09.15.52:532][  2]LogPoseSearch: 914c12febe09d1d5c60f3a968f6f99ac6ee0695d - PSD_Dense_Stand_Idle_Lands_Light BeginCache
[2025.06.05-09.15.52:533][  2]LogPoseSearch: 914c12febe09d1d5c60f3a968f6f99ac6ee0695d - PSD_Dense_Stand_Idle_Lands_Light BuildIndex From Cache
[2025.06.05-09.15.52:538][  2]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-09.15.52:900][  4]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 32.283527
[2025.06.05-09.15.52:901][  4]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.05-09.15.52:902][  4]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 32.452663
[2025.06.05-09.15.53:279][ 16]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Input/IA_Aim.uasset H:/P4/dev/Baoli/Content/Input/IA_Back.uasset H:/P4/dev/Baoli/Content/Input/IA_Crouch.uasset H:/P4/dev/Baoli/Content/Input/IA_Debug.uasset H:/P4/dev/Baoli/Content/Input/IA_Draw.uasset H:/P4/dev/Baoli/Content/Input/IA_Flashlight.uasset H:/P4/dev/Baoli/Content/Input/IA_Interact.uasset H:/P4/dev/Baoli/Content/Input/IA_Jump.uasset H:/P4/dev/Baoli/Content/Input/IA_Lighter.uasset H:/P4/dev/Baoli/Content/Input/IA_LockPick.uasset H:/P4/dev/Baoli/Content/Input/IA_Look.uasset H:/P4/dev/Baoli/Content/Input/IA_Look_Gamepad.uasset'
[2025.06.05-09.15.53:284][ 16]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.05-09.15.53:546][ 45]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 33.089268
[2025.06.05-09.15.53:548][ 45]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 348165457
[2025.06.05-09.15.53:548][ 45]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 33.089268, Update Interval: 321.017487
[2025.06.05-09.17.16:365][825]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.05-09.18.35:255][126]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-09.18.35:262][126]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-09.18.37:040][205]LogSlate: Took 0.000297 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.05-09.18.37:917][243]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/VoiceLines/1.uasset H:/P4/dev/Baoli/Content/VoiceLines/2.uasset H:/P4/dev/Baoli/Content/Assets/Clock/03.uasset H:/P4/dev/Baoli/Content/VoiceLines/3.uasset H:/P4/dev/Baoli/Content/VoiceLines/4.uasset H:/P4/dev/Baoli/Content/VoiceLines/5.uasset H:/P4/dev/Baoli/Content/VoiceLines/6.uasset'
[2025.06.05-09.18.39:064][288]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.05-09.18.39:144][289]LogD3D12RHI: Creating RTPSO with 232 shaders (228 cached, 4 new) took 15.73 ms. Compile time 9.83 ms, link time 5.84 ms.
[2025.06.05-09.18.39:414][299]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.05-09.18.39:948][300]LogD3D12RHI: Creating RTPSO with 233 shaders (1 cached, 0 new) took 469.81 ms. Compile time 0.02 ms, link time 469.74 ms.
[2025.06.05-09.18.40:399][317]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.18.41:988][390]LogAssetEditorSubsystem: Opening Asset editor for ControlRigBlueprint /Game/Characters/BaoliMC/CR_AO.CR_AO
[2025.06.05-09.18.41:988][390]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.05-09.18.42:079][390]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-09.18.42:430][390]LogStreaming: Display: FlushAsyncLoading(356): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-09.18.42:879][390]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.05-09.18.42:918][390]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_3:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.05-09.18.42:918][390]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_3:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.05-09.18.43:130][390]LogUObjectHash: Compacting FUObjectHashTables data took   1.68ms
[2025.06.05-09.18.43:453][390]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-09.18.43:496][390]LogSlate: Window 'Open Asset' being destroyed
[2025.06.05-09.18.43:643][390]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.05-09.18.43:660][390]LogRigVM: Display: Instruction[1] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0001] X:0.000000'
[2025.06.05-09.18.43:660][390]LogRigVM: Display: Instruction[2] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0002] Y:0.000000'
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 14:45:00
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-09.18.43:663][390]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-09.18.43:673][390]LogSlate: Took 0.000234 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.06.05-09.18.43:674][390]LogSlate: Took 0.000135 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.06.05-09.18.43:898][391]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-09.18.43:898][391]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-09.18.43:898][391]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 14:45:00
[2025.06.05-09.18.43:898][391]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-09.18.43:898][391]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-09.18.43:898][391]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-09.18.43:898][391]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-09.18.43:898][391]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-09.18.43:899][391]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-09.18.43:899][391]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-09.18.43:899][391]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-09.18.43:899][391]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-09.18.43:899][391]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-09.18.43:899][391]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-09.18.43:899][391]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-09.18.43:899][391]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-09.18.46:920][535]LogSlate: Window 'CR_AO' being destroyed
[2025.06.05-09.18.50:610][817]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Config/DefaultEditor.ini'
[2025.06.05-09.18.51:411][864]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.06.05-09.18.51:415][865]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.05-09.18.51:472][867]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.06.05-09.18.51:498][868]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.06.05-09.18.52:416][935]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/MetaHumans/Kellan/Face/Kellan_FaceMesh.uasset H:/P4/dev/Baoli/Content/Main/Asset/Almirah/AlmirahDoors/LeftDoorHandle.uasset'
[2025.06.05-09.18.53:738][ 35]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/MetaHumans/Common/Common/Mocap/m_med_nrw_body_mocap.uasset'
[2025.06.05-09.18.54:251][ 80]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/MetaHumans/Common/Common/m_med_nrw_body_preview.uasset'
[2025.06.05-09.18.54:783][126]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/MetaHumans/MH_Friend/Body/m_med_nrw_body.uasset'
[2025.06.05-09.18.54:839][132]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.06.05-09.18.54:962][134]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.06.05-09.18.55:012][135]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.06.05-09.18.55:115][140]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.06.05-09.18.55:222][145]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.06.05-09.18.55:247][146]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.06.05-09.18.55:407][148]LogTexture: Display: Building textures: /Game/MetaHumans/Common/Materials/Jeans/btm_jeans_nrm_Mask.btm_jeans_nrm_Mask (TFO_AutoDXT, 4096x4096 x1x1x1) (Required Memory Estimate: 1152.999984 MB), EncodeSpeed: Final
[2025.06.05-09.18.55:601][149]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_3:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.05-09.18.55:747][149]LogUObjectHash: Compacting FUObjectHashTables data took   1.53ms
[2025.06.05-09.18.55:797][149]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.18.55:811][150]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.06.05-09.18.55:826][150]LogRigVM: Display: Instruction[1] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0001] X:0.000000'
[2025.06.05-09.18.55:826][150]LogRigVM: Display: Instruction[2] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0002] Y:0.000000'
[2025.06.05-09.18.56:029][155]LogTexture: Display: Building textures: /Game/MetaHumans/Common/Materials/Jeans/btm_jeans_nrm_D.btm_jeans_nrm_D (TFO_AutoDXT, 4096x4096 x1x1x1) (Required Memory Estimate: 1120.999983 MB), EncodeSpeed: Final
[2025.06.05-09.18.56:624][184]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.18.56:765][190]LogTexture: Display: Building textures: /Game/MetaHumans/Common/Materials/RunningShoes/shs_runningshoes_AO.shs_runningshoes_AO (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.18.56:843][190]LogTexture: Display: Building textures: /Game/MetaHumans/Common/Materials/RunningShoes/shs_runningshoes_Mask.shs_runningshoes_Mask (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.18.56:941][190]LogTexture: Display: Building textures: /Game/MetaHumans/Common/Materials/Textures/Clothing/Macros/macro_heather_04_normal.macro_heather_04_normal (TFO_BC5, 2048x2048 x1x1x1) (Required Memory Estimate: 562.499988 MB), EncodeSpeed: Final
[2025.06.05-09.18.57:073][190]LogTexture: Display: Building textures: /Game/MetaHumans/Common/Materials/Jeans/btm_jeans_nrm_N.btm_jeans_nrm_N (TFO_BC5, 4096x4096 x1x1x1) (Required Memory Estimate: 1234.499988 MB), EncodeSpeed: Final
[2025.06.05-09.19.00:854][482]LogRigVM: Display: Instruction[1] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0001] X:0.000000'
[2025.06.05-09.19.00:854][482]LogRigVM: Display: Instruction[2] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0002] Y:0.000000'
[2025.06.05-09.19.04:043][757]LogRigVM: Display: Instruction[1] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0001] X:0.000000'
[2025.06.05-09.19.04:043][757]LogRigVM: Display: Instruction[2] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0002] Y:0.000000'
[2025.06.05-09.19.04:533][796]LogRigVM: Display: Instruction[1] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0001] X:0.000000'
[2025.06.05-09.19.04:533][796]LogRigVM: Display: Instruction[2] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0002] Y:0.000000'
[2025.06.05-09.19.05:054][841]LogRigVM: Display: Instruction[1] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0001] X:0.000000'
[2025.06.05-09.19.05:054][841]LogRigVM: Display: Instruction[2] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0002] Y:0.000000'
[2025.06.05-09.19.05:556][883]LogRigVM: Display: Instruction[1] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0001] X:0.000000'
[2025.06.05-09.19.05:556][883]LogRigVM: Display: Instruction[2] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0002] Y:0.000000'
[2025.06.05-09.19.06:120][932]LogRigVM: Display: Instruction[1] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0001] X:0.000000'
[2025.06.05-09.19.06:121][932]LogRigVM: Display: Instruction[2] 'DISPATCH_RigVMDispatch_Print::Prefix:FString,Value:double,Enabled:bool,ScreenDuration:float,ScreenColor:FLinearColor': 'ControlRig_VM[0002] Y:0.000000'
[2025.06.05-09.19.09:128][212]LogUObjectHash: Compacting FUObjectHashTables data took   1.65ms
[2025.06.05-09.19.37:719][ 97]LogUObjectHash: Compacting FUObjectHashTables data took   1.75ms
[2025.06.05-09.19.37:750][ 97]LogStreaming: Display: FlushAsyncLoading(585): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-09.19.37:833][ 97]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Sphere_solid) ...
[2025.06.05-09.20.01:725][336]LogUObjectHash: Compacting FUObjectHashTables data took   1.73ms
[2025.06.05-09.20.41:767][  3]LogStreaming: Display: FlushAsyncLoading(587): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-09.20.41:793][  3]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/ControlRig/Controls/ControlRig_Arrow4_solid) ...
[2025.06.05-09.20.51:269][825]LogEditorTransaction: Undo Change Numeric Value
[2025.06.05-09.21.14:568][794]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 354.119110
[2025.06.05-09.21.14:848][819]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-09.21.14:848][819]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 354.387604, Update Interval: 343.946655
[2025.06.05-09.21.22:281][601]LogUObjectHash: Compacting FUObjectHashTables data took   2.27ms
[2025.06.05-09.21.38:090][902]LogEditorTransaction: Undo Move Control
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: ================================================
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Total job queries 491, among them cache hits 76 (15.48%), DDC hits 404 (82.28%), Duplicates 9 (1.83%)
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Tracking 406 distinct input hashes that result in 267 distinct outputs (65.76%)
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: RAM used: 153.34 KiB of 3.20 GiB budget. Usage: 0.00%
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Shaders Compiled: 2
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Jobs assigned 2, completed 2 (100%)
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Average time worker was idle: 43.10 s
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Time job spent in pending queue: average 0.06 s, longest 0.07 s
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Job execution time: average 2.32 s, max 3.23 s
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Job life time (pending + execution): average 2.38 s, max 3.30
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Shader code size: average 9.879 KiB, min 5.906 KiB, max 13.852 KiB
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 4.77 s
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.05%
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Jobs were issued in 2 batches (only local compilation was used), average 1.00 jobs/batch
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Average processing rate: 0.42 jobs/sec
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Total thread time: 0.921 s
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Total thread preprocess time: 8.42 s
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Percentage time preprocessing: 914.66%
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Effective parallelization: 0.19 (times faster than compiling all shaders on one thread). Compare with number of workers: 16
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.06.05-09.21.50:413][899]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy (compiled    2 times, average 0.46 sec, max 0.52 sec, min 0.40 sec)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display:             TShadowDepthVSVertexShadowDepth_VirtualShadowMap (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display:              TShadowDepthPSPixelShadowDepth_VirtualShadowMap (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display:                                             FDebugViewModeVS (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 100.00% of total time (compiled    2 times, average 0.46 sec, max 0.52 sec, min 0.40 sec)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display:              TShadowDepthPSPixelShadowDepth_VirtualShadowMap - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display:                                 TBasePassVSFNoLightMapPolicy - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display:                                             FDebugViewModeVS - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display:                                 TBasePassCSFNoLightMapPolicy - 0.00% of total time (compiled    0 times, average 0.00 sec, max 0.00 sec, min 0.00 sec)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display: === Material stats ===
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display: Materials Cooked:        0
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display: Materials Translated:    248
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display: Material Total Translate Time: 0.17 s
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display: Material Translation Only: 0.10 s (55%)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display: Material DDC Serialization Only: 0.01 s (4%)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display: Material Cache Hits: 96 (39%)
[2025.06.05-09.21.50:414][899]LogShaderCompilers: Display: ================================================
[2025.06.05-09.21.57:445][460]LogUObjectHash: Compacting FUObjectHashTables data took   1.61ms
[2025.06.05-09.21.59:037][460]LogSlate: Window 'Save Content' being destroyed
[2025.06.05-09.21.59:060][460]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.22.00:408][460]LogSlate: Window 'Check Out Assets' being destroyed
[2025.06.05-09.22.00:424][460]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.22.00:489][460]LogSourceControl: Attempting 'p4 edit H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.22.00:518][460]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.05-09.22.00:554][460]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Characters/BaoliMC/CR_AO] ([2] browsable assets)...
[2025.06.05-09.22.00:554][460]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.06.05-09.22.00:638][460]OBJ SavePackage:     Rendered thumbnail for [ControlRigBlueprint /Game/Characters/BaoliMC/CR_AO.CR_AO]
[2025.06.05-09.22.00:638][460]OBJ SavePackage: Finished generating thumbnails for package [/Game/Characters/BaoliMC/CR_AO]
[2025.06.05-09.22.00:638][460]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Characters/BaoliMC/CR_AO" FILE="H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset" SILENT=true
[2025.06.05-09.22.00:648][460]LogSavePackage: Moving output files for package: /Game/Characters/BaoliMC/CR_AO
[2025.06.05-09.22.00:648][460]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/CR_AOC7C7C13243F7445551701E896B4C26AB.tmp' to 'H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.22.00:746][460]LogFileHelpers: InternalPromptForCheckoutAndSave took 228.672 ms
[2025.06.05-09.22.00:787][460]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.05-09.22.00:787][460]LogContentValidation: Enabled validators:
[2025.06.05-09.22.00:787][460]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.05-09.22.00:787][460]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.05-09.22.00:787][460]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.05-09.22.00:787][460]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.05-09.22.00:787][460]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.05-09.22.00:787][460]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.05-09.22.00:787][460]LogContentValidation: 	/Script/GameFeaturesEditor.IllegalPluginDependenciesValidator
[2025.06.05-09.22.00:787][460]LogContentValidation: 	/Script/AssetReferenceRestrictions.EditorValidator_PluginAssetReferences
[2025.06.05-09.22.00:787][460]LogContentValidation: 	/Script/AssetReferenceRestrictions.AssetValidator_AssetReferenceRestrictions
[2025.06.05-09.22.00:787][460]AssetCheck: /Game/Characters/BaoliMC/CR_AO Validating asset
[2025.06.05-09.22.24:198][482]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.22.25:197][592]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.25.01:873][545]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.25.50:302][881]LogUObjectHash: Compacting FUObjectHashTables data took   1.93ms
[2025.06.05-09.25.50:306][881]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Levels/DefaultLevel' took 0.036
[2025.06.05-09.25.50:306][881]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Characters/BaoliMC/CR_AO] ([2] browsable assets)...
[2025.06.05-09.25.50:313][881]OBJ SavePackage:     Rendered thumbnail for [ControlRigBlueprint /Game/Characters/BaoliMC/CR_AO.CR_AO]
[2025.06.05-09.25.50:313][881]OBJ SavePackage: Finished generating thumbnails for package [/Game/Characters/BaoliMC/CR_AO]
[2025.06.05-09.25.50:313][881]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Characters/BaoliMC/CR_AO" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Characters/BaoliMC/CR_AO_Auto1.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-09.25.50:325][881]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Characters/BaoliMC/CR_AO_Auto1
[2025.06.05-09.25.50:326][881]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/CR_AO_Auto1476439A345C8071A6AC385BE057169AA.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Characters/BaoliMC/CR_AO_Auto1.uasset'
[2025.06.05-09.25.50:349][881]LogFileHelpers: Auto-saving content packages took 0.042
[2025.06.05-09.25.51:366][974]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.27.00:007][380]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid primary target.'
[2025.06.05-09.27.00:914][467]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 700.463257
[2025.06.05-09.27.01:187][498]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-09.27.01:187][498]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 700.726990, Update Interval: 328.003174
[2025.06.05-09.27.02:538][636]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid primary target.'
[2025.06.05-09.27.02:539][636]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.27.08:797][302]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.27.10:125][447]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.27.26:235][829]LogUObjectHash: Compacting FUObjectHashTables data took   1.66ms
[2025.06.05-09.27.26:263][829]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.27.38:468][539]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.06.05-09.27.38:559][540]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.06.05-09.27.38:605][541]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.06.05-09.27.38:654][542]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.06.05-09.27.38:699][543]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.06.05-09.27.38:745][544]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.06.05-09.27.38:792][545]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_22
[2025.06.05-09.27.38:838][546]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_23
[2025.06.05-09.27.38:986][554]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_24
[2025.06.05-09.27.39:120][556]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_25
[2025.06.05-09.27.39:405][563]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.uasset H:/P4/dev/Baoli/Content/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.uasset H:/P4/dev/Baoli/Content/Blueprints/Diary/Mesh/BookFlipped1.uasset H:/P4/dev/Baoli/Content/Characters/Animations/CasRetarget/Cas.uasset H:/P4/dev/Baoli/Content/MetaHumans/Common/Common/Mocap/CASm_med_nrw_body_mocap.uasset H:/P4/dev/Baoli/Content/Sequences/V2/Animations/ChairFinal.uasset H:/P4/dev/Baoli/Content/Levels/_GENERATED/Anshul/curtain.uasset H:/P4/dev/Baoli/Content/MetaHumans/Common/Female/Medium/NormalWeight/Body/f_med_nrw_body.uasset H:/P4/dev/Baoli/Content/MetaHumans/Common/Common/Mocap/f_med_nrw_body_mocap.uasset H:/P4/dev/Baoli/Content/MetaHumans/Common/Common/f_med_nrw_preview.uasset H:/P4/dev/Baoli/Content/MetaHumans/Common/Female/Medium/NormalWeight/Shoes/CasualSneakers/f_med_nrw_shs_casualsneakers.uasset'
[2025.06.05-09.27.39:422][563]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_26
[2025.06.05-09.27.39:500][565]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_27
[2025.06.05-09.27.39:611][568]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_28
[2025.06.05-09.27.39:992][578]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/MetaHumans/Common/Female/Medium/NormalWeight/Shoes/Flipflops/f_med_nrw_shs_flipflops.uasset'
[2025.06.05-09.27.40:287][587]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_29
[2025.06.05-09.27.40:375][589]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_30
[2025.06.05-09.27.40:425][590]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_31
[2025.06.05-09.27.40:548][592]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_32
[2025.06.05-09.27.40:690][596]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_33
[2025.06.05-09.27.40:954][602]LogTexture: Display: Building textures: /Game/MetaHumans/Common/FemaleHair/Textures/Eyelashes_L_SlightCurl_Cards/Eyelashes_L_SlightCurl_Coverage.Eyelashes_L_SlightCurl_Coverage (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.41:056][602]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceRoughness_MAIN.FaceRoughness_MAIN (TFO_AutoDXT, 1024x1024 x1x1x1) (Required Memory Estimate: 170.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.41:130][603]LogStreaming: Display: FlushAsyncLoading(588): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-09.27.41:150][603]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/Characters/Animations/CasRetarget/Cas.Cas
[2025.06.05-09.27.41:189][603]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_34
[2025.06.05-09.27.41:199][603]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_34:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-09.27.41:216][603]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-09.27.41:480][603]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-09.27.41:811][603]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_35
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 14:57:14
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-09.27.41:824][603]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-09.27.42:115][604]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_36
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 14:57:14
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-09.27.42:125][604]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-09.27.42:308][609]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceNormal_WM2.FaceNormal_WM2 (TFO_BC5, 2048x2048 x1x1x1) (Required Memory Estimate: 562.499988 MB), EncodeSpeed: Final
[2025.06.05-09.27.42:416][609]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceNormal_WM1.FaceNormal_WM1 (TFO_BC5, 2048x2048 x1x1x1) (Required Memory Estimate: 562.499988 MB), EncodeSpeed: Final
[2025.06.05-09.27.42:507][609]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceNormal_WM3.FaceNormal_WM3 (TFO_BC5, 2048x2048 x1x1x1) (Required Memory Estimate: 562.499988 MB), EncodeSpeed: Final
[2025.06.05-09.27.42:688][616]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceColor_MAIN.FaceColor_MAIN (TFO_AutoDXT, 1024x1024 x1x1x1) (Required Memory Estimate: 170.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.42:735][616]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceNormal_MAIN.FaceNormal_MAIN (TFO_BC5, 2048x2048 x1x1x1) (Required Memory Estimate: 562.499988 MB), EncodeSpeed: Final
[2025.06.05-09.27.42:796][616]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceColor_CM3.FaceColor_CM3 (TFO_AutoDXT, 1024x1024 x1x1x1) (Required Memory Estimate: 170.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.42:843][616]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceEyeLipstickMask.FaceEyeLipstickMask (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.42:894][616]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceColor_CM1.FaceColor_CM1 (TFO_AutoDXT, 1024x1024 x1x1x1) (Required Memory Estimate: 170.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.42:926][616]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceColor_CM2.FaceColor_CM2 (TFO_AutoDXT, 1024x1024 x1x1x1) (Required Memory Estimate: 170.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.42:980][616]LogTexture: Display: Building textures: /Game/MetaHumans/Common/Face/Textures/IrisTextures/iris_009/T_Iris_A_M.T_Iris_A_M (BGRA8, 1024x1024 x1x1x1) (Required Memory Estimate: 36.999992 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:009][616]LogTexture: Display: Building textures: /Game/MetaHumans/Common/Face/Textures/IrisTextures/iris_009/T_Iris_A_H.T_Iris_A_H (G8, 1024x1024 x1x1x1) (Required Memory Estimate: 29.999993 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:055][616]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceFoundationBlusherMask.FaceFoundationBlusherMask (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:136][616]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceCavity_MAIN.FaceCavity_MAIN (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:224][616]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceColor_MAIN.FaceColor_MAIN (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:302][616]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceEyeLipstickMask.FaceEyeLipstickMask (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:363][616]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceFoundationBlusherMask.FaceFoundationBlusherMask (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:514][623]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceBakedGroomRootTipGradientRegionMasks.FaceBakedGroomRootTipGradientRegionMasks (BGRA8, 2048x2048 x1x1x1) (Required Memory Estimate: 144.999992 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:669][630]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_Child/Face/FaceBakedGroomAttributeMap.FaceBakedGroomAttributeMap (BGRA8, 2048x2048 x1x1x1) (Required Memory Estimate: 144.999992 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:742][630]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceColor_CM3.FaceColor_CM3 (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:799][630]LogTexture: Display: Building textures: /Game/MetaHumans/Kellan/Body/Textures/T_Body_Specular.T_Body_Specular (TFO_DXT1, 1024x1024 x1x1x1) (Required Memory Estimate: 122.708323 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:863][630]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceColor_CM2.FaceColor_CM2 (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.43:924][630]LogTexture: Display: Building textures: /Game/MetaHumans/Kellan/Body/Textures/T_Body_BaseColor.T_Body_BaseColor (TFO_DXT1, 1024x1024 x1x1x1) (Required Memory Estimate: 122.708323 MB), EncodeSpeed: Final
[2025.06.05-09.27.44:850][637]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceRoughness_MAIN.FaceRoughness_MAIN (TFO_AutoDXT, 4096x4096 x1x1x1) (Required Memory Estimate: 1152.999984 MB), EncodeSpeed: Final
[2025.06.05-09.27.45:848][637]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceColor_CM1.FaceColor_CM1 (TFO_AutoDXT, 2048x2048 x1x1x1) (Required Memory Estimate: 351.083323 MB), EncodeSpeed: Final
[2025.06.05-09.27.45:904][637]LogTexture: Display: Building textures: /Game/MetaHumans/Kellan/Body/Textures/T_Body_Normal.T_Body_Normal (TFO_BC5, 1024x1024 x1x1x1) (Required Memory Estimate: 304.499988 MB), EncodeSpeed: Final
[2025.06.05-09.27.46:719][644]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceNormal_WM3.FaceNormal_WM3 (TFO_BC5, 8192x8192 x1x1x1) (Required Memory Estimate: 4608.999984 MB), EncodeSpeed: Final
[2025.06.05-09.27.48:241][651]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceNormal_WM2.FaceNormal_WM2 (TFO_BC5, 8192x8192 x1x1x1) (Required Memory Estimate: 4608.999984 MB), EncodeSpeed: Final
[2025.06.05-09.27.49:891][672]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceNormal_WM1.FaceNormal_WM1 (TFO_BC5, 8192x8192 x1x1x1) (Required Memory Estimate: 4608.999984 MB), EncodeSpeed: Final
[2025.06.05-09.27.51:219][679]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceNormal_MAIN.FaceNormal_MAIN (TFO_BC5, 8192x8192 x1x1x1) (Required Memory Estimate: 4608.999984 MB), EncodeSpeed: Final
[2025.06.05-09.27.52:621][686]LogTexture: Display: Building textures: /Game/MetaHumans/Baoli_MC/Face/FaceCavity_MAIN.FaceCavity_MAIN (TFO_AutoDXT, 8192x8192 x1x1x1) (Required Memory Estimate: 4608.999984 MB), EncodeSpeed: Final
[2025.06.05-09.27.53:723][717]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_37
[2025.06.05-09.29.15:440][ 84]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.30.16:850][977]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.30.30:987][131]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.30.32:825][277]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.30.34:434][403]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.30.34:434][403]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.30.51:404][641]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.32.36:977][874]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1036.529663
[2025.06.05-09.32.37:250][894]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-09.32.37:250][895]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1036.789795, Update Interval: 340.143433
[2025.06.05-09.35.11:114][225]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Config/DefaultEditor.ini'
[2025.06.05-09.35.16:980][746]LogSlate: Took 0.010282 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensedItalic.ttf' (160K)
[2025.06.05-09.35.20:348][105]LogBlueprint: Error: [AssetLog] H:\P4\dev\Baoli\Content\Characters\BaoliMC\CR_AO.uasset: [Compiler] Variable Node  Get AO_ValueX  is ill-formed (pin type doesn't match the variable type).
Consider to recreate the node.
[2025.06.05-09.35.20:348][105]LogScript: Error: Script Msg: Variable Node @@ is ill-formed (pin type doesn't match the variable type).
Consider to recreate the node.
[2025.06.05-09.35.20:348][105]88D59B184EC29D20FCA7CF923704659F_CR_AO_CompilerResultsLog: Error: Variable Node  Get AO_ValueX  is ill-formed (pin type doesn't match the variable type).
Consider to recreate the node.
[2025.06.05-09.35.33:131][303]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.35.33:131][303]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.35.36:367][603]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.35.36:367][603]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.35.42:284][168]LogRigVM: Warning: Instruction[1] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.35.42:284][168]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.35.43:992][322]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\ABP_SandboxCharacter.uasset: [Compiler] Invalid field '__CustomProperty_AO_ValueX_F58E350B4A32BFAAC901D6B58A6DA640' found in property path for  Control Rig
[2025.06.05-09.35.43:992][322]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\ABP_SandboxCharacter.uasset: [Compiler] Invalid field '__CustomProperty_AO_ValueY_F58E350B4A32BFAAC901D6B58A6DA640' found in property path for  Control Rig
[2025.06.05-09.35.44:006][322]LogClass: Warning: SerializeFromMismatchedTag failed: Type mismatch in AimAO - Previous (DoubleProperty) Current(StructProperty(Vector(/Script/CoreUObject))) in package: FObjectReader
[2025.06.05-09.35.44:008][322]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Characters\BaoliMC\CR_AO.uasset: [Compiler] Can't parse default value '0.000000' for  . Property: AimAO.
[2025.06.05-09.35.44:105][322]LogUObjectHash: Compacting FUObjectHashTables data took   5.41ms
[2025.06.05-09.35.44:273][323]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.35.46:735][594]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.35.46:735][594]LogRigVM: Warning: Instruction[2] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.35.50:177][926]LogUObjectHash: Compacting FUObjectHashTables data took   1.71ms
[2025.06.05-09.35.50:226][927]LogRigVM: Warning: Instruction[2] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.35.51:433][ 53]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.36.06:979][308]LogUObjectHash: Compacting FUObjectHashTables data took   1.72ms
[2025.06.05-09.36.07:187][309]LogRigVM: Warning: Instruction[2] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.36.32:031][590]LogUObjectHash: Compacting FUObjectHashTables data took   2.08ms
[2025.06.05-09.36.32:063][590]LogRigVM: Warning: Instruction[2] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.36.39:564][935]LogSourceControl: Attempting 'p4 fstat -Or D:/UE_5.5/Engine/Content/EditorMeshes/SkeletalMesh/DefaultSkeletalMesh.uasset'
[2025.06.05-09.36.39:566][935]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-09.36.39:634][936]SourceControl: CommandMessage Command: UpdateStatus, Info: Path 'D:/UE_5.5/Engine/Content/EditorMeshes/SkeletalMesh/DefaultSkeletalMesh.uasset' is not under client's root 'H:\P4\dev'.

[2025.06.05-09.36.39:827][942]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-09.36.40:047][949]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.05-09.36.40:244][956]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-09.36.46:434][190]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_38
[2025.06.05-09.36.46:443][190]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.36.46:444][190]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.36.46:532][191]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_39
[2025.06.05-09.36.46:541][191]LogActor: Warning: BP_Almirah_C /Engine/Transient.World_39:PersistentLevel.BP_Almirah_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.05-09.36.46:583][192]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_40
[2025.06.05-09.36.46:758][196]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.36.46:758][196]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.36.46:852][199]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_41
[2025.06.05-09.36.46:895][200]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_42
[2025.06.05-09.36.47:138][205]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.36.47:139][205]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.36.47:312][210]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_43
[2025.06.05-09.36.47:322][210]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_43:PersistentLevel.CBP_SandboxCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.36.47:322][210]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_43:PersistentLevel.CBP_SandboxCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.36.47:487][214]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.36.47:489][214]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.36.47:681][220]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_44
[2025.06.05-09.36.47:689][220]LogActor: Warning: BP_Bed_C /Engine/Transient.World_44:PersistentLevel.BP_Bed_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.05-09.37.16:239][537]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Almirah.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bed.uasset'
[2025.06.05-09.37.17:490][575]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_Bulb.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_ChestActorPawn.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Fan.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_FlashLight.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameMinimal.uasset'
[2025.06.05-09.37.17:651][579]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.17:651][579]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.17:735][580]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.17:736][580]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.17:772][581]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.17:773][581]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.17:948][589]LogAssetEditorSubsystem: Opening Asset editor for AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter
[2025.06.05-09.37.17:948][589]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_45
[2025.06.05-09.37.17:957][589]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_46
[2025.06.05-09.37.17:965][589]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-09.37.17:968][589]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.17:969][589]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.18:030][589]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-09.37.18:075][589]LogStreaming: Display: FlushAsyncLoading(589): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-09.37.21:332][589]LogBlueprintEditor: Perf: 3.3 total seconds to load all 13 blueprint libraries in project. Avoid references to content in blueprint libraries to shorten this time.
[2025.06.05-09.37.21:332][589]LogBlueprintEditor: Perf: 3.1 seconds loading: /Game/UltraDynamicSky/Blueprints/Functions/UltraDynamicWeather_Functions
[2025.06.05-09.37.21:618][589]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_47
[2025.06.05-09.37.21:657][589]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-09.37.22:042][589]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_7
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.22:042][589]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_7
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.22:065][589]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 15:02:14
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-09.37.22:087][589]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-09.37.22:246][590]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.22:251][590]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-09.37.22:251][590]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-09.37.22:251][590]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 15:02:14
[2025.06.05-09.37.22:251][590]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-09.37.22:251][590]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-09.37.22:251][590]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-09.37.22:251][590]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-09.37.22:251][590]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-09.37.22:251][590]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-09.37.22:251][590]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-09.37.22:251][590]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-09.37.22:252][590]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-09.37.22:252][590]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-09.37.22:252][590]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-09.37.22:252][590]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-09.37.22:252][590]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-09.37.22:266][591]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.22:291][592]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.22:303][593]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.22:316][594]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.22:328][595]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.22:342][596]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.22:355][597]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.22:367][598]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.35:553][624]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-09.37.36:559][697]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-09.37.42:247][ 98]LogUObjectHash: Compacting FUObjectHashTables data took   3.10ms
[2025.06.05-09.37.42:303][ 98]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_11
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.42:320][ 98]LogOutputDevice: Warning: 

Script Stack (0 frames) :

[2025.06.05-09.37.42:411][ 98]LogStats: FPlatformStackWalk::StackWalkAndDump -  0.090 s
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: === Handled ensure: ===
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: 
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: Ensure condition failed: StructProperty->Struct == TBaseStructure<FTransform>::Get()  [File:D:\build\++UE5\Sync\Engine\Plugins\Animation\ControlRig\Source\ControlRig\Private\AnimNode_ControlRig.cpp] [Line: 744] 
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: 
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: Stack: 
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd2c98472d UnrealEditor-ControlRig.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd2c944055 UnrealEditor-ControlRig.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a5400ad UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd56f8b60e UnrealEditor-AnimGraphRuntime.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd56fee0d3 UnrealEditor-AnimGraphRuntime.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd2b788ce9 UnrealEditor-PoseSearch.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd2c944242 UnrealEditor-ControlRig.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd2c9440c3 UnrealEditor-ControlRig.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd56fee0d3 UnrealEditor-AnimGraphRuntime.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd56fee0d3 UnrealEditor-AnimGraphRuntime.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a540158 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a53feb8 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a46983e UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a4dcb57 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a4db74e UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a4b66fd UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6a4dc0d0 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6ac6db96 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6ac6e066 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6ac453a6 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6ac61a51 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd678a1b0e UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd67863dbb UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6741e9ac UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6666ebbf UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd666bd0aa UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd666e4cd8 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd66ce939b UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd6c44ce75 UnrealEditor-Engine.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd66d23d3a UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffd679b42c6 UnrealEditor-UnrealEd.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ff66f6b6b47 UnrealEditor.exe!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ff66f6d57ac UnrealEditor.exe!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ff66f6d589a UnrealEditor.exe!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ff66f6d9114 UnrealEditor.exe!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ff66f6ebd04 UnrealEditor.exe!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ff66f6ef0ba UnrealEditor.exe!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffea7a2259d KERNEL32.DLL!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: [Callstack] 0x00007ffea944af38 ntdll.dll!UnknownFunction []
[2025.06.05-09.37.42:411][ 98]LogOutputDevice: Error: 
[2025.06.05-09.37.42:413][ 98]LogStats:                SubmitErrorReport -  0.000 s
[2025.06.05-09.37.43:381][ 98]LogStats:                    SendNewReport -  0.969 s
[2025.06.05-09.37.43:382][ 98]LogStats:             FDebug::EnsureFailed -  1.061 s
[2025.06.05-09.37.43:385][ 98]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_11
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.43:741][ 99]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.43:742][ 99]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.43:773][100]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_13
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.37.43:774][100]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_13
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.38.30:785][ 71]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1390.341187
[2025.06.05-09.38.31:056][ 94]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-09.38.31:057][ 94]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1390.599609, Update Interval: 343.228851
[2025.06.05-09.39.05:310][383]LogUObjectHash: Compacting FUObjectHashTables data took   1.94ms
[2025.06.05-09.39.05:317][383]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/Levels/DefaultLevel' took 0.054
[2025.06.05-09.39.05:317][383]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Blueprints/ABP_SandboxCharacter] ([2] browsable assets)...
[2025.06.05-09.39.05:325][383]OBJ SavePackage:     Rendered thumbnail for [AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter]
[2025.06.05-09.39.05:325][383]OBJ SavePackage: Finished generating thumbnails for package [/Game/Blueprints/ABP_SandboxCharacter]
[2025.06.05-09.39.05:325][383]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Blueprints/ABP_SandboxCharacter" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto2.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-09.39.05:468][383]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto2
[2025.06.05-09.39.05:468][383]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/ABP_SandboxCharacter_Auto2722919B648221B8F5E6FAFBE3C2A3CFA.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Blueprints/ABP_SandboxCharacter_Auto2.uasset'
[2025.06.05-09.39.05:470][383]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Characters/BaoliMC/CR_AO] ([2] browsable assets)...
[2025.06.05-09.39.05:476][383]OBJ SavePackage:     Rendered thumbnail for [ControlRigBlueprint /Game/Characters/BaoliMC/CR_AO.CR_AO]
[2025.06.05-09.39.05:476][383]OBJ SavePackage: Finished generating thumbnails for package [/Game/Characters/BaoliMC/CR_AO]
[2025.06.05-09.39.05:476][383]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Characters/BaoliMC/CR_AO" FILE="H:/P4/dev/Baoli/Saved/Autosaves/Game/Characters/BaoliMC/CR_AO_Auto2.uasset" SILENT=false AUTOSAVING=true
[2025.06.05-09.39.05:487][383]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/Characters/BaoliMC/CR_AO_Auto2
[2025.06.05-09.39.05:487][383]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/CR_AO_Auto290FDBC35401EA9AC3F51E7BC1E6AF1DA.tmp' to 'H:/P4/dev/Baoli/Saved/Autosaves/Game/Characters/BaoliMC/CR_AO_Auto2.uasset'
[2025.06.05-09.39.05:600][383]LogFileHelpers: Auto-saving content packages took 0.284
[2025.06.05-09.39.06:618][481]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.41.09:406][975]LogEditorTransaction: Undo Move Control
[2025.06.05-09.41.09:839][975]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_34:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-09.41.09:908][975]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_46:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-09.41.10:053][977]LogRigVM: Warning: Instruction[2] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.41.11:733][126]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Config/DefaultEditor.ini'
[2025.06.05-09.42.50:479][571]LogRigVM: Warning: Instruction[2] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.42.50:479][571]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.43.02:659][553]LogUObjectHash: Compacting FUObjectHashTables data took   1.93ms
[2025.06.05-09.43.02:701][553]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_33
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.43.02:702][553]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_33
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.43.02:756][553]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_34
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.43.02:756][553]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_34
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.43.02:795][555]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_35
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.43.02:796][555]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_35
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.43.02:802][555]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.44.06:663][174]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.44.40:019][243]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1759.562744
[2025.06.05-09.44.40:295][267]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-09.44.40:295][267]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1759.829956, Update Interval: 308.139282
[2025.06.05-09.45.03:833][471]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.45.06:685][719]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.45.08:076][834]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.45.08:076][834]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.45.13:045][280]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.45.13:045][280]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.45.13:045][280]LogRigVM: Warning: Instruction[5] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.45.32:794][141]LogRigVM: Warning: Instruction[3] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.45.32:794][141]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.45.32:794][141]LogRigVM: Warning: Instruction[5] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.45.36:893][499]LogUObjectHash: Compacting FUObjectHashTables data took   2.17ms
[2025.06.05-09.45.36:954][499]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_36
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.45.36:955][499]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_36
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.45.37:010][499]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_37
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.45.37:011][499]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_37
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.45.37:091][501]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_38
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.45.37:092][501]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_38
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.45.37:097][501]LogRigVM: Warning: Instruction[5] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.46.44:083][200]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\ABP_SandboxCharacter.uasset: [Compiler] Function 'Get_AOValue' has too many parameters in property path for  Blendspace Player 'BS_Neutral_AO_Stand'
[2025.06.05-09.46.44:083][200]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\ABP_SandboxCharacter.uasset: [Compiler] Function 'Get_AOValue' has too many parameters in property path for  Blendspace Player 'BS_Neutral_AO_Stand'
[2025.06.05-09.46.44:270][200]LogUObjectHash: Compacting FUObjectHashTables data took   1.96ms
[2025.06.05-09.46.44:302][200]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_69
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.46.44:302][200]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_69
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.46.44:309][200]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_70
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.46.44:310][200]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_70
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.46.44:385][202]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_71
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.46.44:386][202]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_71
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.47.11:253][477]LogUObjectHash: Compacting FUObjectHashTables data took   2.17ms
[2025.06.05-09.47.11:290][477]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_78
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.47.11:291][477]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_78
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.47.11:297][477]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_79
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.47.11:298][477]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_79
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.47.11:379][479]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_80
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.47.11:379][479]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_80
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.48.23:537][430]LogBlueprint: Error: [AssetLog] H:\P4\dev\Baoli\Content\Characters\BaoliMC\CR_AO.uasset: [Compiler] Variable Node  Get AimAO  is ill-formed (pin type doesn't match the variable type).
Consider to recreate the node.
[2025.06.05-09.48.23:537][430]LogScript: Error: Script Msg: Variable Node @@ is ill-formed (pin type doesn't match the variable type).
Consider to recreate the node.
[2025.06.05-09.48.23:538][430]88D59B184EC29D20FCA7CF923704659F_CR_AO_CompilerResultsLog: Error: Variable Node  Get AimAO  is ill-formed (pin type doesn't match the variable type).
Consider to recreate the node.
[2025.06.05-09.48.35:577][473]LogRigVM: Warning: Instruction[5] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.48.35:577][473]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.48.38:573][734]LogRigVM: Warning: Instruction[5] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.48.38:573][734]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.48.41:224][954]LogClass: Warning: Type mismatch in AimAO_X of CR_AO_C - Previous (StructProperty) Current(DoubleProperty) in package: FObjectReader
[2025.06.05-09.48.41:226][954]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Characters\BaoliMC\CR_AO.uasset: [Compiler] Can't parse default value '0.000000,0.000000,0.000000' for  . Property: AimAO_X.
[2025.06.05-09.48.41:383][954]LogUObjectHash: Compacting FUObjectHashTables data took   2.81ms
[2025.06.05-09.48.41:426][954]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_81
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.48.41:427][954]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_81
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.48.41:483][954]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_82
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.48.41:483][954]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_82
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.48.41:526][956]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_83
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.48.41:526][956]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_83
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.48.41:534][956]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.00:530][937]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.04:934][353]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.06:761][523]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.49.16:059][395]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.17:830][541]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.23:634][121]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.27:062][451]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.29:169][628]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.33:378][  6]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.34:402][ 93]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.36:644][278]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.36:644][278]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.37:877][384]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.37:877][384]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.37:877][384]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.39:965][545]LogUObjectHash: Compacting FUObjectHashTables data took   2.69ms
[2025.06.05-09.49.40:019][545]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_84
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.40:019][545]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_84
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.40:072][545]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_85
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.40:073][545]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_85
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.40:128][547]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_86
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.40:129][547]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_86
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.40:135][547]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.50:889][392]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.50:889][392]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.49.55:578][666]LogUObjectHash: Compacting FUObjectHashTables data took   1.90ms
[2025.06.05-09.49.55:617][666]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_87
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.55:618][666]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_87
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.55:673][666]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_88
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.55:674][666]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_88
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.55:729][668]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_89
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.55:730][668]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_89
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.49.55:737][668]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.50.11:291][854]LogUObjectHash: Compacting FUObjectHashTables data took   1.96ms
[2025.06.05-09.50.11:345][854]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_93
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.11:345][854]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_93
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.11:353][854]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_94
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.11:354][854]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_94
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.11:652][855]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_95
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.11:653][855]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_95
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.18:976][378]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.50.18:996][378]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.05-09.50.19:033][378]OBJ SavePackage: Generating thumbnails for [2] asset(s) in package [/Game/Characters/BaoliMC/CR_AO] ([2] browsable assets)...
[2025.06.05-09.50.19:111][378]OBJ SavePackage:     Rendered thumbnail for [ControlRigBlueprint /Game/Characters/BaoliMC/CR_AO.CR_AO]
[2025.06.05-09.50.19:111][378]OBJ SavePackage: Finished generating thumbnails for package [/Game/Characters/BaoliMC/CR_AO]
[2025.06.05-09.50.19:111][378]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/Characters/BaoliMC/CR_AO" FILE="H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset" SILENT=true
[2025.06.05-09.50.19:132][378]LogSavePackage: Moving output files for package: /Game/Characters/BaoliMC/CR_AO
[2025.06.05-09.50.19:133][378]LogSavePackage: Moving 'H:/P4/dev/Baoli/Saved/CR_AOCDF0AE3B48E33E0B153C2E9D9DC98004.tmp' to 'H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.50.19:168][378]LogFileHelpers: InternalPromptForCheckoutAndSave took 172.486 ms (total: 401.158 ms)
[2025.06.05-09.50.19:234][378]LogContentValidation: Display: Starting to validate 1 assets
[2025.06.05-09.50.19:234][378]LogContentValidation: Enabled validators:
[2025.06.05-09.50.19:234][378]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.05-09.50.19:234][378]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.05-09.50.19:234][378]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.05-09.50.19:234][378]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.05-09.50.19:234][378]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.05-09.50.19:234][378]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.05-09.50.19:234][378]LogContentValidation: 	/Script/GameFeaturesEditor.IllegalPluginDependenciesValidator
[2025.06.05-09.50.19:234][378]LogContentValidation: 	/Script/AssetReferenceRestrictions.EditorValidator_PluginAssetReferences
[2025.06.05-09.50.19:234][378]LogContentValidation: 	/Script/AssetReferenceRestrictions.AssetValidator_AssetReferenceRestrictions
[2025.06.05-09.50.19:234][378]AssetCheck: /Game/Characters/BaoliMC/CR_AO Validating asset
[2025.06.05-09.50.19:278][380]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.50.21:796][574]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.50.22:819][646]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.50.23:150][651]LogUObjectHash: Compacting FUObjectHashTables data took   3.10ms
[2025.06.05-09.50.23:191][651]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_99
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.23:191][651]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_99
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.23:246][651]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_100
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.23:246][651]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_100
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.23:294][653]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_101
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.23:294][653]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_101
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.23:301][653]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.50.46:031][185]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2125.575439
[2025.06.05-09.50.46:375][208]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-09.50.46:375][208]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2125.904541, Update Interval: 309.313019
[2025.06.05-09.50.51:148][512]LogUObjectHash: Compacting FUObjectHashTables data took   2.55ms
[2025.06.05-09.50.51:202][512]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_111
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.51:202][512]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_111
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.51:209][512]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_112
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.51:209][512]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_112
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.51:530][514]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_113
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.51:531][514]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_113
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.57:170][906]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-09.50.57:176][906]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-09.50.57:181][906]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.06.05-09.50.57:181][906]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.06.05-09.50.57:181][906]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-09.50.57:243][906]LogPlayLevel: PIE: StaticDuplicateObject took: (0.061457s)
[2025.06.05-09.50.57:243][906]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.061516s)
[2025.06.05-09.50.57:283][906]LogUObjectHash: Compacting FUObjectHashTables data took   2.64ms
[2025.06.05-09.50.57:288][906]r.RayTracing.Culling = "0"
[2025.06.05-09.50.57:288][906]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-09.50.57:288][906]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-09.50.57:290][906]LogPlayLevel: PIE: World Init took: (0.001863s)
[2025.06.05-09.50.57:291][906]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.06.05-09.50.57:291][906]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-09.50.57:291][906]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-09.50.57:291][906]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-09.50.57:291][906]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-09.50.57:291][906]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-09.50.57:291][906]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-09.50.57:291][906]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-09.50.57:291][906]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-09.50.57:291][906]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-09.50.57:291][906]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-09.50.57:291][906]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-09.50.57:293][906]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-09.50.57:329][906]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-09.50.57:329][906]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-09.50.57:330][906]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-09.50.57:330][906]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-09.50.57:332][906]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.06.05-09.50.57:333][906]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.06.05-09.50.57:335][906]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.06.05-09.50.57:335][906]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.06.05-09.50.57:335][906]LogInit: FAudioDevice initialized with ID 2.
[2025.06.05-09.50.57:335][906]LogAudio: Display: Audio Device (ID: 2) registered with world 'DefaultLevel'.
[2025.06.05-09.50.57:336][906]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.06.05-09.50.57:336][906]LogWindows: WindowsPlatformFeatures enabled
[2025.06.05-09.50.57:336][906]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-09.50.57:338][906]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-09.50.57:366][906]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-09.50.57:374][906]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-15.20.57
[2025.06.05-09.50.57:380][906]LogWorld: Bringing up level for play took: 0.042601
[2025.06.05-09.50.57:384][906]LogOnline: OSS: Created online subsystem instance for: :Context_49
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-09.50.57:403][906]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-09.50.57:404][906]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-09.50.57:404][906]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-09.50.57:404][906]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-09.50.57:404][906]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-09.50.57:417][906]PIE: Server logged in
[2025.06.05-09.50.57:419][906]PIE: Play in editor total start time 0.243 seconds.
[2025.06.05-09.50.58:111][907]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-09.50.58:167][907]LogD3D12RHI: Creating RTPSO with 24 shaders (20 cached, 4 new) took 19.76 ms. Compile time 14.76 ms, link time 4.94 ms.
[2025.06.05-09.50.58:274][908]LogD3D12RHI: Creating RTPSO with 234 shaders (0 cached, 1 new) took 126.94 ms. Compile time 6.38 ms, link time 120.37 ms.
[2025.06.05-09.50.58:669][912]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_114
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.58:670][912]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_114
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.58:779][913]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'UE.Wave Player.Stereo (v1.0)': Interface change detected.
[2025.06.05-09.50.58:779][913]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'Array.Random Get.WaveAsset:Array': Newer version 'v1.1' found.
[2025.06.05-09.50.58:868][915]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_115
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.58:868][915]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_115
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.50.59:476][932]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.05-09.51.00:004][948]PIE: Error: No database assets provided for motion matching.
[2025.06.05-09.51.00:985][979]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_116
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.51.00:986][979]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_116
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.51.01:159][982]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_117
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.51.01:159][982]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_117
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.51.15:131][409]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-09.51.15:131][409]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-09.51.15:136][409]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-09.51.15:136][409]LogWebBrowser: Deleting browser for Url=file:///D:/Lasun/Baoli/Saved/index.html.
[2025.06.05-09.51.15:137][409]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-09.51.15:139][409]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-09.51.15:143][409]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-09.51.15:156][409]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-09.51.15:197][409]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-09.51.15:221][409]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.06.05-09.51.15:221][409]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.05-09.51.15:223][409]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.05-09.51.15:228][409]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-09.51.15:236][409]LogUObjectHash: Compacting FUObjectHashTables data took   2.18ms
[2025.06.05-09.51.15:351][410]LogPlayLevel: Display: Destroying online subsystem :Context_49
[2025.06.05-09.51.17:505][534]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-09.51.17:522][534]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-09.51.54:805][321]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.51.54:805][321]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.51.59:715][639]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.51.59:715][639]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.52.27:793][858]LogUObjectHash: Compacting FUObjectHashTables data took   2.01ms
[2025.06.05-09.52.27:834][858]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_118
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.27:834][858]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_118
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.27:841][858]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_119
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.27:841][858]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_119
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.27:881][860]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_120
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.27:882][860]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_120
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.27:889][860]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.52.28:178][866]LogUObjectHash: Compacting FUObjectHashTables data took   1.92ms
[2025.06.05-09.52.28:228][866]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_121
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.28:229][866]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_121
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.28:236][866]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_122
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.28:236][866]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_122
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.28:278][868]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_123
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.28:279][868]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_123
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.28:286][868]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.52.36:815][678]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.52.38:589][818]LogUObjectHash: Compacting FUObjectHashTables data took   1.94ms
[2025.06.05-09.52.38:641][818]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_124
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.38:641][818]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_124
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.38:648][818]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_125
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.38:649][818]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_125
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.38:685][820]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_126
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.38:686][820]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_126
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.52.38:691][820]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.52.45:564][357]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-09.52.45:576][357]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-09.52.45:576][357]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-09.52.45:640][357]LogPlayLevel: PIE: StaticDuplicateObject took: (0.063149s)
[2025.06.05-09.52.45:640][357]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.063230s)
[2025.06.05-09.52.45:678][357]LogUObjectHash: Compacting FUObjectHashTables data took   1.81ms
[2025.06.05-09.52.45:685][357]r.RayTracing.Culling = "0"
[2025.06.05-09.52.45:685][357]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-09.52.45:685][357]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-09.52.45:687][357]LogPlayLevel: PIE: World Init took: (0.001795s)
[2025.06.05-09.52.45:688][357]LogAudio: Display: Creating Audio Device:                 Id: 3, Scope: Unique, Realtime: True
[2025.06.05-09.52.45:688][357]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-09.52.45:688][357]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-09.52.45:688][357]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-09.52.45:688][357]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-09.52.45:688][357]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-09.52.45:688][357]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-09.52.45:688][357]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-09.52.45:688][357]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-09.52.45:688][357]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-09.52.45:688][357]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-09.52.45:688][357]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-09.52.45:690][357]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-09.52.45:726][357]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-09.52.45:727][357]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-09.52.45:727][357]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-09.52.45:727][357]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-09.52.45:727][357]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=3
[2025.06.05-09.52.45:727][357]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=3
[2025.06.05-09.52.45:730][357]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=3
[2025.06.05-09.52.45:730][357]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=3
[2025.06.05-09.52.45:730][357]LogInit: FAudioDevice initialized with ID 3.
[2025.06.05-09.52.45:730][357]LogAudio: Display: Audio Device (ID: 3) registered with world 'DefaultLevel'.
[2025.06.05-09.52.45:730][357]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 3
[2025.06.05-09.52.45:730][357]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-09.52.45:734][357]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-09.52.45:763][357]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-09.52.45:771][357]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-15.22.45
[2025.06.05-09.52.45:778][357]LogWorld: Bringing up level for play took: 0.043467
[2025.06.05-09.52.45:780][357]LogOnline: OSS: Created online subsystem instance for: :Context_50
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-09.52.45:800][357]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-09.52.45:813][357]PIE: Server logged in
[2025.06.05-09.52.45:815][357]PIE: Play in editor total start time 0.244 seconds.
[2025.06.05-09.52.45:944][358]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-09.52.47:429][403]PIE: Error: No database assets provided for motion matching.
[2025.06.05-09.52.53:560][596]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-09.52.53:560][596]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-09.52.53:565][596]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-09.52.53:565][596]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-09.52.53:567][596]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-09.52.53:571][596]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-09.52.53:587][596]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-09.52.53:603][596]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-09.52.53:640][596]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-09.52.53:665][596]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 3
[2025.06.05-09.52.53:665][596]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.05-09.52.53:668][596]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.05-09.52.53:673][596]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-09.52.53:680][596]LogUObjectHash: Compacting FUObjectHashTables data took   2.19ms
[2025.06.05-09.52.53:798][597]LogPlayLevel: Display: Destroying online subsystem :Context_50
[2025.06.05-09.52.55:312][647]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-09.52.55:349][647]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-09.52.59:185][948]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.00:596][ 48]LogUObjectHash: Compacting FUObjectHashTables data took   1.93ms
[2025.06.05-09.53.00:646][ 48]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_127
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.00:647][ 48]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_127
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.00:654][ 48]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_128
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.00:655][ 48]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_128
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.00:690][ 50]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_129
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.00:690][ 50]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_129
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.00:696][ 50]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.11:526][815]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.11:526][815]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.11:882][830]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.11:882][830]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.11:882][830]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.12:348][853]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.12:348][853]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.12:348][853]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.12:348][853]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.30:818][ 60]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.30:818][ 60]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.30:818][ 60]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.30:818][ 60]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.33:494][281]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.33:494][281]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.33:494][281]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.33:494][281]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.37:192][592]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.37:192][592]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.37:192][592]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.37:192][592]LogRigVM: Warning: Instruction[4] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.38:827][686]LogUObjectHash: Compacting FUObjectHashTables data took   1.93ms
[2025.06.05-09.53.38:875][686]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_130
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.38:875][686]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_130
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.38:881][686]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_131
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.38:883][686]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_131
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.38:936][688]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_132
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.38:937][688]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_132
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.53.38:941][688]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.53.41:276][851]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-09.53.41:288][851]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-09.53.41:288][851]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-09.53.41:351][851]LogPlayLevel: PIE: StaticDuplicateObject took: (0.061955s)
[2025.06.05-09.53.41:351][851]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.062006s)
[2025.06.05-09.53.41:391][851]LogUObjectHash: Compacting FUObjectHashTables data took   1.85ms
[2025.06.05-09.53.41:398][851]r.RayTracing.Culling = "0"
[2025.06.05-09.53.41:398][851]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-09.53.41:399][851]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-09.53.41:401][851]LogPlayLevel: PIE: World Init took: (0.002196s)
[2025.06.05-09.53.41:402][851]LogAudio: Display: Creating Audio Device:                 Id: 4, Scope: Unique, Realtime: True
[2025.06.05-09.53.41:402][851]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-09.53.41:402][851]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-09.53.41:402][851]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-09.53.41:402][851]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-09.53.41:402][851]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-09.53.41:402][851]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-09.53.41:402][851]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-09.53.41:402][851]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-09.53.41:402][851]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-09.53.41:402][851]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-09.53.41:402][851]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-09.53.41:404][851]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-09.53.41:439][851]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-09.53.41:439][851]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-09.53.41:439][851]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-09.53.41:439][851]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-09.53.41:440][851]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=4
[2025.06.05-09.53.41:440][851]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=4
[2025.06.05-09.53.41:443][851]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=4
[2025.06.05-09.53.41:443][851]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=4
[2025.06.05-09.53.41:443][851]LogInit: FAudioDevice initialized with ID 4.
[2025.06.05-09.53.41:443][851]LogAudio: Display: Audio Device (ID: 4) registered with world 'DefaultLevel'.
[2025.06.05-09.53.41:443][851]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 4
[2025.06.05-09.53.41:443][851]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-09.53.41:445][851]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-09.53.41:475][851]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-09.53.41:483][851]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-15.23.41
[2025.06.05-09.53.41:490][851]LogWorld: Bringing up level for play took: 0.044456
[2025.06.05-09.53.41:493][851]LogOnline: OSS: Created online subsystem instance for: :Context_51
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-09.53.41:511][851]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-09.53.41:512][851]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-09.53.41:525][851]PIE: Server logged in
[2025.06.05-09.53.41:527][851]PIE: Play in editor total start time 0.244 seconds.
[2025.06.05-09.53.41:655][852]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-09.53.43:305][901]PIE: Error: No database assets provided for motion matching.
[2025.06.05-09.53.57:531][349]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-09.53.57:531][349]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-09.53.57:536][349]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-09.53.57:536][349]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-09.53.57:536][349]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-09.53.57:539][349]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-09.53.57:559][349]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-09.53.57:571][349]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-09.53.57:607][349]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-09.53.57:632][349]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 4
[2025.06.05-09.53.57:632][349]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4
[2025.06.05-09.53.57:635][349]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4
[2025.06.05-09.53.57:639][349]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-09.53.57:646][349]LogUObjectHash: Compacting FUObjectHashTables data took   2.09ms
[2025.06.05-09.53.57:763][350]LogPlayLevel: Display: Destroying online subsystem :Context_51
[2025.06.05-09.53.58:957][387]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-09.53.58:996][387]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-09.54.06:867][ 56]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-09.54.23:693][465]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.54.29:922][972]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.54.31:263][ 72]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\ABP_SandboxCharacter.uasset: [Compiler] Invalid field '__CustomProperty_AimAO_Y_F58E350B4A32BFAAC901D6B58A6DA640' found in property path for  Control Rig
[2025.06.05-09.54.31:418][ 72]LogUObjectHash: Compacting FUObjectHashTables data took   1.92ms
[2025.06.05-09.54.31:470][ 72]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_133
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.54.31:471][ 72]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_133
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.54.31:477][ 72]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_134
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.54.31:478][ 72]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_134
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.54.31:542][ 74]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_135
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.54.31:543][ 74]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_135
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.54.31:548][ 74]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.54.49:783][585]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.54.57:853][251]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.08:974][ 28]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.08:975][ 28]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.09:299][ 56]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.09:299][ 56]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.09:299][ 56]LogRigVM: Warning: Instruction[10] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.09:633][ 84]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.09:634][ 84]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.09:634][ 84]LogRigVM: Warning: Instruction[10] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.09:634][ 84]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.15:518][544]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.15:519][544]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.15:519][544]LogRigVM: Warning: Instruction[10] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.15:519][544]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.19:365][870]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.19:365][870]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.19:365][870]LogRigVM: Warning: Instruction[10] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.19:365][870]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.19:365][870]LogRigVM: Warning: Instruction[9] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.22:857][134]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Characters/BaoliMC/CR_AO.uasset'
[2025.06.05-09.55.27:885][573]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.27:885][573]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.27:885][573]LogRigVM: Warning: Instruction[10] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.27:885][573]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.27:885][573]LogRigVM: Warning: Instruction[9] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.30:059][746]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.30:059][746]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.30:059][746]LogRigVM: Warning: Instruction[10] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.30:059][746]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.30:059][746]LogRigVM: Warning: Instruction[9] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.31:621][874]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.31:621][874]LogRigVM: Warning: Instruction[11] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.31:621][874]LogRigVM: Warning: Instruction[10] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.31:621][874]LogRigVM: Warning: Instruction[8] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.31:621][874]LogRigVM: Warning: Instruction[9] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.33:711][ 35]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\ABP_SandboxCharacter.uasset: [Compiler] Invalid field '__CustomProperty_AimAO_X_F58E350B4A32BFAAC901D6B58A6DA640' found in property path for  Control Rig
[2025.06.05-09.55.33:884][ 35]LogUObjectHash: Compacting FUObjectHashTables data took   2.05ms
[2025.06.05-09.55.33:935][ 35]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_136
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.55.33:936][ 35]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_136
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.55.33:942][ 35]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_137
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.55.33:942][ 35]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_137
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.55.33:985][ 37]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_138
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.55.33:985][ 37]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_38:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_138
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-09.55.33:991][ 37]LogRigVM: Warning: Instruction[12] 'FRigUnit_AimItem::Execute': 'Invalid secondary target.'
[2025.06.05-09.55.36:135][235]LogOutputDevice: Warning: 

Script Stack (0 frames) :

[2025.06.05-09.55.36:233][235]LogStats: FPlatformStackWalk::StackWalkAndDump -  0.098 s
[2025.06.05-09.55.36:233][235]LogOutputDevice: Error: === Handled ensure: ===
[2025.06.05-09.55.36:233][235]LogOutputDevice: Error: 
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Plugins\Animation\ControlRig\Source\ControlRigDeveloper\Private\AnimGraphNode_ControlRig.cpp] [Line: 401] 
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: 
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: Stack: 
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd2beb41d5 UnrealEditor-ControlRigDeveloper.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd2bea9c76 UnrealEditor-ControlRigDeveloper.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd2c30673d UnrealEditor-RigVMEditor.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd2c2899f1 UnrealEditor-RigVMEditor.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692f4c5c UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692cc3cc UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692cc6f2 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed2b9 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed33d UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: [Callstack] 0x00007ffd692ed442 UnrealEditor-SlateCore.dll!UnknownFunction []
[2025.06.05-09.55.36:234][235]LogOutputDevice: Error: 
