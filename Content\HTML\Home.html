﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>OpenSearch</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        .browser-bar {
            background: #f1f1f1;
            padding: 10px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #ddd;
            gap: 10px;
        }

        .browser-buttons {
            display: flex;
            gap: 10px;
        }

        .browser-button {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .back-btn {
            background: #ff605c;
        }

        .home-btn {
            background: #00ca4e;
        }

        .url-bar {
            flex: 1;
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 10px;
            gap: 5px;
        }

        .https-icon {
            color: #00ca4e;
            font-size: 14px;
        }

        .url-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 14px;
            color: #333;
            background: transparent;
        }

        .weather {
            padding: 10px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .search-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 150px;
        }

        .logo {
            font-size: 72px;
            color: #4285f4;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .search-wrapper {
            display: flex;
            align-items: center;
            width: 500px;
            position: relative;
        }

        .search-box {
            width: 100%;
            padding: 15px 45px;
            border: 1px solid #ddd;
            border-radius: 24px;
            font-size: 16px;
            outline: none;
        }

        .search-box:hover, .search-box:focus {
            box-shadow: 0 1px 6px rgba(32,33,36,.28);
        }

        .search-icon {
            position: absolute;
            left: 15px;
            color: #9aa0a6;
            font-size: 18px;
        }

        .search-button {
            position: absolute;
            right: 5px;
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .search-button:hover {
            background: #357ae8;
        }

        .search-result {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            display: none;
        }

        .result-title {
            color: #1a0dab;
            font-size: 20px;
            margin-bottom: 10px;
        }

        .result-url {
            color: #006621;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .result-description {
            color: #545454;
            font-size: 14px;
        }

        .news-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            max-width: 1200px;
            margin: 40px auto;
            padding: 20px;
        }

        .news-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .news-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .news-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .news-description {
            font-size: 14px;
            color: #666;
        }

        .error-message {
            text-align: center;
            margin-top: 50px;
            font-size: 24px;
            color: #d93025;
            display: none;
        }

        @media (max-width: 1200px) {
            .news-container {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 900px) {
            .news-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 600px) {
            .news-container {
                grid-template-columns: 1fr;
            }
            .search-wrapper {
                width: 90%;
            }
        }
    </style>
</head>
<body>
    <div class="browser-bar">
        <div class="browser-buttons">
            <button class="browser-button back-btn" onclick="goBack()">←</button>
            <button class="browser-button home-btn" onclick="goHome()">⌂</button>
        </div>
        <div class="url-bar">
            <span class="https-icon">🔒</span>
            <input type="text" class="url-input" value="https://www.opensearch.com" readonly>
        </div>
        <div class="weather">🌤️ 22°C</div>
    </div>

    <div class="search-container">
        <div class="logo">OpenSearch</div>
        <div class="search-wrapper">
            <span class="search-icon">🔍</span>
            <input type="text" class="search-box" placeholder="Search OpenSearch or type a URL" id="searchInput" onkeypress="handleSearch(event)">
            <button class="search-button" onclick="performSearch()">Search</button>
        </div>
    </div>

    <div class="error-message">
        404 - Page Not Found<br>
        Redirecting to home...
    </div>

    <div class="search-result">
        <div class="result-title">Baoli - Official Website</div>
        <div class="result-url">https://www.baoli.com</div>
        <div class="result-description">
            Baoli is a leading manufacturer of forklifts and material handling equipment. 
            Explore our range of products and solutions for your business needs.
        </div>
    </div>

    <div class="news-container">
        <div class="news-card" onclick="handleNewsClick('health')">
            <img src="/api/placeholder/300/200" alt="Health News">
            <div class="news-title">Public Health Alert: Understanding Substance Use Prevention</div>
            <div class="news-description">Local health officials release new community support resources and prevention strategies...</div>
        </div>
        <div class="news-card" onclick="handleNewsClick('community')">
            <img src="/api/placeholder/300/200" alt="Community News">
            <div class="news-title">Community Safety Report: Local Crime Statistics</div>
            <div class="news-description">Police department shares annual safety report and prevention measures...</div>
        </div>
        <div class="news-card" onclick="handleNewsClick('safety')">
            <img src="/api/placeholder/300/200" alt="Safety News">
            <div class="news-title">Child Safety Awareness Campaign Launched</div>
            <div class="news-description">New initiative focuses on protecting children and promoting community vigilance...</div>
        </div>
        <div class="news-card" onclick="handleNewsClick('tech')">
            <img src="/api/placeholder/300/200" alt="Tech News">
            <div class="news-title">Technology Breakthrough</div>
            <div class="news-description">Revolutionary AI system helps improve urban safety measures...</div>
        </div>
        <div class="news-card" onclick="handleNewsClick('education')">
            <img src="/api/placeholder/300/200" alt="Education News">
            <div class="news-title">Education System Updates</div>
            <div class="news-description">New safety protocols implemented in local schools...</div>
        </div>
        <div class="news-card" onclick="handleNewsClick('environment')">
            <img src="/api/placeholder/300/200" alt="Environment News">
            <div class="news-title">Environmental Impact Study</div>
            <div class="news-description">Research reveals effects of climate change on local communities...</div>
        </div>
        <div class="news-card" onclick="handleNewsClick('transport')">
            <img src="/api/placeholder/300/200" alt="Transport News">
            <div class="news-title">Transportation Safety Measures</div>
            <div class="news-description">City implements new traffic safety protocols...</div>
        </div>
        <div class="news-card" onclick="handleNewsClick('business')">
            <img src="/api/placeholder/300/200" alt="Business News">
            <div class="news-title">Business District Security</div>
            <div class="news-description">Local businesses collaborate on community safety initiatives...</div>
        </div>
    </div>

    <script>
        let searchHistory = [];
        let currentIndex = -1;

        function performSearch() {
            const searchInput = document.getElementById('searchInput');
            const searchTerm = searchInput.value.toLowerCase();
            searchHistory.push(window.location.href);
            currentIndex = searchHistory.length - 1;

            if (searchTerm === 'baoli') {
                document.querySelector('.search-container').style.marginTop = '20px';
                document.querySelector('.search-result').style.display = 'block';
                document.querySelector('.error-message').style.display = 'none';
                document.querySelector('.news-container').style.display = 'grid';
                document.querySelector('.url-input').value = 'https://www.opensearch.com/search?q=baoli';
            } else {
                document.querySelector('.search-container').style.marginTop = '20px';
                document.querySelector('.search-result').style.display = 'none';
                document.querySelector('.error-message').style.display = 'block';
                document.querySelector('.news-container').style.display = 'none';
                document.querySelector('.url-input').value = `https://www.opensearch.com/search?q=${encodeURIComponent(searchTerm)}`;
                setTimeout(goHome, 2000);
            }
        }

        function handleSearch(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        }

        function handleNewsClick(category) {
            searchHistory.push(window.location.href);
            currentIndex = searchHistory.length - 1;
            
            document.querySelector('.url-input').value = `https://www.opensearch.com/news/${category}`;
            
            document.querySelector('.search-container').style.marginTop = '20px';
            document.querySelector('.search-result').style.display = 'none';
            document.querySelector('.news-container').style.display = 'none';
            
            const articleContent = document.createElement('div');
            articleContent.style.maxWidth = '800px';
            articleContent.style.margin = '20px auto';
            articleContent.style.padding = '20px';
            
            const articles = {
                health: {
                    title: "Public Health Alert: Understanding Substance Use Prevention",
                    content: "Local health officials have released a comprehensive report on community health resources and prevention strategies. The report includes information about available support services, treatment options, and community outreach programs..."
                },
                community: {
                    title: "Community Safety Report: Local Crime Statistics",
                    content: "The annual safety report highlights areas of improvement and ongoing challenges in community safety. Officials emphasize the importance of community cooperation and provide resources for neighborhood watch programs..."
                },
                safety: {
                    title: "Child Safety Awareness Campaign Launched",
                    content: "A new initiative focusing on child safety has been launched, emphasizing prevention through education and community awareness. The campaign includes safety tips, emergency contacts, and resources for families..."
                }
            };
            
            const article = articles[category] || {
                title: "News Article",
                content: "This is a detailed news article about " + category + "..."
            };
            
            articleContent.innerHTML = `
                <h1 style="font-size: 24px; margin-bottom: 20px;">${article.title}</h1>
                <p style="line-height: 1.6; color: #333;">${article.content}</p>
                <button onclick="goBack()" style="margin-top: 20px; padding: 10px 20px; background: #4285f4; color: white; border: none; border-radius: 4px; cursor: pointer;">Back to News</button>
            `;
            
            document.body.appendChild(articleContent);
        }

        function goBack() {
            const articleContent = document.body.querySelector('div:last-child');
            if (articleContent && articleContent.querySelector('button')) {
                articleContent.remove();
                document.querySelector('.news-container').style.display = 'grid';
                document.querySelector('.search-container').style.marginTop = '20px';
                document.querySelector('.url-input').value = 'https://www.opensearch.com';
            } else if (currentIndex > 0) {
                currentIndex--;
                resetPage();
            } else {
                goHome();
            }
        }

        function goHome() {
            resetPage();
            searchHistory = [];
            currentIndex = -1;
            document.querySelector('.url-input').value = 'https://www.opensearch.com';
        }

        function resetPage() {
            document.querySelector('.search-box').value = '';
            document.querySelector('.search-container').style.marginTop = '150px';
            document.querySelector('.search-result').style.display = 'none';
            document.querySelector('.error-message').style.display = 'none';
            document.querySelector('.news-container').style.display = 'grid';
        }

        // Initialize the page
        goHome();
    </script>
</body>
</html>