# Optimized Web Browser Plugin for Unreal Engine

This plugin provides an optimized version of the WebBrowserWidget for Unreal Engine that improves performance without removing any functionality.

## Author

**Anshul Patalbansi**
Game Developer | Unreal Engine Generalist
Website: [https://anshulpatalbansi.com](https://anshulpatalbansi.com)
Email: [<EMAIL>](mailto:<EMAIL>)
LinkedIn: [linkedin.com/in/anshul-patalbansi-014a43186](https://linkedin.com/in/anshul-patalbansi-014a43186)

## Installation

1. Copy the `OptimizedWebBrowser` folder to your project's `Plugins` directory
2. Restart the Unreal Engine editor
3. Enable the plugin in Edit > Plugins > UI > Optimized Web Browser

## Features

- **Improved Performance**: Optimized rendering and reduced CPU usage
- **Configurable Frame Rate**: Set the browser frame rate to balance between smoothness and performance
- **Hardware Acceleration Control**: Enable or disable hardware acceleration
- **Background Tab Throttling**: Reduce CPU usage when the browser is not visible
- **Optimized Scrolling**: Improved scrolling performance
- **Lazy Loading**: Automatically lazy load images for better performance
- **Animation Optimization**: Disable animations when the page is not visible

## Usage

### In Blueprints

1. Add the "Optimized Web Browser" widget to your UI
2. Configure the performance settings:
   - Browser Frame Rate: Higher values (30-120) for smoother experience, lower values (15-24) for better performance
   - Hardware Acceleration: Enable for better performance on most systems
   - Background Color: Use opaque colors for better performance
   - Background Tab Throttling: Enable to reduce CPU usage when the browser is not visible
3. Use the "Log To Console" function to send messages from Blueprint to the browser's JavaScript console:
   - This is useful for debugging or communicating between Unreal Engine and web content

### In C++

```cpp
#include "OptimizedWebBrowser.h"

// Create an optimized web browser
UOptimizedWebBrowser* Browser = CreateWidget<UOptimizedWebBrowser>(GetWorld(), UOptimizedWebBrowser::StaticClass());

// Configure performance settings
Browser->SetBrowserFrameRate(60); // Can now set up to 120 fps
Browser->SetHardwareAccelerationEnabled(true);
Browser->SetBackgroundColor(FColor(255, 255, 255, 255));
Browser->SetBackgroundTabThrottlingEnabled(true);

// Load a URL
Browser->LoadURL("https://www.example.com");

// Log a message to the browser's JavaScript console
Browser->LogToConsole("Hello from Unreal Engine!");
```

## Performance Tips

1. **Use Opaque Background Colors**: Transparent backgrounds require more processing power
2. **Choose Appropriate Frame Rate**:
   - Use 30 fps for a good balance between smoothness and performance
   - Use 60 fps for smoother animations and interactions
   - Use 120 fps for high-refresh rate displays and competitive applications
3. **Optimize HTML Content**:
   - Minimize DOM size and complexity
   - Reduce CSS animations and transitions
   - Use efficient JavaScript
   - Optimize images (size and format)
4. **Avoid Autoplay Media**: Autoplay videos and animations can cause performance issues
5. **Use Simple Layouts**: Complex layouts with many elements can slow down rendering

## Technical Details

The optimized web browser applies several techniques to improve performance:

1. **Frame Rate Limiting**: Controls the browser's internal rendering frame rate
2. **Hardware Acceleration Management**: Properly configures GPU acceleration
3. **Background Tab Throttling**: Reduces CPU usage when the browser is not visible
4. **JavaScript Optimizations**: Applies performance best practices via JavaScript
5. **Rendering Optimizations**: Configures the browser for optimal rendering performance

## Compatibility

This optimized web browser is compatible with all platforms supported by the standard WebBrowserWidget:
- Windows
- Mac
- Linux
- Android
- iOS

## License

This code is subject to the same license as the Unreal Engine WebBrowserWidget module.
