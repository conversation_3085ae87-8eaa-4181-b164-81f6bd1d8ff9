// This file is part of the FidelityFX Super Resolution 3.1 Unreal Engine Plugin.
//
// Copyright (c) 2023-2024 Advanced Micro Devices, Inc. All rights reserved.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.

#include "/Engine/Private/Common.ush"
#include "/Engine/Private/ScreenPass.ush"
#include "/Engine/Private/DeferredShadingCommon.ush"


// =====================================================================================
//
// SHADER RESOURCES
//
// =====================================================================================
Texture2D FirstFrame;
Texture2D FirstFrameWithUI;
Texture2D SecondFrame;
RWTexture2D<float4> SecondFrameWithUI;
uint2 ViewSize;
uint2 ViewMin;

// =====================================================================================
//
// ENTRY POINTS
//
// =====================================================================================
[numthreads(THREADGROUP_SIZEX, THREADGROUP_SIZEY, THREADGROUP_SIZEZ)] 
void MainCS(uint3 LocalThreadId : SV_GroupThreadID, uint3 WorkGroupId : SV_GroupID, uint3 DispatchThreadId : SV_DispatchThreadID)
{
	uint Width = ViewSize.x;
	uint Height = ViewSize.y;
	uint2 ThreadId = DispatchThreadId.xy + ViewMin;

	if (Width > DispatchThreadId.x && Height > DispatchThreadId.y)
	{
		float4 FirstFrameSample = FirstFrame[ThreadId];
		float4 FirstFrameWithUISample = FirstFrameWithUI[ThreadId];
		float4 SecondFrameSample = SecondFrame[ThreadId];
		float4 SecondFrameWithUISample = SecondFrameWithUI[ThreadId];
		float4 Result = SecondFrameWithUISample;
		if (any(FirstFrameSample.rgb - FirstFrameWithUISample.rgb))
		{
			if (!any(SecondFrameSample.rgb - SecondFrameWithUISample.rgb) || any(abs(SecondFrameWithUISample.rgb - FirstFrameWithUISample.rgb) > float3(0.1f, 0.1f, 0.1f)))
			{
				Result.rgb += (FirstFrameWithUISample.rgb - FirstFrameSample.rgb);
				Result.rgb = max(min(FirstFrameWithUISample.rgb, SecondFrameWithUISample.rgb), Result.rgb);
				Result.rgb = min(max(FirstFrameWithUISample.rgb, SecondFrameWithUISample.rgb), Result.rgb);
			}
		}
		SecondFrameWithUI[DispatchThreadId.xy] = Result;
	}
}
