﻿Log file open, 06/05/25 17:47:06
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=34804)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: Baoli
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\P4\dev\Baoli\Baoli.uproject -skipcompile""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.293384
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-4FD195154A949FBE41267891900EBD0A
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/P4/dev/Baoli/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogConfig: Display: Loading Mac ini files took 0.04 seconds
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading Android ini files took 0.05 seconds
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Windows ini files took 0.06 seconds
LogConfig: Display: Loading TVOS ini files took 0.06 seconds
LogConfig: Display: Loading Unix ini files took 0.06 seconds
LogConfig: Display: Loading VisionOS ini files took 0.06 seconds
LogConfig: Display: Loading Linux ini files took 0.06 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin Chooser
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin JsonBlueprintUtilities
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin AnimationLocomotionLibrary
LogPluginManager: Mounting Engine plugin AnimationWarping
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin BlendStack
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin MotionWarping
LogPluginManager: Mounting Engine plugin PoseSearch
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetReferenceRestrictions
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogAssetRegistry: Display: Asset registry cache read as 106.3 MiB from H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ScriptableToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin AxFImporter
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin MDLImporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin ActorPalette
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GPULightmass
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraFluids
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin USDImporter
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GameFeatures
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ModularGameplay
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin ScriptableToolsFramework
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebBrowserWidget
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin MotionTrajectory
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin AnalyticsBlueprintLibrary
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin Reflex
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Project plugin Inkpot
LogPluginManager: Mounting Project plugin OptimizedWebBrowser
LogPluginManager: Mounting Project plugin rdBPtools
LogPluginManager: Mounting Project plugin PlatformFunctions
LogPluginManager: Mounting Project plugin SnappingHelper
SourceControl: Revision control is disabled
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum ********** concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogGPULightmass: GPULightmass module is loaded
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: NVIDIA GeForce RTX 2080 Ti
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: -skipcompile
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 36
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.57ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Chooser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'JsonBlueprintUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AnimationLocomotionLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationWarping' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'GameplayInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MotionWarping' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PoseSearch' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetReferenceRestrictions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ScriptableToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AxFImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/MDLImporter.ini) has wildcard redirect /DatasmithContent/Materials/MDL/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'ActorPalette' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GPULightmass' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/NiagaraFluids.ini) has wildcard redirect /NiagaraSimulationStages/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'USDImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataRegistry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameFeatures' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModularGameplay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ScriptableToolsFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WebBrowserWidget' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MotionTrajectory' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnalyticsBlueprintLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Reflex' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Inkpot' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OptimizedWebBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'rdBPtools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformFunctions' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SnappingHelper' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.05-12.17.07:224][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.05-12.17.07:224][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.05-12.17.07:224][  0]LogConfig: CVar [[r.Mobile.EnableNoPrecomputedLightingCSMShader:1]] deferred - dummy variable created
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.AllowGlobalClipPlane:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.CustomDepth:3]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.VirtualTextures:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.AntiAliasingMethod:4]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.ReflectionCaptureResolution:32]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.MegaLights.EnableForProject:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.PathTracing:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.RayTracing.Shadows:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.RayTracing.UseTextureLod:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.CustomDepthTemporalAAJitter:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default:75.000000]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default.Desktop.Mode:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.Shadow.CSMCaching:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.MSAACount:8]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileSize:32]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileBorderSize:2]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.MaxTextureSize:4096]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.UseCompression:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.VT.TileBorderSize:4]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.DefaultTexelsPerVertex:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.AllowStaticLighting:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.Shadow.UnbuiltPreviewInGame:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.NormalMapsForStaticLighting:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing.LightingMode:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.VirtualTexturedLightmaps:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.Lumen.ScreenTracingSource:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Bias:0.000000]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Method:2]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.vt.rvt.HighQualityPerPixelHeight:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.05-12.17.07:224][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.05-12.17.07:224][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.05-12.17.07:224][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.05-12.17.07:224][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.05-12.17.07:224][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.05-12.17.07:224][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.05-12.17.07:224][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.05-12.17.07:224][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.05-12.17.07:224][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.05-12.17.07:224][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.05-12.17.07:225][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.05-12.17.07:227][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.05-12.17.07:227][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.05-12.17.07:227][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.05-12.17.07:227][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.05-12.17.07:228][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.06.05-12.17.07:228][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.05-12.17.07:228][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.05-12.17.07:228][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.05-12.17.07:228][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.05-12.17.07:231][  0]LogRHI: Using Default RHI: D3D12
[2025.06.05-12.17.07:231][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.05-12.17.07:231][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.05-12.17.07:236][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.05-12.17.07:236][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.05-12.17.07:356][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.05-12.17.07:356][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-12.17.07:356][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 3 output[s]
[2025.06.05-12.17.07:356][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.05-12.17.07:356][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.05-12.17.07:517][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.05-12.17.07:517][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-12.17.07:517][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-12.17.07:517][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.05-12.17.07:517][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.05-12.17.07:523][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.05-12.17.07:523][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.05-12.17.07:523][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-12.17.07:658][  0]LogD3D12RHI: Found D3D12 adapter 3: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.05-12.17.07:658][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-12.17.07:658][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-12.17.07:659][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.05-12.17.07:659][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.05-12.17.07:659][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.05-12.17.07:659][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.05-12.17.07:660][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.05-12.17.07:660][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.05-12.17.07:660][  0]LogHAL: Display: Platform has ~ 64 GB [68630138880 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.05-12.17.07:660][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.05-12.17.07:660][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.05-12.17.07:660][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.05-12.17.07:660][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.05-12.17.07:660][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.05-12.17.07:660][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.05-12.17.07:660][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.05-12.17.07:660][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.05-12.17.07:660][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.05-12.17.07:660][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.05-12.17.07:660][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.05-12.17.07:660][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.05-12.17.07:660][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.06.05-12.17.07:660][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/P4/dev/Baoli/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.05-12.17.07:661][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.06.05-12.17.07:661][  0]LogInit: User: Shashank
[2025.06.05-12.17.07:661][  0]LogInit: CPU Page size=4096, Cores=16
[2025.06.05-12.17.07:661][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.05-12.17.08:445][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.06.05-12.17.08:445][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.05-12.17.08:445][  0]LogMemory: Process Physical Memory: 696.24 MB used, 731.16 MB peak
[2025.06.05-12.17.08:445][  0]LogMemory: Process Virtual Memory: 777.01 MB used, 780.24 MB peak
[2025.06.05-12.17.08:445][  0]LogMemory: Physical Memory: 34476.94 MB used,  30973.86 MB free, 65450.80 MB total
[2025.06.05-12.17.08:445][  0]LogMemory: Virtual Memory: 43282.74 MB used,  26264.06 MB free, 69546.80 MB total
[2025.06.05-12.17.08:445][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.05-12.17.08:450][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.05-12.17.08:457][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.05-12.17.08:458][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.05-12.17.08:459][  0]LogInit: Using OS detected language (en-GB).
[2025.06.05-12.17.08:459][  0]LogInit: Using OS detected locale (en-IN).
[2025.06.05-12.17.08:461][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.05-12.17.08:461][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.05-12.17.08:767][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.05-12.17.08:767][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.06.05-12.17.08:767][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.05-12.17.08:791][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.05-12.17.08:791][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.05-12.17.08:937][  0]LogRHI: Using Default RHI: D3D12
[2025.06.05-12.17.08:937][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.05-12.17.08:937][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.05-12.17.08:937][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.05-12.17.08:937][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.05-12.17.08:937][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.05-12.17.08:939][  0]LogWindows: Attached monitors:
[2025.06.05-12.17.08:939][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.06.05-12.17.08:939][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.06.05-12.17.08:939][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.06.05-12.17.08:939][  0]LogWindows: Found 3 attached monitors.
[2025.06.05-12.17.08:939][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.05-12.17.08:939][  0]LogRHI: RHI Adapter Info:
[2025.06.05-12.17.08:939][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.06.05-12.17.08:939][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.05-12.17.08:939][  0]LogRHI:      Driver Date: 4-25-2025
[2025.06.05-12.17.08:939][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.06.05-12.17.08:969][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.05-12.17.09:044][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.05-12.17.09:044][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.05-12.17.09:177][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: Raster order views are supported
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.05-12.17.09:177][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.05-12.17.09:222][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000043BE1B85300)
[2025.06.05-12.17.09:223][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000043BE1B85580)
[2025.06.05-12.17.09:223][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000043BE1B85800)
[2025.06.05-12.17.09:223][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.05-12.17.09:223][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.05-12.17.09:223][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.06.05-12.17.09:223][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.06.05-12.17.09:223][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.05-12.17.09:223][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.05-12.17.09:240][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.05-12.17.09:247][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.05-12.17.09:257][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all'
[2025.06.05-12.17.09:257][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all" ]
[2025.06.05-12.17.09:302][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.05-12.17.09:302][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.05-12.17.09:302][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.05-12.17.09:302][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.05-12.17.09:302][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.05-12.17.09:302][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.06.05-12.17.09:302][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.05-12.17.09:303][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.05-12.17.09:303][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.05-12.17.09:344][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.05-12.17.09:344][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.05-12.17.09:344][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.05-12.17.09:344][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.05-12.17.09:344][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.05-12.17.09:344][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.05-12.17.09:344][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.05-12.17.09:344][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.05-12.17.09:344][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.05-12.17.09:344][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.05-12.17.09:364][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.05-12.17.09:364][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.05-12.17.09:384][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.05-12.17.09:384][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.05-12.17.09:384][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.05-12.17.09:384][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.05-12.17.09:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.06.05-12.17.09:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.06.05-12.17.09:403][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.06.05-12.17.09:426][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.05-12.17.09:426][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.05-12.17.09:426][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.05-12.17.09:426][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.05-12.17.09:450][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.05-12.17.09:450][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.05-12.17.09:475][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.05-12.17.09:475][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.05-12.17.09:475][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.05-12.17.09:475][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.05-12.17.09:475][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.05-12.17.09:542][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_METAL
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.05-12.17.09:546][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.05-12.17.09:546][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.06.05-12.17.09:546][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.06.05-12.17.09:553][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.05-12.17.09:553][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/P4/dev/Baoli/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.05-12.17.09:553][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.05-12.17.09:553][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/P4/dev/Baoli/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.05-12.17.09:553][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.05-12.17.09:648][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.06.05-12.17.09:648][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.05-12.17.09:648][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.05-12.17.09:649][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.05-12.17.09:651][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.05-12.17.09:652][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.05-12.17.09:652][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.06.05-12.17.09:652][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 11136 --child-id Zen_11136_Startup'
[2025.06.05-12.17.09:810][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.05-12.17.09:810][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.160 seconds
[2025.06.05-12.17.09:812][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.05-12.17.09:821][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.06.05-12.17.09:821][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.07ms. RandomReadSpeed=1213.37MBs, RandomWriteSpeed=142.46MBs. Assigned SpeedClass 'Local'
[2025.06.05-12.17.09:822][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.05-12.17.09:822][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.05-12.17.09:822][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.05-12.17.09:823][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.05-12.17.09:823][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.05-12.17.09:823][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.05-12.17.09:823][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.05-12.17.09:823][  0]LogShaderCompilers: Guid format shader working directory is 33 characters bigger than the processId version (H:/P4/dev/Baoli/Intermediate/Shaders/WorkingDirectory/11136/).
[2025.06.05-12.17.09:823][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/1C55F19E4B98AACAB59457A70D79D0C9/'.
[2025.06.05-12.17.09:823][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.06.05-12.17.09:823][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.06.05-12.17.09:824][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/P4/dev/Baoli/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.05-12.17.09:825][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.05-12.17.10:319][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.05-12.17.11:647][  0]LogSlate: Using FreeType 2.10.0
[2025.06.05-12.17.11:648][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.05-12.17.11:649][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-12.17.11:649][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-12.17.11:650][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-12.17.11:650][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-12.17.11:650][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-12.17.11:650][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-12.17.11:688][  0]LogAssetRegistry: FAssetRegistry took 0.0038 seconds to start up
[2025.06.05-12.17.11:692][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.05-12.17.11:696][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.003s loading caches H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.05-12.17.11:947][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-12.17.11:950][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.05-12.17.11:950][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.05-12.17.11:950][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.05-12.17.11:964][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.05-12.17.11:964][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.05-12.17.11:990][  0]LogDeviceProfileManager: Active device profile: [0000043BFB657C00][0000043BF2EEC000 66] WindowsEditor
[2025.06.05-12.17.11:990][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.05-12.17.11:990][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.05-12.17.11:995][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.06.05-12.17.11:995][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.06.05-12.17.12:062][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:062][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.06.05-12.17.12:062][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-12.17.12:062][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:062][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.06.05-12.17.12:062][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-12.17.12:063][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:064][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.06.05-12.17.12:064][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-12.17.12:064][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:064][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.06.05-12.17.12:064][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-12.17.12:064][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:065][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:065][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.06.05-12.17.12:065][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-12.17.12:065][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:065][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.06.05-12.17.12:065][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-12.17.12:065][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:065][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:065][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.06.05-12.17.12:065][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-12.17.12:066][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:066][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.06.05-12.17.12:066][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-12.17.12:066][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:067][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-12.17.12:068][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:068][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.06.05-12.17.12:068][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.05-12.17.12:068][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:068][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.06.05-12.17.12:068][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.05-12.17.12:068][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.06.05-12.17.12:069][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-12.17.12:070][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.12:070][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.06.05-12.17.12:070][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-12.17.12:310][  0]LogMeshReduction: Display: Mesh reduction module (r.MeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-12.17.12:310][  0]LogMeshReduction: Display: Skeletal mesh reduction module (r.SkeletalMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-12.17.12:310][  0]LogMeshReduction: Display: HLOD mesh reduction module (r.ProxyLODMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-12.17.12:332][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.05-12.17.12:332][  0]LogMeshReduction: Display: Using InstaLODMeshReduction for automatic skeletal mesh reduction
[2025.06.05-12.17.12:332][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.05-12.17.12:332][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.05-12.17.12:332][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.05-12.17.12:516][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.68ms
[2025.06.05-12.17.12:540][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.55ms
[2025.06.05-12.17.12:553][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.50ms
[2025.06.05-12.17.12:554][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.51ms
[2025.06.05-12.17.12:831][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.05-12.17.12:831][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.05-12.17.12:846][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.05-12.17.12:846][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.05-12.17.12:851][  0]LogLiveCoding: Display: First instance in process group "UE_Baoli_0x736adef1", spawning console
[2025.06.05-12.17.12:857][  0]LogLiveCoding: Display: Waiting for server
[2025.06.05-12.17.12:878][  0]LogSlate: Border
[2025.06.05-12.17.12:878][  0]LogSlate: BreadcrumbButton
[2025.06.05-12.17.12:878][  0]LogSlate: Brushes.Title
[2025.06.05-12.17.12:878][  0]LogSlate: Default
[2025.06.05-12.17.12:878][  0]LogSlate: Icons.Save
[2025.06.05-12.17.12:878][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.05-12.17.12:878][  0]LogSlate: ListView
[2025.06.05-12.17.12:878][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.05-12.17.12:878][  0]LogSlate: SoftwareCursor_Grab
[2025.06.05-12.17.12:878][  0]LogSlate: TableView.DarkRow
[2025.06.05-12.17.12:878][  0]LogSlate: TableView.Row
[2025.06.05-12.17.12:878][  0]LogSlate: TreeView
[2025.06.05-12.17.13:015][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.05-12.17.13:017][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 2.249 ms
[2025.06.05-12.17.13:029][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.48ms
[2025.06.05-12.17.13:035][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.05-12.17.13:052][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.05-12.17.13:052][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.05-12.17.13:052][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.05-12.17.13:052][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.06.05-12.17.13:100][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.05-12.17.13:106][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.05-12.17.13:106][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.05-12.17.13:106][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:51166'.
[2025.06.05-12.17.13:109][  0]LogUdpMessaging: Display: Added local interface '192.168.1.5' to multicast group '230.0.0.1:6666'
[2025.06.05-12.17.13:109][  0]LogUdpMessaging: Display: Added local interface '172.24.112.1' to multicast group '230.0.0.1:6666'
[2025.06.05-12.17.13:109][  0]LogUdpMessaging: Display: Added local interface '172.29.240.1' to multicast group '230.0.0.1:6666'
[2025.06.05-12.17.13:352][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.54ms
[2025.06.05-12.17.13:460][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 1A7253088CE442568000000000003200 | Instance: 430EEC8B496A0D5CA84178BC6EC2C6FC (DESKTOP-E41IK6R-11136).
[2025.06.05-12.17.13:955][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.05-12.17.13:955][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.06.05-12.17.13:955][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.06.05-12.17.13:955][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.05-12.17.13:955][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.05-12.17.14:241][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.05-12.17.14:241][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.05-12.17.14:254][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.05-12.17.14:454][  0]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.06.05-12.17.14:455][  0]LogCEFBrowser: CEF GPU acceleration enabled
[2025.06.05-12.17.14:585][  0]LogTemp: Warning: ✓ AI Perception system enabled and events bound
[2025.06.05-12.17.14:629][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.05-12.17.14:669][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.06.05-12.17.14:810][  0]LogTimingProfiler: Initialize
[2025.06.05-12.17.14:810][  0]LogTimingProfiler: OnSessionChanged
[2025.06.05-12.17.14:810][  0]LoadingProfiler: Initialize
[2025.06.05-12.17.14:810][  0]LoadingProfiler: OnSessionChanged
[2025.06.05-12.17.14:811][  0]LogNetworkingProfiler: Initialize
[2025.06.05-12.17.14:811][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.05-12.17.14:811][  0]LogMemoryProfiler: Initialize
[2025.06.05-12.17.14:811][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.05-12.17.14:952][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.05-12.17.14:963][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.05-12.17.15:420][  0]LogUsd: Attempted to call a static function from UnrealUSDWrapper before the module is actually loaded! The module will be loaded now, but not all static functions have this check. In general, please ensure an Unreal module is loaded before calling any of its static functions, for example by calling 'FModuleManager::LoadModuleChecked<IUnrealUSDWrapperModule>("UnrealUSDWrapper");' beforehand.
[2025.06.05-12.17.15:422][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.06.05-12.17.15:422][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.06.05-12.17.15:423][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.06.05-12.17.15:423][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.06.05-12.17.15:423][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.06.05-12.17.15:424][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.06.05-12.17.15:424][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.06.05-12.17.15:425][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.06.05-12.17.15:425][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.06.05-12.17.15:426][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.06.05-12.17.15:426][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.06.05-12.17.15:426][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.06.05-12.17.15:426][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.06.05-12.17.15:426][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.06.05-12.17.15:427][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.06.05-12.17.15:427][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.06.05-12.17.15:427][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.06.05-12.17.15:427][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.06.05-12.17.15:428][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.06.05-12.17.15:429][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.06.05-12.17.15:429][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.06.05-12.17.15:429][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.06.05-12.17.15:430][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.06.05-12.17.15:430][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.06.05-12.17.15:430][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.06.05-12.17.15:430][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.06.05-12.17.15:430][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.06.05-12.17.15:431][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.06.05-12.17.15:431][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.06.05-12.17.15:432][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.06.05-12.17.15:432][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.06.05-12.17.15:432][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.06.05-12.17.15:432][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.06.05-12.17.15:432][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.06.05-12.17.15:433][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.06.05-12.17.15:560][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-12.17.15:560][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-12.17.15:617][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.05-12.17.15:632][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.05-12.17.15:657][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.05-12.17.15:657][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.05-12.17.15:783][  0]LogCollectionManager: Loaded 1 collections in 0.000736 seconds
[2025.06.05-12.17.15:788][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Saved/Collections/' took 0.00s
[2025.06.05-12.17.15:791][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Developers/Shashank/Collections/' took 0.00s
[2025.06.05-12.17.15:795][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Collections/' took 0.00s
[2025.06.05-12.17.15:887][  0]LogConfig: Branch 'Plugins' had been unloaded. Reloading on-demand took 0.51ms
[2025.06.05-12.17.15:889][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-12.17.15:889][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-12.17.15:890][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-12.17.15:890][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-12.17.15:890][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-12.17.15:890][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-12.17.15:924][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-12.17.15:924][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-12.17.16:000][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-12.17.16:000][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-12.17.16:003][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-12.17.16:003][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-12.17.16:003][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-12.17.16:003][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-12.17.16:037][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-12.17.16:037][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-12.17.16:066][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-06-05T12:17:16.066Z using C
[2025.06.05-12.17.16:066][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=Baoli, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.05-12.17.16:067][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.05-12.17.16:067][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.05-12.17.16:101][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.05-12.17.16:101][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.05-12.17.16:101][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.05-12.17.16:101][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000105
[2025.06.05-12.17.16:101][  0]LogFab: Display: Logging in using persist
[2025.06.05-12.17.16:102][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.06.05-12.17.16:152][  0]LogUObjectArray: 47651 objects as part of root set at end of initial load.
[2025.06.05-12.17.16:152][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.05-12.17.16:166][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 40254 public script object entries (1077.02 KB)
[2025.06.05-12.17.16:166][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.06.05-12.17.16:277][  0]LogEngine: Initializing Engine...
[2025.06.05-12.17.16:278][  0]LogGameFeatures: Initializing game features subsystem
[2025.06.05-12.17.16:279][  0]InkPlusPlus: FStory::FStory 0000043C18CED210
[2025.06.05-12.17.16:279][  0]InkPlusPlus: Warning: WARNING: Version of ink used to build story doesn't match current version of engine. Non-critical, but recommend synchronising.
[2025.06.05-12.17.16:281][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.05-12.17.16:281][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.05-12.17.16:408][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.05-12.17.16:429][  0]LogGameFeatures: Scanning for built-in game feature plugins
[2025.06.05-12.17.16:429][  0]LogGameFeatures: Loading 233 builtins
[2025.06.05-12.17.16:430][  0]LogGameFeatures: Display: Total built in plugin load time 0.0011s
[2025.06.05-12.17.16:430][  0]LogStats: BuiltInGameFeaturePlugins loaded. -  0.001 s
[2025.06.05-12.17.16:430][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.05-12.17.16:450][  0]LogNetVersion: Set ProjectVersion to Alpha. Version Checksum will be recalculated on next use.
[2025.06.05-12.17.16:450][  0]LogInit: Texture streaming: Enabled
[2025.06.05-12.17.16:465][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.06.05-12.17.16:471][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.05-12.17.16:478][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.05-12.17.16:478][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.05-12.17.16:479][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.05-12.17.16:479][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.05-12.17.16:479][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-12.17.16:479][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-12.17.16:479][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-12.17.16:479][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-12.17.16:479][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-12.17.16:479][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-12.17.16:479][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-12.17.16:479][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-12.17.16:479][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-12.17.16:479][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-12.17.16:479][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-12.17.16:487][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-12.17.16:552][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-12.17.16:556][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-12.17.16:557][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-12.17.16:557][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-12.17.16:563][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.05-12.17.16:563][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.05-12.17.16:565][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.05-12.17.16:565][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.05-12.17.16:565][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.05-12.17.16:566][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.05-12.17.16:566][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.05-12.17.16:572][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.05-12.17.16:576][  0]LogInit: Undo buffer set to 256 MB
[2025.06.05-12.17.16:576][  0]LogInit: Transaction tracking system initialized
[2025.06.05-12.17.16:653][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.54ms
[2025.06.05-12.17.16:655][  0]LocalizationService: Localization service is disabled
[2025.06.05-12.17.17:002][  0]LogPython: Using Python 3.11.8
[2025.06.05-12.17.17:196][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/' took 0.26s
[2025.06.05-12.17.18:740][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.06.05-12.17.18:756][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (0 permutations).
[2025.06.05-12.17.18:763][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.05-12.17.18:857][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.05-12.17.18:858][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.05-12.17.18:925][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-12.17.18:925][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-12.17.18:927][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-12.17.18:927][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-12.17.18:927][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-12.17.18:927][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-12.17.18:963][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-12.17.18:963][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-12.17.18:971][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.05-12.17.18:971][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.05-12.17.19:020][  0]LogEditorDataStorage: Initializing
[2025.06.05-12.17.19:021][  0]LogEditorDataStorage: Initialized
[2025.06.05-12.17.19:030][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.06.05-12.17.19:054][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.05-12.17.19:072][  0]LogUnrealEdMisc: Loading editor; pre map load, took 12.688
[2025.06.05-12.17.19:073][  0]Cmd: MAP LOAD FILE="H:/P4/dev/Baoli/Content/Levels/DefaultLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.05-12.17.19:075][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.17.19:075][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.17.19:088][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-12.17.19:089][  0]InkPlusPlus: FStory::~FStory 0000043C18CED210
[2025.06.05-12.17.19:089][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.73ms
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/MHI.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performances/MHP_Scene1_01.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performance/MHP_Baoli.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:118][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-12.17.19:119][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.05-12.17.19:200][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.05-12.17.19:200][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Glossy (0x2CCA389A36D3E860)
[2025.06.05-12.17.19:200][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/material (0x38FB08B605AA9364)
[2025.06.05-12.17.19:201][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.05-12.17.19:201][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Dark (0xC267FEC07D768F2)
[2025.06.05-12.17.19:275][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.05-12.17.19:276][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVback (0x8B181E584DB8A471) /Game/Assets/TV/TVback (0x8B181E584DB8A471) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.05-12.17.19:672][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS (0xF21345B7066A3DF7)
[2025.06.05-12.17.20:518][  0]LogLinker: Warning: [AssetLog] H:\P4\dev\Baoli\Content\BaoliAssets\BrickInstances\Brick_low_001.uasset: VerifyImport: Failed to find script package for import object 'Package /Script/rdInst'
[2025.06.05-12.17.20:888][  0]LogAssetRegistry: Display: Asset registry cache written as 106.3 MiB to H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin
[2025.06.05-12.17.22:508][  0]LogEditorDomain: Display: Class /Script/rdInst.rdInstAssetUserData is imported by a package but does not exist in memory. EditorDomain keys for packages using it will be invalid if it still exists.
	To clear this message, resave packages that use the deleted class, or load its module earlier than the packages that use it are referenced.
[2025.06.05-12.17.26:115][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: WaitingForIo) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.26:121][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 334 to allow recursive sync load to finish
[2025.06.05-12.17.26:121][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.05-12.17.26:121][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: ExportsDone) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.26:121][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 335 to allow recursive sync load to finish
[2025.06.05-12.17.26:121][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.05-12.17.26:835][  0]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\CBP_SandboxCharacter.uasset: [Compiler] Input pin  Debug Session Unique Identifier  specifying non-default value no longer exists on node  Motion Match . Please refresh node or reset pin to default value to remove pin.
[2025.06.05-12.17.28:712][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: WaitingForIo) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.28:713][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 336 to allow recursive sync load to finish
[2025.06.05-12.17.28:713][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.05-12.17.28:713][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: ExportsDone) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-12.17.28:713][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 337 to allow recursive sync load to finish
[2025.06.05-12.17.28:713][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.05-12.17.28:821][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:822][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Handplant' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Handplant.MSS_FoleySound_Handplant' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:822][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Jump' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Jump.MSS_FoleySound_Jump' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:824][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Land' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Land.MSS_FoleySound_Land' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:825][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Run' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Run.MSS_FoleySound_Run' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:826][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunBackwards.MSS_FoleySound_RunBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:827][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunStrafe' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunStrafe.MSS_FoleySound_RunStrafe' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:828][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Scuff' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Scuff.MSS_FoleySound_Scuff' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:829][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffPivot' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffPivot.MSS_FoleySound_ScuffPivot' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:830][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffWall' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffWall.MSS_FoleySound_ScuffWall' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:831][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Tumble' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Tumble.MSS_FoleySound_Tumble' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:832][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Walk' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Walk.MSS_FoleySound_Walk' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.28:833][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_WalkBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_WalkBackwards.MSS_FoleySound_WalkBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-12.17.30:614][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.06.05-12.17.30:651][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.06.05-12.17.30:674][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_High...
[2025.06.05-12.17.30:674][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_High...
[2025.06.05-12.17.31:082][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.31:085][  0]LogSkeletalMesh: Built Skeletal Mesh [0.42s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High
[2025.06.05-12.17.31:095][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.06.05-12.17.31:705][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants_High...
[2025.06.05-12.17.32:056][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.32:057][  0]LogSkeletalMesh: Built Skeletal Mesh [0.35s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High
[2025.06.05-12.17.32:162][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.32:165][  0]LogSkeletalMesh: Built Skeletal Mesh [1.50s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High
[2025.06.05-12.17.32:478][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_High...
[2025.06.05-12.17.32:497][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_Cap_01_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-12.17.32:497][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelNut_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-12.17.32:498][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelLeaf_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-12.17.32:860][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.32:862][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High
[2025.06.05-12.17.34:351][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_hoodie_nrm_High...
[2025.06.05-12.17.34:806][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.34:811][  0]LogSkeletalMesh: Built Skeletal Mesh [0.46s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High
[2025.06.05-12.17.34:877][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Foley.SM_Foley.
[2025.06.05-12.17.34:877][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.05-12.17.34:877][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Ambient.SM_Ambient.
[2025.06.05-12.17.34:877][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.05-12.17.34:877][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Reverb.SM_Reverb.
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Jumps
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Stops
[2025.06.05-12.17.36:681][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.05-12.17.36:682][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.05-12.17.36:682][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.05-12.17.36:682][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.05-12.17.36:682][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.05-12.17.36:682][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.05-12.17.36:682][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.05-12.17.36:683][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.05-12.17.36:683][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.05-12.17.36:683][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.05-12.17.36:683][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.05-12.17.36:683][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.05-12.17.36:683][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.05-12.17.36:830][  0]LogPoseSearch: 25abbe71a7361c623bd3755ff5e1fbe7a56e7f94 - PSD_Dense_Jumps BeginCache
[2025.06.05-12.17.36:832][  0]LogPoseSearch: 25abbe71a7361c623bd3755ff5e1fbe7a56e7f94 - PSD_Dense_Jumps BuildIndex From Cache
[2025.06.05-12.17.36:844][  0]LogPoseSearch: 52bcfc861296fe46e902c3e1977b2a893663552c - PSD_Dense_Jumps_Far BeginCache
[2025.06.05-12.17.36:844][  0]LogPoseSearch: 52bcfc861296fe46e902c3e1977b2a893663552c - PSD_Dense_Jumps_Far BuildIndex From Cache
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Stops
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.05-12.17.36:855][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.05-12.17.36:856][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.05-12.17.36:856][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.05-12.17.36:908][  0]LogPoseSearch: c903d208ce6ce6d6d57c16e8029907a718a6c7e9 - PSD_Dense_Stand_Idle_Lands_Heavy BeginCache
[2025.06.05-12.17.36:908][  0]LogPoseSearch: c903d208ce6ce6d6d57c16e8029907a718a6c7e9 - PSD_Dense_Stand_Idle_Lands_Heavy BuildIndex From Cache
[2025.06.05-12.17.36:929][  0]LogPoseSearch: 2c155ec5c459344cf515e7737ccfee7b0c910810 - PSD_Dense_Stand_Run_Lands_Heavy BeginCache
[2025.06.05-12.17.36:930][  0]LogPoseSearch: 2c155ec5c459344cf515e7737ccfee7b0c910810 - PSD_Dense_Stand_Run_Lands_Heavy BuildIndex From Cache
[2025.06.05-12.17.36:960][  0]LogPoseSearch: 0647c6013483ddb2053168a7d66d3f931f221b6f - PSD_Dense_Stand_Walk_Lands_Heavy BeginCache
[2025.06.05-12.17.36:961][  0]LogPoseSearch: 0647c6013483ddb2053168a7d66d3f931f221b6f - PSD_Dense_Stand_Walk_Lands_Heavy BuildIndex From Cache
[2025.06.05-12.17.37:072][  0]LogPoseSearch: d6c5c07093dc0167059bbdea88559d05dbdcf369 - PSD_Traversal BeginCache
[2025.06.05-12.17.37:073][  0]LogPoseSearch: d6c5c07093dc0167059bbdea88559d05dbdcf369 - PSD_Traversal BuildIndex From Cache
[2025.06.05-12.17.37:082][  0]LogPoseSearch: 1f816991d0c81fa165c9258a64c0e110cb545466 - PSD_Dense_Jumps_FromTraversal BeginCache
[2025.06.05-12.17.37:082][  0]LogPoseSearch: 1f816991d0c81fa165c9258a64c0e110cb545466 - PSD_Dense_Jumps_FromTraversal BuildIndex From Cache
[2025.06.05-12.17.37:090][  0]LogPoseSearch: 44dfcd0439dfacb33784494a07f81e5c4107264f - PSD_Dense_Stand_Walk_FromTraversal BeginCache
[2025.06.05-12.17.37:090][  0]LogPoseSearch: 44dfcd0439dfacb33784494a07f81e5c4107264f - PSD_Dense_Stand_Walk_FromTraversal BuildIndex From Cache
[2025.06.05-12.17.37:091][  0]LogPoseSearch: 2ef3b5bccd181d28d29d656af18a9552f7025b60 - PSD_Dense_Stand_Run_SpinTransition BeginCache
[2025.06.05-12.17.37:092][  0]LogPoseSearch: 2ef3b5bccd181d28d29d656af18a9552f7025b60 - PSD_Dense_Stand_Run_SpinTransition BuildIndex From Cache
[2025.06.05-12.17.37:092][  0]LogPoseSearch: c3b27113a33ed0195cb86def79fac28ef88b955a - PSD_Dense_Stand_Walk_SpinTransition BeginCache
[2025.06.05-12.17.37:093][  0]LogPoseSearch: c3b27113a33ed0195cb86def79fac28ef88b955a - PSD_Dense_Stand_Walk_SpinTransition BuildIndex From Cache
[2025.06.05-12.17.37:103][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_btm_shorts_nrm...
[2025.06.05-12.17.37:106][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_top_crewneckt_nrm...
[2025.06.05-12.17.37:107][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.06.05-12.17.37:107][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants...
[2025.06.05-12.17.37:310][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.37:312][  0]LogSkeletalMesh: Built Skeletal Mesh [0.21s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm
[2025.06.05-12.17.37:393][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm...
[2025.06.05-12.17.37:430][  0]LogPoseSearch: 2fb236583b053e85334fffc7f28ad671a7ba6fcd - PSD_Dense_Crouch_Idle BeginCache
[2025.06.05-12.17.37:431][  0]LogPoseSearch: 2fb236583b053e85334fffc7f28ad671a7ba6fcd - PSD_Dense_Crouch_Idle BuildIndex From Cache
[2025.06.05-12.17.37:456][  0]LogPoseSearch: 3eaff9342445c24b7281b43f7e167cf00b79697d - PSD_Dense_Crouch_Loop BeginCache
[2025.06.05-12.17.37:457][  0]LogPoseSearch: 3eaff9342445c24b7281b43f7e167cf00b79697d - PSD_Dense_Crouch_Loop BuildIndex From Cache
[2025.06.05-12.17.37:474][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.37:476][  0]LogSkeletalMesh: Built Skeletal Mesh [0.37s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants
[2025.06.05-12.17.37:477][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.06.05-12.17.37:497][  0]LogPoseSearch: e5bb22fa3f0832f6b1de4740e665f8b8a0cc4a40 - PSD_Dense_Crouch_Pivot BeginCache
[2025.06.05-12.17.37:498][  0]LogPoseSearch: e5bb22fa3f0832f6b1de4740e665f8b8a0cc4a40 - PSD_Dense_Crouch_Pivot BuildIndex From Cache
[2025.06.05-12.17.37:539][  0]LogPoseSearch: 10463b9b5d9a2019be2ed12e5f4f9c3378a2c9df - PSD_Dense_Crouch_Start BeginCache
[2025.06.05-12.17.37:540][  0]LogPoseSearch: 10463b9b5d9a2019be2ed12e5f4f9c3378a2c9df - PSD_Dense_Crouch_Start BuildIndex From Cache
[2025.06.05-12.17.37:584][  0]LogPoseSearch: fb459193d12be90ae89d82deeb58b955bd5ded69 - PSD_Dense_Crouch_Stops BeginCache
[2025.06.05-12.17.37:585][  0]LogPoseSearch: fb459193d12be90ae89d82deeb58b955bd5ded69 - PSD_Dense_Crouch_Stops BuildIndex From Cache
[2025.06.05-12.17.37:868][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.37:871][  0]LogSkeletalMesh: Built Skeletal Mesh [0.40s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.06.05-12.17.37:873][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.06.05-12.17.38:292][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.38:297][  0]LogSkeletalMesh: Built Skeletal Mesh [0.42s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.06.05-12.17.38:298][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_shs_flipflops...
[2025.06.05-12.17.38:333][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.06.05-12.17.38:391][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.38:393][  0]LogSkeletalMesh: Built Skeletal Mesh [0.10s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops
[2025.06.05-12.17.38:394][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-12.17.38:621][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.38:626][  0]LogSkeletalMesh: Built Skeletal Mesh [1.52s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm
[2025.06.05-12.17.38:628][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_casualsneakers...
[2025.06.05-12.17.38:629][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.38:634][  0]LogSkeletalMesh: Built Skeletal Mesh [0.24s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-12.17.38:635][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_MC_FaceMesh...
[2025.06.05-12.17.38:636][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.38:644][  0]LogSkeletalMesh: Built Skeletal Mesh [1.54s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.06.05-12.17.38:645][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_Child2_FaceMesh...
[2025.06.05-12.17.38:968][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.38:972][  0]LogSkeletalMesh: Built Skeletal Mesh [0.34s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers
[2025.06.05-12.17.38:976][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.06.05-12.17.39:006][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.39:015][  0]LogSkeletalMesh: Built Skeletal Mesh [1.62s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm
[2025.06.05-12.17.39:018][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-12.17.39:277][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.39:281][  0]LogSkeletalMesh: Built Skeletal Mesh [0.26s] /Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-12.17.39:283][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-12.17.39:389][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'DefaultLevel'.
[2025.06.05-12.17.39:389][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-12.17.39:459][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.05-12.17.39:547][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.39:551][  0]LogSkeletalMesh: Built Skeletal Mesh [0.27s] /Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-12.17.39:642][  0]LogSkeletalMesh: Building Skeletal Mesh Kellan_FaceMesh...
[2025.06.05-12.17.40:096][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.40:100][  0]LogSkeletalMesh: Built Skeletal Mesh [0.46s] /Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh
[2025.06.05-12.17.50:713][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.50:732][  0]LogSkeletalMesh: Built Skeletal Mesh [12.09s] /Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh
[2025.06.05-12.17.50:811][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.50:827][  0]LogSkeletalMesh: Built Skeletal Mesh [12.19s] /Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh
[2025.06.05-12.17.50:868][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-12.17.50:885][  0]LogSkeletalMesh: Built Skeletal Mesh [11.91s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.06.05-12.17.50:959][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.05-12.17.51:167][  0]LogUObjectHash: Compacting FUObjectHashTables data took   2.53ms
[2025.06.05-12.17.51:171][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.05-12.17.51:171][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.101ms to complete.
[2025.06.05-12.17.51:180][  0]LogUnrealEdMisc: Total Editor Startup Time, took 44.797
[2025.06.05-12.17.51:363][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.06.05-12.17.51:477][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.17.51:539][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.17.51:598][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.17.51:657][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.17.51:703][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-12.17.51:704][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.05-12.17.51:704][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-12.17.51:704][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.05-12.17.51:704][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-12.17.51:705][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.05-12.17.51:705][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-12.17.51:705][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.05-12.17.51:705][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-12.17.51:705][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.05-12.17.51:706][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-12.17.51:706][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.05-12.17.51:706][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-12.17.51:706][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.05-12.17.51:706][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-12.17.51:707][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.05-12.17.51:707][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-12.17.51:707][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.05-12.17.51:707][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-12.17.51:707][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.05-12.17.51:852][  0]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-12.17.51:873][  0]LogStall: Startup...
[2025.06.05-12.17.51:877][  0]LogStall: Startup complete.
[2025.06.05-12.17.51:918][  0]LogLoad: (Engine Initialization) Total time: 45.54 seconds
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:47:25
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-12.17.52:256][  0]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-12.17.52:256][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.05-12.17.52:256][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.05-12.17.52:270][  0]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Config/DefaultEngine.ini'
[2025.06.05-12.17.52:532][  0]LogSlate: Took 0.000374 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.05-12.17.52:539][  0]LogSlate: Took 0.000234 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.05-12.17.52:542][  0]LogSlate: Took 0.000173 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.05-12.17.52:542][  0]LogSlate: Took 0.000137 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.05-12.17.52:607][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.06.05-12.17.52:607][  0]LogStreaming: Display: FlushAsyncLoading(342): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-12.17.52:613][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.06.05-12.17.52:613][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.06.05-12.17.52:613][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.05-12.17.52:719][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.05-12.17.52:719][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.06.05-12.17.52:720][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.06.05-12.17.52:720][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.06.05-12.17.52:720][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.05-12.17.52:786][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.05-12.17.52:786][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.06.05-12.17.52:846][  0]LogSlate: Took 0.000881 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.05-12.17.53:422][  0]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-12.17.53:506][  0]LogD3D12RHI: Creating RTPSO with 197 shaders (0 cached, 197 new) took 64.18 ms. Compile time 54.82 ms, link time 9.17 ms.
[2025.06.05-12.17.53:507][  0]LogD3D12RHI: Creating RTPSO with 24 shaders (0 cached, 24 new) took 17.01 ms. Compile time 6.51 ms, link time 10.47 ms.
[2025.06.05-12.17.53:601][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.17.53:614][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.06.05-12.17.53:614][  0]LogFab: Display: Logging in using exchange code
[2025.06.05-12.17.53:614][  0]LogFab: Display: Reading exchange code from commandline
[2025.06.05-12.17.53:614][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.06.05-12.17.53:614][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... started...
[2025.06.05-12.17.53:615][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... took 318 us
[2025.06.05-12.17.53:615][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.05-12.17.53:664][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.05-12.17.53:673][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 58.713 ms (total: 59.031 ms)
[2025.06.05-12.17.53:673][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-12.17.53:674][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-12.17.53:674][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-12.17.53:674][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_Glossy was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Glossy has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-12.17.53:674][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/material which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-12.17.53:674][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/material was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/material has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/material.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-12.17.53:674][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-12.17.53:674][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-12.17.53:674][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-12.17.53:674][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_Dark was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Dark has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-12.17.53:674][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-12.17.53:674][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVfront, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-12.17.53:674][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-12.17.53:674][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVback, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-12.17.53:674][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-12.17.53:674][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Washingmachine/steel, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-12.17.53:674][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BaoliEditor Win64 Development
[2025.06.05-12.17.53:863][  1]LogAssetRegistry: AssetRegistryGather time 0.3783s: AssetDataDiscovery 0.0616s, AssetDataGather 0.0876s, StoreResults 0.2291s. Wall time 42.1770s.
	NumCachedDirectories 0. NumUncachedDirectories 3058. NumCachedFiles 16173. NumUncachedFiles 0.
	BackgroundTickInterruptions 6.
[2025.06.05-12.17.53:946][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.06.05-12.17.53:952][  1]LogCollectionManager: Fixed up redirectors for 1 collections in 0.000053 seconds (updated 1 objects)
[2025.06.05-12.17.54:078][  1]LogMaterial: Display: Material /InterchangeAssets/gltf/M_Default.M_Default needed to have new flag set bUsedWithNanite !
[2025.06.05-12.17.54:103][  1]MapCheck: Warning: M_Default Material /InterchangeAssets/gltf/M_Default.M_Default was missing the usage flag bUsedWithNanite. If the material asset is not re-saved, it may not render correctly when run outside the editor. Fix
[2025.06.05-12.17.54:334][  1]LogSourceControl: Uncontrolled asset enumeration finished in 0.387696 seconds (Found 7982 uncontrolled assets)
[2025.06.05-12.17.54:347][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.06.05-12.17.54:750][  3]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 38.516811
[2025.06.05-12.17.54:752][  3]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.05-12.17.54:754][  3]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 38.649490
[2025.06.05-12.17.55:149][ 29]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.05-12.17.55:388][ 50]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.05-12.17.55:399][ 50]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.17.55:401][ 50]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.17.55:414][ 50]LogPoseSearch: UPoseSearchLibrary::UpdateMotionMatchingState invalid search result : ForceInterrupt [true], CanAdvance [false], Indexing [true], Databases [PSD_Dense_Stand_Idles] 
[2025.06.05-12.17.55:469][ 50]LogPoseSearch: 50dc2d6fc66385f69c3f4316520b5453bbe11082 - PSD_Sparse_Stand_Walk_Stops BeginCache
[2025.06.05-12.17.55:471][ 50]LogPoseSearch: 50dc2d6fc66385f69c3f4316520b5453bbe11082 - PSD_Sparse_Stand_Walk_Stops BuildIndex From Cache
[2025.06.05-12.17.55:472][ 50]LogPoseSearch: 7bd24e04e6106f1973227ab2f5dd916a0214b232 - PSD_Sparse_Stand_Walk_Starts BeginCache
[2025.06.05-12.17.55:473][ 50]LogPoseSearch: 7bd24e04e6106f1973227ab2f5dd916a0214b232 - PSD_Sparse_Stand_Walk_Starts BuildIndex From Cache
[2025.06.05-12.17.55:474][ 50]LogPoseSearch: 6c818e5f72e0650bcaac1c26c2a2af0097681f03 - PSD_Sparse_Stand_Walk_Pivots BeginCache
[2025.06.05-12.17.55:476][ 50]LogPoseSearch: a53f5d87ef31e2efb2cffa95e177eb2de30ea68b - PSD_Sparse_Stand_Walk_Loops BeginCache
[2025.06.05-12.17.55:478][ 50]LogPoseSearch: 64befbf5d961ed5ac476d534110da8895e69df96 - PSD_Sparse_Stand_Run_Stops BeginCache
[2025.06.05-12.17.55:480][ 50]LogPoseSearch: 2a285cc36d3969680553c96c4cc33908d8d326c8 - PSD_Sparse_Stand_Run_Starts BeginCache
[2025.06.05-12.17.55:480][ 50]LogPoseSearch: 6c818e5f72e0650bcaac1c26c2a2af0097681f03 - PSD_Sparse_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.05-12.17.55:480][ 50]LogPoseSearch: a53f5d87ef31e2efb2cffa95e177eb2de30ea68b - PSD_Sparse_Stand_Walk_Loops BuildIndex From Cache
[2025.06.05-12.17.55:480][ 50]LogPoseSearch: 64befbf5d961ed5ac476d534110da8895e69df96 - PSD_Sparse_Stand_Run_Stops BuildIndex From Cache
[2025.06.05-12.17.55:480][ 50]LogPoseSearch: 2a285cc36d3969680553c96c4cc33908d8d326c8 - PSD_Sparse_Stand_Run_Starts BuildIndex From Cache
[2025.06.05-12.17.55:481][ 50]LogPoseSearch: df8620db2cd0e21552cf91b1d3cd5aa199f8e4ba - PSD_Sparse_Stand_Run_Pivots BeginCache
[2025.06.05-12.17.55:482][ 50]LogPoseSearch: 20bcf12fcf57878594cfd98261e8196a850d3275 - PSD_Sparse_Stand_Run_Loops BeginCache
[2025.06.05-12.17.55:483][ 50]LogPoseSearch: df8620db2cd0e21552cf91b1d3cd5aa199f8e4ba - PSD_Sparse_Stand_Run_Pivots BuildIndex From Cache
[2025.06.05-12.17.55:484][ 50]LogPoseSearch: 20bcf12fcf57878594cfd98261e8196a850d3275 - PSD_Sparse_Stand_Run_Loops BuildIndex From Cache
[2025.06.05-12.17.55:484][ 50]LogPoseSearch: 093bf032f877970de263de3430d86e0291f0b843 - PSD_Dense_Stand_Walk_Stops BeginCache
[2025.06.05-12.17.55:486][ 50]LogPoseSearch: 58635c6253bdbf281b54ac2883681f632df4ac5a - PSD_Dense_Stand_TurnInPlace BeginCache
[2025.06.05-12.17.55:488][ 50]LogPoseSearch: aa8f1551ead5ebb7649a9c0cf3f329785019c495 - PSD_Dense_Stand_Run_FromTraversal BeginCache
[2025.06.05-12.17.55:489][ 50]LogPoseSearch: aa8f1551ead5ebb7649a9c0cf3f329785019c495 - PSD_Dense_Stand_Run_FromTraversal BuildIndex From Cache
[2025.06.05-12.17.55:489][ 50]LogPoseSearch: 58635c6253bdbf281b54ac2883681f632df4ac5a - PSD_Dense_Stand_TurnInPlace BuildIndex From Cache
[2025.06.05-12.17.55:489][ 50]LogPoseSearch: 3c18bdf4eafddb521f27fde8b603d69f34423c6e - PSD_Dense_Stand_Walk_Starts BeginCache
[2025.06.05-12.17.55:489][ 50]LogPoseSearch: 093bf032f877970de263de3430d86e0291f0b843 - PSD_Dense_Stand_Walk_Stops BuildIndex From Cache
[2025.06.05-12.17.55:491][ 50]LogPoseSearch: 3c18bdf4eafddb521f27fde8b603d69f34423c6e - PSD_Dense_Stand_Walk_Starts BuildIndex From Cache
[2025.06.05-12.17.55:491][ 50]LogPoseSearch: 75620188eb8a82eb61257a3e37062d404180d1eb - PSD_Dense_Stand_Walk_Pivots BeginCache
[2025.06.05-12.17.55:492][ 50]LogPoseSearch: 75620188eb8a82eb61257a3e37062d404180d1eb - PSD_Dense_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.05-12.17.55:492][ 50]LogPoseSearch: ba3a46564c5498cbb68507c1ccbbd585a3fc84d4 - PSD_Dense_Stand_Walk_Loops BeginCache
[2025.06.05-12.17.55:493][ 50]LogPoseSearch: ba3a46564c5498cbb68507c1ccbbd585a3fc84d4 - PSD_Dense_Stand_Walk_Loops BuildIndex From Cache
[2025.06.05-12.17.55:494][ 50]LogPoseSearch: 54f0f90cd23005d37fa1e725d1b27c86bd11c9b3 - PSD_Dense_Stand_Walk_Lands_Light BeginCache
[2025.06.05-12.17.55:494][ 50]LogPoseSearch: 54f0f90cd23005d37fa1e725d1b27c86bd11c9b3 - PSD_Dense_Stand_Walk_Lands_Light BuildIndex From Cache
[2025.06.05-12.17.55:495][ 50]LogPoseSearch: d9aafe8d3e39205b5f2061763c086eddbfb05fac - PSD_Dense_Stand_Run_Stops BeginCache
[2025.06.05-12.17.55:495][ 50]LogPoseSearch: d9aafe8d3e39205b5f2061763c086eddbfb05fac - PSD_Dense_Stand_Run_Stops BuildIndex From Cache
[2025.06.05-12.17.55:495][ 50]LogPoseSearch: d8d5fe007588aee9a3da79e9f1a75f582c1b9f99 - PSD_Dense_Stand_Run_Starts BeginCache
[2025.06.05-12.17.55:496][ 50]LogPoseSearch: d8d5fe007588aee9a3da79e9f1a75f582c1b9f99 - PSD_Dense_Stand_Run_Starts BuildIndex From Cache
[2025.06.05-12.17.55:497][ 50]LogPoseSearch: b53a49cb986c56c8a7f2626bb2d2b7429fbf1afb - PSD_Dense_Stand_Run_Pivots BeginCache
[2025.06.05-12.17.55:498][ 50]LogPoseSearch: 570ea0e9de87396e5797a333746ddd45125a84d2 - PSD_Dense_Stand_Run_Loops BeginCache
[2025.06.05-12.17.55:499][ 50]LogPoseSearch: 570ea0e9de87396e5797a333746ddd45125a84d2 - PSD_Dense_Stand_Run_Loops BuildIndex From Cache
[2025.06.05-12.17.55:500][ 50]LogPoseSearch: 360967ed92817bbce2fc65a7f0c26e89ee4b6373 - PSD_Dense_Stand_Run_Lands_Light BeginCache
[2025.06.05-12.17.55:500][ 50]LogPoseSearch: 360967ed92817bbce2fc65a7f0c26e89ee4b6373 - PSD_Dense_Stand_Run_Lands_Light BuildIndex From Cache
[2025.06.05-12.17.55:500][ 50]LogPoseSearch: b53a49cb986c56c8a7f2626bb2d2b7429fbf1afb - PSD_Dense_Stand_Run_Pivots BuildIndex From Cache
[2025.06.05-12.17.55:501][ 50]LogPoseSearch: 3faefe98948c1fcc929efd51f12856b3b9f3b947 - PSD_Dense_Stand_Idles BeginCache
[2025.06.05-12.17.55:502][ 50]LogPoseSearch: 3faefe98948c1fcc929efd51f12856b3b9f3b947 - PSD_Dense_Stand_Idles BuildIndex From Cache
[2025.06.05-12.17.55:503][ 50]LogPoseSearch: 914c12febe09d1d5c60f3a968f6f99ac6ee0695d - PSD_Dense_Stand_Idle_Lands_Light BeginCache
[2025.06.05-12.17.55:504][ 50]LogPoseSearch: 914c12febe09d1d5c60f3a968f6f99ac6ee0695d - PSD_Dense_Stand_Idle_Lands_Light BuildIndex From Cache
[2025.06.05-12.17.55:512][ 50]LogD3D12RHI: Creating RTPSO with 21 shaders (13 cached, 8 new) took 13.61 ms. Compile time 11.99 ms, link time 1.59 ms.
[2025.06.05-12.17.56:110][ 50]LogD3D12RHI: Creating RTPSO with 235 shaders (231 cached, 4 new) took 612.02 ms. Compile time 12.13 ms, link time 599.69 ms.
[2025.06.05-12.17.56:316][ 51]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 40.170815
[2025.06.05-12.17.56:319][ 51]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 348165457
[2025.06.05-12.17.56:319][ 51]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 40.170815, Update Interval: 336.287109
[2025.06.05-12.17.56:323][ 52]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.17.56:324][ 52]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.17.56:416][ 53]LogD3D12RHI: Creating RTPSO with 236 shaders (0 cached, 1 new) took 87.71 ms. Compile time 2.04 ms, link time 85.58 ms.
[2025.06.05-12.17.56:473][ 53]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.05-12.17.56:482][ 53]LogActor: Warning: BP_Almirah_C /Engine/Transient.World_1:PersistentLevel.BP_Almirah_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.05-12.17.57:396][126]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.05-12.17.57:723][129]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.17.57:724][129]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.17.57:885][130]LogD3D12RHI: Creating RTPSO with 237 shaders (0 cached, 1 new) took 101.38 ms. Compile time 2.04 ms, link time 99.23 ms.
[2025.06.05-12.17.58:053][132]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.05-12.17.58:063][132]LogActor: Warning: BP_Bed_C /Engine/Transient.World_3:PersistentLevel.BP_Bed_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.05-12.17.58:103][133]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.05-12.17.58:158][134]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.05-12.17.58:291][137]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.17.58:291][137]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.17.58:585][143]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.06.05-12.17.58:595][143]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.CBP_SandboxCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.17.58:595][143]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.CBP_SandboxCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.17.58:673][145]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.17.58:673][145]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.24:962][241]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-12.18.25:359][256]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.25:360][256]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.28:673][389]LogAssetEditorSubsystem: Opening Asset editor for AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter
[2025.06.05-12.18.28:673][389]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.06.05-12.18.28:720][389]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.06.05-12.18.28:729][389]LogStreaming: Display: FlushAsyncLoading(356): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-12.18.28:750][389]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-12.18.28:753][389]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.28:753][389]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.28:774][389]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-12.18.31:995][389]LogBlueprintEditor: Perf: 3.2 total seconds to load all 13 blueprint libraries in project. Avoid references to content in blueprint libraries to shorten this time.
[2025.06.05-12.18.31:995][389]LogBlueprintEditor: Perf: 3.1 seconds loading: /Game/UltraDynamicSky/Blueprints/Functions/UltraDynamicWeather_Functions
[2025.06.05-12.18.33:140][389]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.06.05-12.18.33:171][389]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-12.18.33:275][389]LogAssetEditorSubsystem: Opening Asset editor for ChooserTable /Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Dense.CHT_PoseSearchDatabases_Dense
[2025.06.05-12.18.33:362][389]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-12.18.33:563][389]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.33:563][389]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.33:631][389]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.06.05-12.18.33:664][389]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:47:25
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-12.18.33:692][389]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-12.18.33:727][389]LogSlate: Took 0.000146 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.06.05-12.18.33:894][390]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:47:25
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-12.18.33:898][390]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-12.18.33:924][391]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.33:926][391]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-12.18.33:926][391]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:47:25
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-12.18.33:927][391]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-12.18.33:955][392]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_7
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.33:955][392]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_7
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.33:957][392]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.34:459][393]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_8
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.34:459][393]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_8
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.34:487][394]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.34:512][395]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.34:527][396]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.34:541][397]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.34:555][398]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.34:567][399]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_8:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.18.50:257][414]LogSlate: Took 0.000120 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.06.05-12.18.50:263][414]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-12.18.51:290][482]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-12.19.09:823][780]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.05-12.19.32:610][472]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Almirah.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bed.uasset'
[2025.06.05-12.19.32:841][486]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter
[2025.06.05-12.19.32:841][486]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.06.05-12.19.32:856][486]LogTemp: Display: rdBPTools: Failed to load rdBPTools config ini file
[2025.06.05-12.19.32:856][486]LogTemp: Display: Handle AssetOpenedInEditor - BlueprintEditor...
[2025.06.05-12.19.32:859][486]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-12.19.32:898][486]LogStreaming: Display: FlushAsyncLoading(557): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-12.19.32:939][486]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:47:25
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-12.19.33:201][486]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-12.19.33:389][486]LogStreaming: Display: FlushAsyncLoading(559): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-12.19.33:548][487]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:47:25
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-12.19.33:558][487]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-12.19.33:624][489]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.06.05-12.19.33:722][490]LogD3D12RHI: Creating RTPSO with 241 shaders (0 cached, 1 new) took 72.36 ms. Compile time 2.05 ms, link time 70.26 ms.
[2025.06.05-12.19.33:860][494]LogD3D12RHI: Creating RTPSO with 242 shaders (0 cached, 1 new) took 74.32 ms. Compile time 2.13 ms, link time 72.13 ms.
[2025.06.05-12.19.33:936][494]LogD3D12RHI: Creating RTPSO with 243 shaders (0 cached, 1 new) took 75.19 ms. Compile time 1.74 ms, link time 73.37 ms.
[2025.06.05-12.19.34:626][517]LogD3D12RHI: Creating RTPSO with 244 shaders (0 cached, 1 new) took 70.29 ms. Compile time 1.52 ms, link time 68.71 ms.
[2025.06.05-12.19.34:631][517]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.06.05-12.19.38:382][747]LogSlate: Took 0.000213 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/NotoSansMath-Regular.ttf' (574K)
[2025.06.05-12.20.59:043][433]LogUObjectHash: Compacting FUObjectHashTables data took   3.53ms
[2025.06.05-12.20.59:682][433]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_13
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.20.59:682][433]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_13
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.21.00:022][434]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_14
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.21.00:022][434]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_14
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.21.50:215][ 44]LogUObjectHash: Compacting FUObjectHashTables data took   3.14ms
[2025.06.05-12.21.51:621][ 44]LogSlate: Window 'Save Content' being destroyed
[2025.06.05-12.22.44:456][822]LogUObjectHash: Compacting FUObjectHashTables data took   2.47ms
[2025.06.05-12.22.46:232][822]LogSlate: Window 'Save Content' being destroyed
[2025.06.05-12.22.46:270][822]LogStall: Shutdown...
[2025.06.05-12.22.46:270][822]LogStall: Shutdown complete.
[2025.06.05-12.22.46:291][822]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.05-12.22.46:302][822]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:302][822]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:320][822]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:320][822]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:332][822]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:333][822]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:358][822]LogSlate: Window 'CHT_PoseSearchDatabases_Dense' being destroyed
[2025.06.05-12.22.46:400][822]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:400][822]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:468][822]LogSlate: Window 'Baoli - Unreal Editor' being destroyed
[2025.06.05-12.22.46:520][822]Cmd: QUIT_EDITOR
[2025.06.05-12.22.46:520][823]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.06.05-12.22.46:523][823]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.06.05-12.22.46:524][823]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.06.05-12.22.46:524][823]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.06.05-12.22.46:529][823]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:530][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:536][823]LogStylusInput: Shutting down StylusInput subsystem.
[2025.06.05-12.22.46:536][823]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.06.05-12.22.46:542][823]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:542][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:549][823]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:549][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:553][823]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:553][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:560][823]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:560][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:566][823]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:566][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:570][823]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:570][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:577][823]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:577][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:583][823]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:583][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:588][823]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:588][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:593][823]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:593][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:599][823]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.22.46:599][823]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.22.46:604][823]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.06.05-12.22.46:607][823]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.06.05-12.22.46:607][823]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.06.05-12.22.46:607][823]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.06.05-12.22.46:609][823]LogGameFeatures: Shutting down game features subsystem
[2025.06.05-12.22.46:609][823]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.06.05-12.22.46:609][823]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.06.05-12.22.46:609][823]LogAudio: Display: Audio Device unregistered from world 'DefaultLevel'.
[2025.06.05-12.22.46:609][823]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.06.05-12.22.46:609][823]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.05-12.22.46:612][823]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.05-12.22.46:618][823]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.06.05-12.22.46:618][823]LogAudio: Display: Audio Device Manager Shutdown
[2025.06.05-12.22.46:620][823]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.06.05-12.22.46:620][823]LogExit: Preparing to exit.
[2025.06.05-12.22.46:700][823]LogUObjectHash: Compacting FUObjectHashTables data took   2.12ms
[2025.06.05-12.22.46:733][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:733][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:733][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_null_above_selected_PY.add_null_above_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:733][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:733][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_all' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:733][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_x' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:733][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_y' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:733][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_z' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:733][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_scale' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_name_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/set_bone_reference_pose_PY.set_bone_reference_pose' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/ControlRigWorkflows/workflow_fbik_import_ik_rig_PY.import_ik_rig_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.46:734][823]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.ControlOutputFormat' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.22.47:352][823]LogEditorDataStorage: Deinitializing
[2025.06.05-12.22.48:658][823]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.06.05-12.22.48:669][823]LogExit: Editor shut down
[2025.06.05-12.22.48:671][823]LogExit: Transaction tracking system shut down
[2025.06.05-12.22.49:117][823]LogExit: Object subsystem successfully closed.
[2025.06.05-12.22.49:192][823]LogShaderCompilers: Display: Shaders left to compile 0
