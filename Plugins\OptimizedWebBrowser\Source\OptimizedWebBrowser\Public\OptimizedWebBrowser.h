// Copyright Epic Games, Inc. All Rights Reserved.
// Modifications Copyright (C) 2025 Anshul <PERSON>

#pragma once

#include "Components/Widget.h"
#include "OptimizedWebBrowser.generated.h"

// Forward declarations
enum class EWebBrowserConsoleLogSeverity;
class SWebBrowser;

/**
 * An optimized version of the WebBrowser widget that improves performance
 * while maintaining all functionality.
 */
UCLASS()
class OPTIMIZEDWEBBROWSER_API UOptimizedWebBrowser : public UWidget
{
    GENERATED_UCLASS_BODY()

public:
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnUrlChanged, const FText&, Text);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBeforePopup, FString, URL, FString, Frame);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnConsoleMessage, const FString&, Message, const FString&, Source, int32, Line);

    /**
     * Load the specified URL
     *
     * @param NewURL New URL to load
     */
    UFUNCTION(BlueprintCallable, Category="Web Browser")
    void LoadURL(FString NewURL);

    /**
     * Load a string as data to create a web page
     *
     * @param Contents String to load
     * @param DummyURL Dummy URL for the page
     */
    UFUNCTION(BlueprintCallable, Category="Web Browser")
    void LoadString(FString Contents, FString DummyURL);

    /**
    * Executes a JavaScript string in the context of the web page
    *
    * @param ScriptText JavaScript string to execute
    */
    UFUNCTION(BlueprintCallable, Category = "Web Browser")
    void ExecuteJavascript(const FString& ScriptText);

    /**
     * Get the current title of the web page
     */
    UFUNCTION(BlueprintCallable, Category="Web Browser")
    FText GetTitleText() const;

    /**
    * Gets the currently loaded URL.
    *
    * @return The URL, or empty string if no document is loaded.
    */
    UFUNCTION(BlueprintCallable, Category = "Web Browser")
    FString GetUrl() const;

    /**
    * Logs a message to the browser's JavaScript console
    *
    * @param Message The message to log to the console
    */
    UFUNCTION(BlueprintCallable, Category = "Web Browser", meta = (DisplayName = "Log To Console", Keywords = "javascript console log debug"))
    void LogToConsole(const FString& Message);

    /** Called when the Url changes. */
    UPROPERTY(BlueprintAssignable, Category = "Web Browser|Event")
    FOnUrlChanged OnUrlChanged;

    /** Called when a popup is about to spawn. */
    UPROPERTY(BlueprintAssignable, Category = "Web Browser|Event")
    FOnBeforePopup OnBeforePopup;

    /** Called when the browser has console spew to print */
    UPROPERTY(BlueprintAssignable, Category = "Web Browser|Event")
    FOnConsoleMessage OnConsoleMessage;

    /**
     * Set the browser frame rate. Higher values improve smoothness but increase CPU usage.
     * Default is 24 fps. Recommended values: 30-60 for smoother experience, 15-24 for lower CPU usage.
     *
     * @param FrameRate The new frame rate to set
     */
    UFUNCTION(BlueprintCallable, Category = "Web Browser|Performance")
    void SetBrowserFrameRate(int32 FrameRate);

    /**
     * Enable or disable hardware acceleration. Hardware acceleration can improve performance
     * but may cause issues on some systems.
     *
     * @param bEnabled Whether hardware acceleration should be enabled
     */
    UFUNCTION(BlueprintCallable, Category = "Web Browser|Performance")
    void SetHardwareAccelerationEnabled(bool bEnabled);

    /**
     * Set the background color of the browser. Using opaque colors can improve performance.
     *
     * @param Color The background color to set
     */
    UFUNCTION(BlueprintCallable, Category = "Web Browser|Performance")
    void SetBackgroundColor(FColor Color);

    /**
     * Enable or disable throttling of background tabs. When enabled, tabs that are not
     * visible will use less CPU resources.
     *
     * @param bEnabled Whether background tab throttling should be enabled
     */
    UFUNCTION(BlueprintCallable, Category = "Web Browser|Performance")
    void SetBackgroundTabThrottlingEnabled(bool bEnabled);

public:
    //~ Begin UWidget interface
    virtual void SynchronizeProperties() override;
    // End UWidget interface

    virtual void ReleaseSlateResources(bool bReleaseChildren) override;

#if WITH_EDITOR
    virtual const FText GetPaletteCategory() override;
#endif

protected:
    /** URL that the browser will initially navigate to. The URL should include the protocol, eg http:// */
    UPROPERTY(EditAnywhere, Category=Appearance)
    FString InitialURL;

    /** Should the browser window support transparency. */
    UPROPERTY(EditAnywhere, Category=Appearance)
    bool bSupportsTransparency;

    /** The frame rate at which the browser will render. Higher values are smoother but use more CPU. */
    UPROPERTY(EditAnywhere, Category=Performance, meta=(ClampMin="15", ClampMax="120"))
    int32 BrowserFrameRate;

    /** Whether hardware acceleration is enabled. */
    UPROPERTY(EditAnywhere, Category=Performance)
    bool bHardwareAccelerationEnabled;

    /** Background color of the browser. Using opaque colors can improve performance. */
    UPROPERTY(EditAnywhere, Category=Performance)
    FColor BackgroundColor;

    /** Whether to throttle background tabs to save CPU resources. */
    UPROPERTY(EditAnywhere, Category=Performance)
    bool bBackgroundTabThrottlingEnabled;

protected:
    TSharedPtr<class SWebBrowser> WebBrowserWidget;

protected:
    // UWidget interface
    virtual TSharedRef<SWidget> RebuildWidget() override;
    // End of UWidget interface

    void HandleOnConsoleMessage(const FString& Message, const FString& Source, int32 Line, EWebBrowserConsoleLogSeverity Severity);
    void HandleOnUrlChanged(const FText& Text);
    bool HandleOnBeforePopup(FString URL, FString Frame);
};
