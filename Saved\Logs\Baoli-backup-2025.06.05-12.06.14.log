﻿Log file open, 06/05/25 17:28:24
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=37148)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: Baoli
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\P4\dev\Baoli\Baoli.uproject -skipcompile""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.298032
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-135873A442D56B0C2D0D948233D2D17A
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/P4/dev/Baoli/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.05 seconds
LogConfig: Display: Loading Android ini files took 0.05 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.05 seconds
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading Unix ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading VisionOS ini files took 0.06 seconds
LogConfig: Display: Loading TVOS ini files took 0.06 seconds
LogConfig: Display: Loading Windows ini files took 0.06 seconds
LogConfig: Display: Loading Linux ini files took 0.06 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin Chooser
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin JsonBlueprintUtilities
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GameFeatures
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin ModularGameplay
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin ScriptableToolsFramework
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebBrowserWidget
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogAssetRegistry: Display: Asset registry cache read as 106.3 MiB from H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationLocomotionLibrary
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationWarping
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin BlendStack
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin MotionWarping
LogPluginManager: Mounting Engine plugin PoseSearch
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin AssetReferenceRestrictions
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ScriptableToolsEditorMode
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin AxFImporter
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin MDLImporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin ActorPalette
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GPULightmass
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin NiagaraFluids
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin USDImporter
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin MotionTrajectory
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin AnalyticsBlueprintLibrary
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin Reflex
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Project plugin Inkpot
LogPluginManager: Mounting Project plugin OptimizedWebBrowser
LogPluginManager: Mounting Project plugin SnappingHelper
LogPluginManager: Mounting Project plugin rdBPtools
LogPluginManager: Mounting Project plugin PlatformFunctions
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogGPULightmass: GPULightmass module is loaded
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: NVIDIA GeForce RTX 2080 Ti
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: -skipcompile
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 36
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.66ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Chooser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'JsonBlueprintUtilities' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataRegistry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameFeatures' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GeometryScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModularGameplay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ScriptableToolsFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebBrowserWidget' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationLocomotionLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationWarping' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MotionWarping' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PoseSearch' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetReferenceRestrictions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ScriptableToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AxFImporter' had been unloaded. Reloading on-demand took 0.07ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/MDLImporter.ini) has wildcard redirect /DatasmithContent/Materials/MDL/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'ActorPalette' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GPULightmass' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/NiagaraFluids.ini) has wildcard redirect /NiagaraSimulationStages/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'USDImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MotionTrajectory' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnalyticsBlueprintLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Reflex' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'Inkpot' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OptimizedWebBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SnappingHelper' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'rdBPtools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformFunctions' had been unloaded. Reloading on-demand took 0.07ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.06.05-11.58.24:765][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.05-11.58.24:765][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.05-11.58.24:765][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.05-11.58.24:765][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.05-11.58.24:765][  0]LogConfig: CVar [[r.Mobile.EnableNoPrecomputedLightingCSMShader:1]] deferred - dummy variable created
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.AllowGlobalClipPlane:0]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.CustomDepth:3]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.VirtualTextures:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.AntiAliasingMethod:4]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.ReflectionCaptureResolution:32]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs:0]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.MegaLights.EnableForProject:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.PathTracing:0]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.RayTracing.Shadows:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.RayTracing.UseTextureLod:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject:0]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.CustomDepthTemporalAAJitter:1]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default:75.000000]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default.Desktop.Mode:0]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.Shadow.CSMCaching:0]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.MSAACount:8]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileSize:32]]
[2025.06.05-11.58.24:765][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileBorderSize:2]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.MaxTextureSize:4096]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.UseCompression:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.VT.TileBorderSize:4]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.DefaultTexelsPerVertex:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.AllowStaticLighting:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.Shadow.UnbuiltPreviewInGame:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.NormalMapsForStaticLighting:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing.LightingMode:0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.VirtualTexturedLightmaps:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.Lumen.ScreenTracingSource:0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure:0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Bias:0.000000]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Method:2]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.vt.rvt.HighQualityPerPixelHeight:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.05-11.58.24:766][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.05-11.58.24:766][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.05-11.58.24:766][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.05-11.58.24:766][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.05-11.58.24:770][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.05-11.58.24:770][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.06.05-11.58.24:770][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.05-11.58.24:770][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.05-11.58.24:770][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.05-11.58.24:770][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.05-11.58.24:773][  0]LogRHI: Using Default RHI: D3D12
[2025.06.05-11.58.24:773][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.05-11.58.24:773][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.05-11.58.24:778][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.05-11.58.24:778][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.05-11.58.24:903][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.05-11.58.24:903][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-11.58.24:903][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 3 output[s]
[2025.06.05-11.58.24:904][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.05-11.58.24:904][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.05-11.58.25:049][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.05-11.58.25:049][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-11.58.25:049][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-11.58.25:050][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.05-11.58.25:050][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.05-11.58.25:055][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.05-11.58.25:055][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.05-11.58.25:055][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-11.58.25:193][  0]LogD3D12RHI: Found D3D12 adapter 3: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.05-11.58.25:193][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.05-11.58.25:193][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.05-11.58.25:193][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.05-11.58.25:193][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.05-11.58.25:193][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.05-11.58.25:193][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.05-11.58.25:194][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.05-11.58.25:194][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.05-11.58.25:194][  0]LogHAL: Display: Platform has ~ 64 GB [68630138880 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.05-11.58.25:195][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.05-11.58.25:195][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.05-11.58.25:195][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.05-11.58.25:195][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.05-11.58.25:195][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.05-11.58.25:195][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.05-11.58.25:195][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.05-11.58.25:195][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.05-11.58.25:195][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.05-11.58.25:195][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.05-11.58.25:195][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.05-11.58.25:195][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.05-11.58.25:195][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.06.05-11.58.25:195][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/P4/dev/Baoli/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.05-11.58.25:195][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.06.05-11.58.25:195][  0]LogInit: User: Shashank
[2025.06.05-11.58.25:195][  0]LogInit: CPU Page size=4096, Cores=16
[2025.06.05-11.58.25:195][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.05-11.58.25:976][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.06.05-11.58.25:976][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.05-11.58.25:976][  0]LogMemory: Process Physical Memory: 697.77 MB used, 731.71 MB peak
[2025.06.05-11.58.25:976][  0]LogMemory: Process Virtual Memory: 783.05 MB used, 783.57 MB peak
[2025.06.05-11.58.25:976][  0]LogMemory: Physical Memory: 28233.04 MB used,  37217.76 MB free, 65450.80 MB total
[2025.06.05-11.58.25:976][  0]LogMemory: Virtual Memory: 36315.71 MB used,  33231.09 MB free, 69546.80 MB total
[2025.06.05-11.58.25:976][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.05-11.58.25:981][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.05-11.58.25:988][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.05-11.58.25:989][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.05-11.58.25:989][  0]LogInit: Using OS detected language (en-GB).
[2025.06.05-11.58.25:989][  0]LogInit: Using OS detected locale (en-IN).
[2025.06.05-11.58.25:992][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.05-11.58.25:992][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.05-11.58.26:286][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.05-11.58.26:286][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.06.05-11.58.26:286][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.05-11.58.26:300][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.05-11.58.26:300][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.05-11.58.26:421][  0]LogRHI: Using Default RHI: D3D12
[2025.06.05-11.58.26:421][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.05-11.58.26:421][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.05-11.58.26:421][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.05-11.58.26:421][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.05-11.58.26:421][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.05-11.58.26:421][  0]LogWindows: Attached monitors:
[2025.06.05-11.58.26:421][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY1' [PRIMARY]
[2025.06.05-11.58.26:421][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY5'
[2025.06.05-11.58.26:421][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY6'
[2025.06.05-11.58.26:421][  0]LogWindows: Found 3 attached monitors.
[2025.06.05-11.58.26:421][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.05-11.58.26:421][  0]LogRHI: RHI Adapter Info:
[2025.06.05-11.58.26:421][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.06.05-11.58.26:421][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.05-11.58.26:421][  0]LogRHI:      Driver Date: 4-25-2025
[2025.06.05-11.58.26:421][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.06.05-11.58.26:450][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.05-11.58.26:521][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.05-11.58.26:521][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.05-11.58.26:614][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: Raster order views are supported
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.05-11.58.26:614][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.05-11.58.26:640][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000062B6E375300)
[2025.06.05-11.58.26:640][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000062B6E375580)
[2025.06.05-11.58.26:640][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000062B6E375800)
[2025.06.05-11.58.26:640][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.05-11.58.26:640][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.05-11.58.26:640][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.06.05-11.58.26:640][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.06.05-11.58.26:640][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.05-11.58.26:640][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.05-11.58.26:654][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.05-11.58.26:658][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.05-11.58.26:666][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all'
[2025.06.05-11.58.26:666][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all" ]
[2025.06.05-11.58.26:691][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.05-11.58.26:691][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.05-11.58.26:691][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.05-11.58.26:691][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.05-11.58.26:691][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.05-11.58.26:691][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.06.05-11.58.26:691][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.05-11.58.26:691][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.05-11.58.26:691][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.05-11.58.26:719][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.05-11.58.26:719][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.05-11.58.26:719][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.05-11.58.26:719][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.05-11.58.26:719][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.05-11.58.26:719][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.05-11.58.26:719][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.05-11.58.26:719][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.05-11.58.26:719][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.05-11.58.26:719][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.05-11.58.26:735][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.05-11.58.26:735][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.05-11.58.26:750][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.05-11.58.26:750][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.05-11.58.26:750][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.05-11.58.26:750][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.05-11.58.26:766][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.06.05-11.58.26:766][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.06.05-11.58.26:766][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.06.05-11.58.26:782][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.05-11.58.26:782][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.05-11.58.26:782][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.05-11.58.26:782][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.05-11.58.26:797][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.05-11.58.26:797][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.05-11.58.26:815][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.05-11.58.26:815][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.05-11.58.26:815][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.05-11.58.26:815][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.05-11.58.26:815][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.05-11.58.26:861][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_METAL
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.05-11.58.26:865][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.05-11.58.26:865][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.06.05-11.58.26:865][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.06.05-11.58.26:866][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.05-11.58.26:866][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/P4/dev/Baoli/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.05-11.58.26:866][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.05-11.58.26:866][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/P4/dev/Baoli/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.05-11.58.26:866][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.05-11.58.26:928][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.06.05-11.58.26:928][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.05-11.58.26:928][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.05-11.58.26:929][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.05-11.58.26:929][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.05-11.58.26:930][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.05-11.58.26:930][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.06.05-11.58.26:930][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 37440 --child-id Zen_37440_Startup'
[2025.06.05-11.58.27:011][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.05-11.58.27:011][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.082 seconds
[2025.06.05-11.58.27:013][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.05-11.58.27:017][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.06.05-11.58.27:017][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.04ms. RandomReadSpeed=1352.27MBs, RandomWriteSpeed=313.40MBs. Assigned SpeedClass 'Local'
[2025.06.05-11.58.27:018][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.05-11.58.27:018][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.05-11.58.27:018][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.05-11.58.27:018][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.05-11.58.27:018][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.05-11.58.27:018][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.05-11.58.27:018][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.05-11.58.27:018][  0]LogShaderCompilers: Guid format shader working directory is 33 characters bigger than the processId version (H:/P4/dev/Baoli/Intermediate/Shaders/WorkingDirectory/37440/).
[2025.06.05-11.58.27:018][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/1B93263746A683BECFB89EAEEAF9B120/'.
[2025.06.05-11.58.27:018][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.06.05-11.58.27:018][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.06.05-11.58.27:020][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/P4/dev/Baoli/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.05-11.58.27:020][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.05-11.58.27:500][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.05-11.58.28:083][  0]LogSlate: Using FreeType 2.10.0
[2025.06.05-11.58.28:083][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.05-11.58.28:084][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-11.58.28:084][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-11.58.28:085][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-11.58.28:086][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-11.58.28:086][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-11.58.28:086][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-11.58.28:111][  0]LogAssetRegistry: FAssetRegistry took 0.0034 seconds to start up
[2025.06.05-11.58.28:112][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.05-11.58.28:117][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.05-11.58.28:286][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-11.58.28:287][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.05-11.58.28:287][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.05-11.58.28:287][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.05-11.58.28:298][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.05-11.58.28:298][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.05-11.58.28:322][  0]LogDeviceProfileManager: Active device profile: [0000062B88718200][0000062B7B5DC000 66] WindowsEditor
[2025.06.05-11.58.28:322][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.05-11.58.28:322][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.05-11.58.28:325][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.06.05-11.58.28:325][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.06.05-11.58.28:356][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:356][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.06.05-11.58.28:356][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-11.58.28:357][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:357][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.06.05-11.58.28:357][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-11.58.28:357][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:357][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.06.05-11.58.28:357][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-11.58.28:357][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:357][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.06.05-11.58.28:357][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-11.58.28:358][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:359][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-11.58.28:359][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:359][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.06.05-11.58.28:359][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.05-11.58.28:359][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:359][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.06.05-11.58.28:359][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.06.05-11.58.28:361][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.05-11.58.28:534][  0]LogMeshReduction: Display: Mesh reduction module (r.MeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-11.58.28:534][  0]LogMeshReduction: Display: Skeletal mesh reduction module (r.SkeletalMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-11.58.28:534][  0]LogMeshReduction: Display: HLOD mesh reduction module (r.ProxyLODMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.05-11.58.28:547][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.05-11.58.28:547][  0]LogMeshReduction: Display: Using InstaLODMeshReduction for automatic skeletal mesh reduction
[2025.06.05-11.58.28:547][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.05-11.58.28:547][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.05-11.58.28:547][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.05-11.58.28:679][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.77ms
[2025.06.05-11.58.28:700][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.49ms
[2025.06.05-11.58.28:712][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.49ms
[2025.06.05-11.58.28:714][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.46ms
[2025.06.05-11.58.28:894][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.05-11.58.28:894][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.05-11.58.28:900][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.05-11.58.28:900][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.05-11.58.28:902][  0]LogLiveCoding: Display: First instance in process group "UE_Baoli_0x736adef1", spawning console
[2025.06.05-11.58.28:905][  0]LogLiveCoding: Display: Waiting for server
[2025.06.05-11.58.28:928][  0]LogSlate: Border
[2025.06.05-11.58.28:928][  0]LogSlate: BreadcrumbButton
[2025.06.05-11.58.28:928][  0]LogSlate: Brushes.Title
[2025.06.05-11.58.28:928][  0]LogSlate: Default
[2025.06.05-11.58.28:928][  0]LogSlate: Icons.Save
[2025.06.05-11.58.28:928][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.05-11.58.28:928][  0]LogSlate: ListView
[2025.06.05-11.58.28:928][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.05-11.58.28:928][  0]LogSlate: SoftwareCursor_Grab
[2025.06.05-11.58.28:928][  0]LogSlate: TableView.DarkRow
[2025.06.05-11.58.28:928][  0]LogSlate: TableView.Row
[2025.06.05-11.58.28:928][  0]LogSlate: TreeView
[2025.06.05-11.58.29:022][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.05-11.58.29:024][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.867 ms
[2025.06.05-11.58.29:027][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.05-11.58.29:034][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.05-11.58.29:053][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.05-11.58.29:053][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.05-11.58.29:053][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.05-11.58.29:054][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.06.05-11.58.29:314][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.05-11.58.29:318][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.05-11.58.29:318][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.05-11.58.29:318][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:62775'.
[2025.06.05-11.58.29:321][  0]LogUdpMessaging: Display: Added local interface '192.168.1.5' to multicast group '230.0.0.1:6666'
[2025.06.05-11.58.29:321][  0]LogUdpMessaging: Display: Added local interface '172.24.112.1' to multicast group '230.0.0.1:6666'
[2025.06.05-11.58.29:321][  0]LogUdpMessaging: Display: Added local interface '172.29.240.1' to multicast group '230.0.0.1:6666'
[2025.06.05-11.58.29:438][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.05-11.58.29:438][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.06.05-11.58.29:438][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.06.05-11.58.29:438][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.05-11.58.29:438][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.05-11.58.29:603][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.05-11.58.29:603][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.05-11.58.29:616][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.05-11.58.29:780][  0]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.06.05-11.58.29:780][  0]LogCEFBrowser: CEF GPU acceleration enabled
[2025.06.05-11.58.29:973][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.50ms
[2025.06.05-11.58.30:080][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: B76D5308ACDD4E8E8000000000003E00 | Instance: 25EFA0B046071B75AD1A2CAB07F26DB0 (DESKTOP-E41IK6R-37440).
[2025.06.05-11.58.30:190][  0]LogTemp: Warning: ✓ AI Perception system enabled and events bound
[2025.06.05-11.58.30:261][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.06.05-11.58.30:285][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.05-11.58.30:321][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-11.58.30:321][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-11.58.30:330][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.05-11.58.30:340][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.05-11.58.30:357][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.05-11.58.30:357][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.05-11.58.30:450][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.06.05-11.58.30:450][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.06.05-11.58.30:450][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.06.05-11.58.30:450][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.06.05-11.58.30:450][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.06.05-11.58.30:451][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.06.05-11.58.30:452][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.06.05-11.58.30:452][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.06.05-11.58.30:452][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.06.05-11.58.30:452][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.06.05-11.58.30:453][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.06.05-11.58.30:453][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.06.05-11.58.30:453][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.06.05-11.58.30:454][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.06.05-11.58.30:454][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.06.05-11.58.30:455][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.06.05-11.58.30:455][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.06.05-11.58.30:455][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.06.05-11.58.30:456][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.06.05-11.58.30:456][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.06.05-11.58.30:457][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.06.05-11.58.30:457][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.06.05-11.58.30:457][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.06.05-11.58.30:457][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.06.05-11.58.30:457][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.06.05-11.58.30:458][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.06.05-11.58.30:459][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.06.05-11.58.30:459][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.06.05-11.58.30:459][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.06.05-11.58.30:460][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.06.05-11.58.30:460][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.06.05-11.58.30:460][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.06.05-11.58.30:461][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.06.05-11.58.30:461][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.06.05-11.58.30:461][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.06.05-11.58.30:513][  0]LogTimingProfiler: Initialize
[2025.06.05-11.58.30:513][  0]LogTimingProfiler: OnSessionChanged
[2025.06.05-11.58.30:513][  0]LoadingProfiler: Initialize
[2025.06.05-11.58.30:513][  0]LoadingProfiler: OnSessionChanged
[2025.06.05-11.58.30:513][  0]LogNetworkingProfiler: Initialize
[2025.06.05-11.58.30:513][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.05-11.58.30:513][  0]LogMemoryProfiler: Initialize
[2025.06.05-11.58.30:513][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.05-11.58.30:631][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.05-11.58.30:639][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.44ms
[2025.06.05-11.58.31:010][  0]LogCollectionManager: Loaded 1 collections in 0.000756 seconds
[2025.06.05-11.58.31:012][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Saved/Collections/' took 0.00s
[2025.06.05-11.58.31:015][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Developers/Shashank/Collections/' took 0.00s
[2025.06.05-11.58.31:016][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Collections/' took 0.00s
[2025.06.05-11.58.31:097][  0]LogConfig: Branch 'Plugins' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.05-11.58.31:099][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-11.58.31:099][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-11.58.31:101][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-11.58.31:101][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-11.58.31:101][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-11.58.31:101][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-11.58.31:129][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-11.58.31:129][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-11.58.31:167][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-11.58.31:167][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-11.58.31:168][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-11.58.31:168][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-11.58.31:168][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-11.58.31:168][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-11.58.31:196][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-11.58.31:196][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-11.58.31:213][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-06-05T11:58:31.213Z using C
[2025.06.05-11.58.31:214][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=Baoli, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.05-11.58.31:214][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.05-11.58.31:214][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.05-11.58.31:219][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.05-11.58.31:219][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.05-11.58.31:219][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.05-11.58.31:219][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000051
[2025.06.05-11.58.31:219][  0]LogFab: Display: Logging in using persist
[2025.06.05-11.58.31:220][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.06.05-11.58.31:254][  0]LogUObjectArray: 47651 objects as part of root set at end of initial load.
[2025.06.05-11.58.31:254][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.05-11.58.31:268][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 40254 public script object entries (1077.02 KB)
[2025.06.05-11.58.31:268][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.06.05-11.58.31:382][  0]LogEngine: Initializing Engine...
[2025.06.05-11.58.31:384][  0]LogGameFeatures: Initializing game features subsystem
[2025.06.05-11.58.31:384][  0]InkPlusPlus: FStory::FStory 0000062B6E336C10
[2025.06.05-11.58.31:384][  0]InkPlusPlus: Warning: WARNING: Version of ink used to build story doesn't match current version of engine. Non-critical, but recommend synchronising.
[2025.06.05-11.58.31:386][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.05-11.58.31:386][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.05-11.58.31:495][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.05-11.58.31:509][  0]LogGameFeatures: Scanning for built-in game feature plugins
[2025.06.05-11.58.31:509][  0]LogGameFeatures: Loading 233 builtins
[2025.06.05-11.58.31:510][  0]LogGameFeatures: Display: Total built in plugin load time 0.0011s
[2025.06.05-11.58.31:510][  0]LogStats: BuiltInGameFeaturePlugins loaded. -  0.001 s
[2025.06.05-11.58.31:510][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.05-11.58.31:526][  0]LogNetVersion: Set ProjectVersion to Alpha. Version Checksum will be recalculated on next use.
[2025.06.05-11.58.31:526][  0]LogInit: Texture streaming: Enabled
[2025.06.05-11.58.31:536][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.06.05-11.58.31:542][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.05-11.58.31:549][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.05-11.58.31:549][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.05-11.58.31:550][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.05-11.58.31:550][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.05-11.58.31:550][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-11.58.31:550][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-11.58.31:550][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-11.58.31:550][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-11.58.31:550][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-11.58.31:550][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-11.58.31:550][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-11.58.31:550][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-11.58.31:550][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-11.58.31:550][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-11.58.31:550][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-11.58.31:554][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-11.58.31:616][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-11.58.31:617][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-11.58.31:618][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-11.58.31:618][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-11.58.31:619][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.05-11.58.31:619][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.05-11.58.31:622][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.05-11.58.31:622][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.05-11.58.31:622][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.05-11.58.31:622][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.05-11.58.31:622][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.05-11.58.31:628][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.05-11.58.31:632][  0]LogInit: Undo buffer set to 256 MB
[2025.06.05-11.58.31:632][  0]LogInit: Transaction tracking system initialized
[2025.06.05-11.58.31:690][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.49ms
[2025.06.05-11.58.31:691][  0]LocalizationService: Localization service is disabled
[2025.06.05-11.58.31:906][  0]LogPython: Using Python 3.11.8
[2025.06.05-11.58.32:147][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/' took 0.29s
[2025.06.05-11.58.32:980][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.06.05-11.58.32:990][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (0 permutations).
[2025.06.05-11.58.33:063][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.05-11.58.33:063][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.05-11.58.33:101][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.05-11.58.33:119][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.05-11.58.33:119][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.05-11.58.33:120][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.05-11.58.33:120][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.05-11.58.33:120][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.05-11.58.33:120][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.05-11.58.33:148][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.05-11.58.33:148][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.05-11.58.33:153][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.05-11.58.33:153][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.05-11.58.33:173][  0]LogEditorDataStorage: Initializing
[2025.06.05-11.58.33:175][  0]LogEditorDataStorage: Initialized
[2025.06.05-11.58.33:180][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/MHI.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performance/MHP_Baoli.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performances/MHP_Scene1_01.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:193][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:194][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.05-11.58.33:196][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.05-11.58.33:200][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.05-11.58.33:217][  0]LogUnrealEdMisc: Loading editor; pre map load, took 9.296
[2025.06.05-11.58.33:218][  0]Cmd: MAP LOAD FILE="H:/P4/dev/Baoli/Content/Levels/DefaultLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.05-11.58.33:220][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.05-11.58.33:220][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-11.58.33:232][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-11.58.33:233][  0]InkPlusPlus: FStory::~FStory 0000062B6E336C10
[2025.06.05-11.58.33:233][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.51ms
[2025.06.05-11.58.33:307][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.05-11.58.33:307][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Glossy (0x2CCA389A36D3E860)
[2025.06.05-11.58.33:307][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/material (0x38FB08B605AA9364)
[2025.06.05-11.58.33:307][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.05-11.58.33:307][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Dark (0xC267FEC07D768F2)
[2025.06.05-11.58.33:349][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.05-11.58.33:350][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVback (0x8B181E584DB8A471) /Game/Assets/TV/TVback (0x8B181E584DB8A471) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.05-11.58.33:569][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS (0xF21345B7066A3DF7)
[2025.06.05-11.58.34:070][  0]LogLinker: Warning: [AssetLog] H:\P4\dev\Baoli\Content\BaoliAssets\BrickInstances\Brick_low_001.uasset: VerifyImport: Failed to find script package for import object 'Package /Script/rdInst'
[2025.06.05-11.58.34:382][  0]LogAssetRegistry: Display: Asset registry cache written as 106.3 MiB to H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin
[2025.06.05-11.58.35:924][  0]LogEditorDomain: Display: Class /Script/rdInst.rdInstAssetUserData is imported by a package but does not exist in memory. EditorDomain keys for packages using it will be invalid if it still exists.
	To clear this message, resave packages that use the deleted class, or load its module earlier than the packages that use it are referenced.
[2025.06.05-11.58.37:155][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: WaitingForIo) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.37:160][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 334 to allow recursive sync load to finish
[2025.06.05-11.58.37:160][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.05-11.58.37:160][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: ExportsDone) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.37:160][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 335 to allow recursive sync load to finish
[2025.06.05-11.58.37:160][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.05-11.58.37:783][  0]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\CBP_SandboxCharacter.uasset: [Compiler] Input pin  Debug Session Unique Identifier  specifying non-default value no longer exists on node  Motion Match . Please refresh node or reset pin to default value to remove pin.
[2025.06.05-11.58.38:946][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: WaitingForIo) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.38:946][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 336 to allow recursive sync load to finish
[2025.06.05-11.58.38:946][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.05-11.58.38:946][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: ExportsDone) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.05-11.58.38:946][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 337 to allow recursive sync load to finish
[2025.06.05-11.58.38:946][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.05-11.58.39:055][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:055][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Handplant' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Handplant.MSS_FoleySound_Handplant' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:057][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Jump' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Jump.MSS_FoleySound_Jump' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:058][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Land' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Land.MSS_FoleySound_Land' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:060][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Run' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Run.MSS_FoleySound_Run' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:060][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunBackwards.MSS_FoleySound_RunBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:062][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunStrafe' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunStrafe.MSS_FoleySound_RunStrafe' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:063][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Scuff' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Scuff.MSS_FoleySound_Scuff' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:064][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffPivot' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffPivot.MSS_FoleySound_ScuffPivot' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:065][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffWall' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffWall.MSS_FoleySound_ScuffWall' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:066][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Tumble' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Tumble.MSS_FoleySound_Tumble' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:067][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Walk' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Walk.MSS_FoleySound_Walk' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.39:068][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_WalkBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_WalkBackwards.MSS_FoleySound_WalkBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.05-11.58.40:794][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.06.05-11.58.40:830][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.06.05-11.58.40:844][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_High...
[2025.06.05-11.58.40:844][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_High...
[2025.06.05-11.58.41:256][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.41:260][  0]LogSkeletalMesh: Built Skeletal Mesh [0.42s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High
[2025.06.05-11.58.41:269][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.06.05-11.58.41:895][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants_High...
[2025.06.05-11.58.42:263][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.42:266][  0]LogSkeletalMesh: Built Skeletal Mesh [0.37s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High
[2025.06.05-11.58.42:332][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.42:336][  0]LogSkeletalMesh: Built Skeletal Mesh [1.49s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High
[2025.06.05-11.58.42:693][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_High...
[2025.06.05-11.58.42:713][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_Cap_01_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-11.58.42:714][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelNut_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-11.58.42:715][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelLeaf_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.05-11.58.43:074][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.43:077][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High
[2025.06.05-11.58.44:557][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_hoodie_nrm_High...
[2025.06.05-11.58.45:011][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.45:015][  0]LogSkeletalMesh: Built Skeletal Mesh [0.46s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High
[2025.06.05-11.58.45:079][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Foley.SM_Foley.
[2025.06.05-11.58.45:079][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.05-11.58.45:079][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Ambient.SM_Ambient.
[2025.06.05-11.58.45:079][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.05-11.58.45:079][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Reverb.SM_Reverb.
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Jumps
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Stops
[2025.06.05-11.58.46:851][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.05-11.58.46:852][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.05-11.58.46:852][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.05-11.58.46:852][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.05-11.58.46:852][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.05-11.58.46:852][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.05-11.58.46:852][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.05-11.58.46:853][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.05-11.58.46:853][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.05-11.58.46:853][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.05-11.58.46:853][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.05-11.58.46:853][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.05-11.58.46:853][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.05-11.58.47:002][  0]LogPoseSearch: 25abbe71a7361c623bd3755ff5e1fbe7a56e7f94 - PSD_Dense_Jumps BeginCache
[2025.06.05-11.58.47:002][  0]LogPoseSearch: 25abbe71a7361c623bd3755ff5e1fbe7a56e7f94 - PSD_Dense_Jumps BuildIndex From Cache
[2025.06.05-11.58.47:014][  0]LogPoseSearch: 52bcfc861296fe46e902c3e1977b2a893663552c - PSD_Dense_Jumps_Far BeginCache
[2025.06.05-11.58.47:015][  0]LogPoseSearch: 52bcfc861296fe46e902c3e1977b2a893663552c - PSD_Dense_Jumps_Far BuildIndex From Cache
[2025.06.05-11.58.47:025][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.05-11.58.47:025][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.05-11.58.47:025][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.05-11.58.47:025][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.05-11.58.47:025][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.05-11.58.47:025][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.05-11.58.47:025][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.05-11.58.47:025][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Stops
[2025.06.05-11.58.47:025][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.05-11.58.47:025][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.05-11.58.47:025][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.05-11.58.47:026][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.05-11.58.47:026][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.05-11.58.47:026][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.05-11.58.47:026][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.05-11.58.47:080][  0]LogPoseSearch: c903d208ce6ce6d6d57c16e8029907a718a6c7e9 - PSD_Dense_Stand_Idle_Lands_Heavy BeginCache
[2025.06.05-11.58.47:081][  0]LogPoseSearch: c903d208ce6ce6d6d57c16e8029907a718a6c7e9 - PSD_Dense_Stand_Idle_Lands_Heavy BuildIndex From Cache
[2025.06.05-11.58.47:102][  0]LogPoseSearch: 2c155ec5c459344cf515e7737ccfee7b0c910810 - PSD_Dense_Stand_Run_Lands_Heavy BeginCache
[2025.06.05-11.58.47:102][  0]LogPoseSearch: 2c155ec5c459344cf515e7737ccfee7b0c910810 - PSD_Dense_Stand_Run_Lands_Heavy BuildIndex From Cache
[2025.06.05-11.58.47:132][  0]LogPoseSearch: 0647c6013483ddb2053168a7d66d3f931f221b6f - PSD_Dense_Stand_Walk_Lands_Heavy BeginCache
[2025.06.05-11.58.47:133][  0]LogPoseSearch: 0647c6013483ddb2053168a7d66d3f931f221b6f - PSD_Dense_Stand_Walk_Lands_Heavy BuildIndex From Cache
[2025.06.05-11.58.47:242][  0]LogPoseSearch: b9ad6da2b4015614aa861a663ab0dc2c7376fcdd - PSD_Traversal BeginCache
[2025.06.05-11.58.47:243][  0]LogPoseSearch: b9ad6da2b4015614aa861a663ab0dc2c7376fcdd - PSD_Traversal BuildIndex From Cache
[2025.06.05-11.58.47:252][  0]LogPoseSearch: 1f816991d0c81fa165c9258a64c0e110cb545466 - PSD_Dense_Jumps_FromTraversal BeginCache
[2025.06.05-11.58.47:252][  0]LogPoseSearch: 1f816991d0c81fa165c9258a64c0e110cb545466 - PSD_Dense_Jumps_FromTraversal BuildIndex From Cache
[2025.06.05-11.58.47:260][  0]LogPoseSearch: 44dfcd0439dfacb33784494a07f81e5c4107264f - PSD_Dense_Stand_Walk_FromTraversal BeginCache
[2025.06.05-11.58.47:261][  0]LogPoseSearch: 44dfcd0439dfacb33784494a07f81e5c4107264f - PSD_Dense_Stand_Walk_FromTraversal BuildIndex From Cache
[2025.06.05-11.58.47:261][  0]LogPoseSearch: 2ef3b5bccd181d28d29d656af18a9552f7025b60 - PSD_Dense_Stand_Run_SpinTransition BeginCache
[2025.06.05-11.58.47:261][  0]LogPoseSearch: 2ef3b5bccd181d28d29d656af18a9552f7025b60 - PSD_Dense_Stand_Run_SpinTransition BuildIndex From Cache
[2025.06.05-11.58.47:263][  0]LogPoseSearch: c3b27113a33ed0195cb86def79fac28ef88b955a - PSD_Dense_Stand_Walk_SpinTransition BeginCache
[2025.06.05-11.58.47:263][  0]LogPoseSearch: c3b27113a33ed0195cb86def79fac28ef88b955a - PSD_Dense_Stand_Walk_SpinTransition BuildIndex From Cache
[2025.06.05-11.58.47:272][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_btm_shorts_nrm...
[2025.06.05-11.58.47:275][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_top_crewneckt_nrm...
[2025.06.05-11.58.47:276][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.06.05-11.58.47:276][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm...
[2025.06.05-11.58.47:276][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants...
[2025.06.05-11.58.47:280][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.06.05-11.58.47:460][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.47:462][  0]LogSkeletalMesh: Built Skeletal Mesh [0.19s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm
[2025.06.05-11.58.47:532][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_shs_flipflops...
[2025.06.05-11.58.47:570][  0]LogPoseSearch: 2fb236583b053e85334fffc7f28ad671a7ba6fcd - PSD_Dense_Crouch_Idle BeginCache
[2025.06.05-11.58.47:570][  0]LogPoseSearch: 2fb236583b053e85334fffc7f28ad671a7ba6fcd - PSD_Dense_Crouch_Idle BuildIndex From Cache
[2025.06.05-11.58.47:597][  0]LogPoseSearch: 3eaff9342445c24b7281b43f7e167cf00b79697d - PSD_Dense_Crouch_Loop BeginCache
[2025.06.05-11.58.47:598][  0]LogPoseSearch: 3eaff9342445c24b7281b43f7e167cf00b79697d - PSD_Dense_Crouch_Loop BuildIndex From Cache
[2025.06.05-11.58.47:615][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.47:616][  0]LogSkeletalMesh: Built Skeletal Mesh [0.09s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops
[2025.06.05-11.58.47:617][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-11.58.47:642][  0]LogPoseSearch: e5bb22fa3f0832f6b1de4740e665f8b8a0cc4a40 - PSD_Dense_Crouch_Pivot BeginCache
[2025.06.05-11.58.47:644][  0]LogPoseSearch: e5bb22fa3f0832f6b1de4740e665f8b8a0cc4a40 - PSD_Dense_Crouch_Pivot BuildIndex From Cache
[2025.06.05-11.58.47:650][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.47:653][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants
[2025.06.05-11.58.47:654][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_casualsneakers...
[2025.06.05-11.58.47:665][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.47:668][  0]LogSkeletalMesh: Built Skeletal Mesh [0.39s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.06.05-11.58.47:669][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_MC_FaceMesh...
[2025.06.05-11.58.47:696][  0]LogPoseSearch: 10463b9b5d9a2019be2ed12e5f4f9c3378a2c9df - PSD_Dense_Crouch_Start BeginCache
[2025.06.05-11.58.47:697][  0]LogPoseSearch: 10463b9b5d9a2019be2ed12e5f4f9c3378a2c9df - PSD_Dense_Crouch_Start BuildIndex From Cache
[2025.06.05-11.58.47:751][  0]LogPoseSearch: fb459193d12be90ae89d82deeb58b955bd5ded69 - PSD_Dense_Crouch_Stops BeginCache
[2025.06.05-11.58.47:752][  0]LogPoseSearch: fb459193d12be90ae89d82deeb58b955bd5ded69 - PSD_Dense_Crouch_Stops BuildIndex From Cache
[2025.06.05-11.58.47:869][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.47:872][  0]LogSkeletalMesh: Built Skeletal Mesh [0.26s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-11.58.47:873][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.06.05-11.58.47:941][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.47:944][  0]LogSkeletalMesh: Built Skeletal Mesh [0.29s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers
[2025.06.05-11.58.47:946][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_Child2_FaceMesh...
[2025.06.05-11.58.48:294][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.48:297][  0]LogSkeletalMesh: Built Skeletal Mesh [0.42s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.06.05-11.58.48:299][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.06.05-11.58.48:339][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.06.05-11.58.48:841][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.48:847][  0]LogSkeletalMesh: Built Skeletal Mesh [1.57s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.06.05-11.58.48:910][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.48:915][  0]LogSkeletalMesh: Built Skeletal Mesh [1.64s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm
[2025.06.05-11.58.48:916][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-11.58.48:982][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.48:986][  0]LogSkeletalMesh: Built Skeletal Mesh [1.71s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm
[2025.06.05-11.58.48:987][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.05-11.58.49:121][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.49:123][  0]LogSkeletalMesh: Built Skeletal Mesh [0.21s] /Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-11.58.49:199][  0]LogSkeletalMesh: Building Skeletal Mesh Kellan_FaceMesh...
[2025.06.05-11.58.49:240][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.49:246][  0]LogSkeletalMesh: Built Skeletal Mesh [0.26s] /Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.05-11.58.49:391][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'DefaultLevel'.
[2025.06.05-11.58.49:391][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-11.58.49:469][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.05-11.58.49:724][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.49:738][  0]LogSkeletalMesh: Built Skeletal Mesh [0.54s] /Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh
[2025.06.05-11.58.59:557][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.59:572][  0]LogSkeletalMesh: Built Skeletal Mesh [11.91s] /Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh
[2025.06.05-11.58.59:766][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.59:783][  0]LogSkeletalMesh: Built Skeletal Mesh [11.84s] /Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh
[2025.06.05-11.58.59:942][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.05-11.58.59:955][  0]LogSkeletalMesh: Built Skeletal Mesh [11.66s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.06.05-11.59.00:042][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.05-11.59.00:271][  0]LogUObjectHash: Compacting FUObjectHashTables data took   2.50ms
[2025.06.05-11.59.00:280][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.05-11.59.00:280][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.097ms to complete.
[2025.06.05-11.59.00:290][  0]LogUnrealEdMisc: Total Editor Startup Time, took 36.368
[2025.06.05-11.59.00:474][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.06.05-11.59.00:575][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-11.59.00:634][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-11.59.00:694][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-11.59.00:754][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-11.59.00:802][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-11.59.00:802][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.05-11.59.00:802][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-11.59.00:802][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.05-11.59.00:802][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-11.59.00:803][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.05-11.59.00:803][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-11.59.00:804][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.05-11.59.00:804][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-11.59.00:804][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.05-11.59.00:804][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-11.59.00:804][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.05-11.59.00:805][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-11.59.00:805][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.05-11.59.00:805][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-11.59.00:805][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.05-11.59.00:805][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-11.59.00:805][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.05-11.59.00:805][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.05-11.59.00:805][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.05-11.59.00:981][  0]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-11.59.00:986][  0]LogStall: Startup...
[2025.06.05-11.59.00:989][  0]LogStall: Startup complete.
[2025.06.05-11.59.01:019][  0]LogLoad: (Engine Initialization) Total time: 37.10 seconds
[2025.06.05-11.59.01:365][  0]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-11.59.01:365][  0]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-11.59.01:365][  0]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:24:33
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-11.59.01:366][  0]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-11.59.01:366][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.05-11.59.01:366][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.05-11.59.01:392][  0]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Config/DefaultEngine.ini'
[2025.06.05-11.59.01:642][  0]LogSlate: Took 0.000273 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.05-11.59.01:646][  0]LogSlate: Took 0.000211 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.05-11.59.01:648][  0]LogSlate: Took 0.000168 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.05-11.59.01:649][  0]LogSlate: Took 0.000134 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.05-11.59.01:720][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.06.05-11.59.01:720][  0]LogStreaming: Display: FlushAsyncLoading(342): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-11.59.01:725][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.06.05-11.59.01:725][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.06.05-11.59.01:725][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.05-11.59.01:810][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.05-11.59.01:810][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.06.05-11.59.01:811][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.06.05-11.59.01:811][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.06.05-11.59.01:811][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.05-11.59.01:877][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.05-11.59.01:877][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.06.05-11.59.01:923][  0]LogSlate: Took 0.000458 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.05-11.59.02:723][  0]LogD3D12RHI: Creating RTPSO with 24 shaders (0 cached, 24 new) took 46.48 ms. Compile time 42.39 ms, link time 4.03 ms.
[2025.06.05-11.59.02:732][  0]LogD3D12RHI: Creating RTPSO with 197 shaders (0 cached, 197 new) took 217.02 ms. Compile time 205.16 ms, link time 11.46 ms.
[2025.06.05-11.59.02:790][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-11.59.02:801][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.06.05-11.59.02:801][  0]LogFab: Display: Logging in using exchange code
[2025.06.05-11.59.02:801][  0]LogFab: Display: Reading exchange code from commandline
[2025.06.05-11.59.02:801][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.06.05-11.59.02:802][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... started...
[2025.06.05-11.59.02:802][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... took 352 us
[2025.06.05-11.59.02:802][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.05-11.59.02:853][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.05-11.59.02:869][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 66.009 ms (total: 66.361 ms)
[2025.06.05-11.59.02:869][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-11.59.02:869][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-11.59.02:869][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-11.59.02:869][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_Glossy was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Glossy has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-11.59.02:869][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/material which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-11.59.02:869][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/material was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/material has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/material.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-11.59.02:869][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-11.59.02:870][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-11.59.02:870][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-11.59.02:870][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_Dark was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Dark has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-11.59.02:870][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-11.59.02:870][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVfront, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-11.59.02:870][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-11.59.02:870][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVback, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-11.59.02:870][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.05-11.59.02:870][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Washingmachine/steel, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.05-11.59.02:870][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BaoliEditor Win64 Development
[2025.06.05-11.59.03:115][  1]LogAssetRegistry: AssetRegistryGather time 0.2017s: AssetDataDiscovery 0.0258s, AssetDataGather 0.0325s, StoreResults 0.1434s. Wall time 35.0080s.
	NumCachedDirectories 0. NumUncachedDirectories 3058. NumCachedFiles 16167. NumUncachedFiles 6.
	BackgroundTickInterruptions 20.
[2025.06.05-11.59.03:202][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.06.05-11.59.03:215][  1]LogCollectionManager: Fixed up redirectors for 1 collections in 0.000042 seconds (updated 1 objects)
[2025.06.05-11.59.03:402][  1]LogMaterial: Display: Material /InterchangeAssets/gltf/M_Default.M_Default needed to have new flag set bUsedWithNanite !
[2025.06.05-11.59.03:434][  1]MapCheck: Warning: M_Default Material /InterchangeAssets/gltf/M_Default.M_Default was missing the usage flag bUsedWithNanite. If the material asset is not re-saved, it may not render correctly when run outside the editor. Fix
[2025.06.05-11.59.03:625][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.06.05-11.59.03:792][  2]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-11.59.03:912][  2]LogSourceControl: Uncontrolled asset enumeration finished in 0.71023 seconds (Found 7982 uncontrolled assets)
[2025.06.05-11.59.04:098][  3]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 32.742805
[2025.06.05-11.59.04:100][  3]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.05-11.59.04:101][  3]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 32.878433
[2025.06.05-11.59.04:563][ 12]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.05-11.59.04:686][ 17]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.05-11.59.04:697][ 17]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.04:698][ 17]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.04:698][ 17]LogPoseSearch: UPoseSearchLibrary::UpdateMotionMatchingState invalid search result : ForceInterrupt [true], CanAdvance [false], Indexing [true], Databases [PSD_Dense_Stand_Idles] 
[2025.06.05-11.59.04:732][ 17]LogPoseSearch: 50dc2d6fc66385f69c3f4316520b5453bbe11082 - PSD_Sparse_Stand_Walk_Stops BeginCache
[2025.06.05-11.59.04:732][ 17]LogPoseSearch: 50dc2d6fc66385f69c3f4316520b5453bbe11082 - PSD_Sparse_Stand_Walk_Stops BuildIndex From Cache
[2025.06.05-11.59.04:732][ 17]LogPoseSearch: 7bd24e04e6106f1973227ab2f5dd916a0214b232 - PSD_Sparse_Stand_Walk_Starts BeginCache
[2025.06.05-11.59.04:734][ 17]LogPoseSearch: 7bd24e04e6106f1973227ab2f5dd916a0214b232 - PSD_Sparse_Stand_Walk_Starts BuildIndex From Cache
[2025.06.05-11.59.04:734][ 17]LogPoseSearch: 6c818e5f72e0650bcaac1c26c2a2af0097681f03 - PSD_Sparse_Stand_Walk_Pivots BeginCache
[2025.06.05-11.59.04:735][ 17]LogPoseSearch: 6c818e5f72e0650bcaac1c26c2a2af0097681f03 - PSD_Sparse_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.05-11.59.04:735][ 17]LogPoseSearch: a53f5d87ef31e2efb2cffa95e177eb2de30ea68b - PSD_Sparse_Stand_Walk_Loops BeginCache
[2025.06.05-11.59.04:735][ 17]LogPoseSearch: a53f5d87ef31e2efb2cffa95e177eb2de30ea68b - PSD_Sparse_Stand_Walk_Loops BuildIndex From Cache
[2025.06.05-11.59.04:736][ 17]LogPoseSearch: 64befbf5d961ed5ac476d534110da8895e69df96 - PSD_Sparse_Stand_Run_Stops BeginCache
[2025.06.05-11.59.04:736][ 17]LogPoseSearch: 64befbf5d961ed5ac476d534110da8895e69df96 - PSD_Sparse_Stand_Run_Stops BuildIndex From Cache
[2025.06.05-11.59.04:736][ 17]LogPoseSearch: 2a285cc36d3969680553c96c4cc33908d8d326c8 - PSD_Sparse_Stand_Run_Starts BeginCache
[2025.06.05-11.59.04:737][ 17]LogPoseSearch: 2a285cc36d3969680553c96c4cc33908d8d326c8 - PSD_Sparse_Stand_Run_Starts BuildIndex From Cache
[2025.06.05-11.59.04:738][ 17]LogPoseSearch: df8620db2cd0e21552cf91b1d3cd5aa199f8e4ba - PSD_Sparse_Stand_Run_Pivots BeginCache
[2025.06.05-11.59.04:739][ 17]LogPoseSearch: df8620db2cd0e21552cf91b1d3cd5aa199f8e4ba - PSD_Sparse_Stand_Run_Pivots BuildIndex From Cache
[2025.06.05-11.59.04:739][ 17]LogPoseSearch: 20bcf12fcf57878594cfd98261e8196a850d3275 - PSD_Sparse_Stand_Run_Loops BeginCache
[2025.06.05-11.59.04:739][ 17]LogPoseSearch: 20bcf12fcf57878594cfd98261e8196a850d3275 - PSD_Sparse_Stand_Run_Loops BuildIndex From Cache
[2025.06.05-11.59.04:740][ 17]LogPoseSearch: 093bf032f877970de263de3430d86e0291f0b843 - PSD_Dense_Stand_Walk_Stops BeginCache
[2025.06.05-11.59.04:740][ 17]LogPoseSearch: 093bf032f877970de263de3430d86e0291f0b843 - PSD_Dense_Stand_Walk_Stops BuildIndex From Cache
[2025.06.05-11.59.04:741][ 17]LogPoseSearch: 58635c6253bdbf281b54ac2883681f632df4ac5a - PSD_Dense_Stand_TurnInPlace BeginCache
[2025.06.05-11.59.04:742][ 17]LogPoseSearch: 58635c6253bdbf281b54ac2883681f632df4ac5a - PSD_Dense_Stand_TurnInPlace BuildIndex From Cache
[2025.06.05-11.59.04:742][ 17]LogPoseSearch: aa8f1551ead5ebb7649a9c0cf3f329785019c495 - PSD_Dense_Stand_Run_FromTraversal BeginCache
[2025.06.05-11.59.04:743][ 17]LogPoseSearch: aa8f1551ead5ebb7649a9c0cf3f329785019c495 - PSD_Dense_Stand_Run_FromTraversal BuildIndex From Cache
[2025.06.05-11.59.04:743][ 17]LogPoseSearch: 3c18bdf4eafddb521f27fde8b603d69f34423c6e - PSD_Dense_Stand_Walk_Starts BeginCache
[2025.06.05-11.59.04:744][ 17]LogPoseSearch: 3c18bdf4eafddb521f27fde8b603d69f34423c6e - PSD_Dense_Stand_Walk_Starts BuildIndex From Cache
[2025.06.05-11.59.04:744][ 17]LogPoseSearch: 75620188eb8a82eb61257a3e37062d404180d1eb - PSD_Dense_Stand_Walk_Pivots BeginCache
[2025.06.05-11.59.04:745][ 17]LogPoseSearch: 75620188eb8a82eb61257a3e37062d404180d1eb - PSD_Dense_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.05-11.59.04:745][ 17]LogPoseSearch: ba3a46564c5498cbb68507c1ccbbd585a3fc84d4 - PSD_Dense_Stand_Walk_Loops BeginCache
[2025.06.05-11.59.04:746][ 17]LogPoseSearch: ba3a46564c5498cbb68507c1ccbbd585a3fc84d4 - PSD_Dense_Stand_Walk_Loops BuildIndex From Cache
[2025.06.05-11.59.04:746][ 17]LogPoseSearch: 54f0f90cd23005d37fa1e725d1b27c86bd11c9b3 - PSD_Dense_Stand_Walk_Lands_Light BeginCache
[2025.06.05-11.59.04:747][ 17]LogPoseSearch: 54f0f90cd23005d37fa1e725d1b27c86bd11c9b3 - PSD_Dense_Stand_Walk_Lands_Light BuildIndex From Cache
[2025.06.05-11.59.04:747][ 17]LogPoseSearch: d9aafe8d3e39205b5f2061763c086eddbfb05fac - PSD_Dense_Stand_Run_Stops BeginCache
[2025.06.05-11.59.04:748][ 17]LogPoseSearch: d9aafe8d3e39205b5f2061763c086eddbfb05fac - PSD_Dense_Stand_Run_Stops BuildIndex From Cache
[2025.06.05-11.59.04:748][ 17]LogPoseSearch: d8d5fe007588aee9a3da79e9f1a75f582c1b9f99 - PSD_Dense_Stand_Run_Starts BeginCache
[2025.06.05-11.59.04:748][ 17]LogPoseSearch: d8d5fe007588aee9a3da79e9f1a75f582c1b9f99 - PSD_Dense_Stand_Run_Starts BuildIndex From Cache
[2025.06.05-11.59.04:749][ 17]LogPoseSearch: b53a49cb986c56c8a7f2626bb2d2b7429fbf1afb - PSD_Dense_Stand_Run_Pivots BeginCache
[2025.06.05-11.59.04:750][ 17]LogPoseSearch: 570ea0e9de87396e5797a333746ddd45125a84d2 - PSD_Dense_Stand_Run_Loops BeginCache
[2025.06.05-11.59.04:751][ 17]LogPoseSearch: 360967ed92817bbce2fc65a7f0c26e89ee4b6373 - PSD_Dense_Stand_Run_Lands_Light BeginCache
[2025.06.05-11.59.04:751][ 17]LogPoseSearch: 570ea0e9de87396e5797a333746ddd45125a84d2 - PSD_Dense_Stand_Run_Loops BuildIndex From Cache
[2025.06.05-11.59.04:751][ 17]LogPoseSearch: 360967ed92817bbce2fc65a7f0c26e89ee4b6373 - PSD_Dense_Stand_Run_Lands_Light BuildIndex From Cache
[2025.06.05-11.59.04:753][ 17]LogPoseSearch: 3faefe98948c1fcc929efd51f12856b3b9f3b947 - PSD_Dense_Stand_Idles BeginCache
[2025.06.05-11.59.04:753][ 17]LogPoseSearch: b53a49cb986c56c8a7f2626bb2d2b7429fbf1afb - PSD_Dense_Stand_Run_Pivots BuildIndex From Cache
[2025.06.05-11.59.04:753][ 17]LogPoseSearch: 3faefe98948c1fcc929efd51f12856b3b9f3b947 - PSD_Dense_Stand_Idles BuildIndex From Cache
[2025.06.05-11.59.04:754][ 17]LogPoseSearch: 914c12febe09d1d5c60f3a968f6f99ac6ee0695d - PSD_Dense_Stand_Idle_Lands_Light BeginCache
[2025.06.05-11.59.04:755][ 17]LogPoseSearch: 914c12febe09d1d5c60f3a968f6f99ac6ee0695d - PSD_Dense_Stand_Idle_Lands_Light BuildIndex From Cache
[2025.06.05-11.59.04:788][ 17]LogD3D12RHI: Creating RTPSO with 21 shaders (13 cached, 8 new) took 12.26 ms. Compile time 10.23 ms, link time 2.00 ms.
[2025.06.05-11.59.04:798][ 17]LogD3D12RHI: Creating RTPSO with 232 shaders (228 cached, 4 new) took 21.91 ms. Compile time 10.52 ms, link time 11.26 ms.
[2025.06.05-11.59.05:051][ 19]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 33.812851
[2025.06.05-11.59.05:055][ 19]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 348165457
[2025.06.05-11.59.05:055][ 19]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 33.812851, Update Interval: 322.205872
[2025.06.05-11.59.05:081][ 20]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.05:082][ 20]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.06:102][ 88]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.05-11.59.06:112][ 88]LogActor: Warning: BP_Almirah_C /Engine/Transient.World_1:PersistentLevel.BP_Almirah_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.05-11.59.06:286][ 89]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.05-11.59.06:514][ 92]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.06:516][ 92]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.07:148][ 93]LogD3D12RHI: Creating RTPSO with 234 shaders (0 cached, 1 new) took 578.37 ms. Compile time 1.32 ms, link time 576.99 ms.
[2025.06.05-11.59.07:189][ 94]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.05-11.59.07:197][ 94]LogActor: Warning: BP_Bed_C /Engine/Transient.World_3:PersistentLevel.BP_Bed_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.05-11.59.07:231][ 95]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.05-11.59.07:281][ 96]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.05-11.59.07:409][ 99]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.07:409][ 99]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.07:550][100]LogD3D12RHI: Creating RTPSO with 235 shaders (0 cached, 1 new) took 83.75 ms. Compile time 1.68 ms, link time 82.02 ms.
[2025.06.05-11.59.07:743][105]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.06.05-11.59.07:752][105]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.CBP_SandboxCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.07:753][105]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.CBP_SandboxCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.07:828][107]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.07:829][107]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.35:371][269]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-11.59.35:695][281]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-11.59.35:697][281]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.00.27:020][477]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.05-12.01.02:883][ 11]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.01.02:883][ 11]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.01.04:129][ 51]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_7
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.01.04:129][ 51]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_7
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.01.04:252][ 52]LogD3D12RHI: Creating RTPSO with 236 shaders (1 cached, 0 new) took 70.05 ms. Compile time 0.02 ms, link time 69.98 ms.
[2025.06.05-12.01.04:610][ 63]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_8
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.01.04:611][ 63]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_8
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.01.04:738][ 64]LogD3D12RHI: Creating RTPSO with 238 shaders (2 cached, 0 new) took 76.73 ms. Compile time 0.02 ms, link time 76.66 ms.
[2025.06.05-12.01.09:343][233]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.06.05-12.01.09:408][234]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.06.05-12.01.10:596][326]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-12.01.10:601][326]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.06.05-12.01.10:601][326]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.06.05-12.01.10:601][326]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.01.10:669][326]LogPlayLevel: PIE: StaticDuplicateObject took: (0.067624s)
[2025.06.05-12.01.10:669][326]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.067669s)
[2025.06.05-12.01.10:760][326]LogUObjectHash: Compacting FUObjectHashTables data took   1.60ms
[2025.06.05-12.01.10:763][326]r.RayTracing.Culling = "0"
[2025.06.05-12.01.10:763][326]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-12.01.10:764][326]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-12.01.10:766][326]LogPlayLevel: PIE: World Init took: (0.001730s)
[2025.06.05-12.01.10:766][326]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.06.05-12.01.10:766][326]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-12.01.10:766][326]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-12.01.10:766][326]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-12.01.10:766][326]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-12.01.10:766][326]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-12.01.10:766][326]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-12.01.10:766][326]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-12.01.10:766][326]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-12.01.10:766][326]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-12.01.10:766][326]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-12.01.10:766][326]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-12.01.10:769][326]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-12.01.10:810][326]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-12.01.10:810][326]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-12.01.10:810][326]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-12.01.10:810][326]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-12.01.10:810][326]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.06.05-12.01.10:811][326]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.06.05-12.01.10:816][326]LogInit: FAudioDevice initialized with ID 2.
[2025.06.05-12.01.10:816][326]LogAudio: Display: Audio Device (ID: 2) registered with world 'DefaultLevel'.
[2025.06.05-12.01.10:816][326]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.06.05-12.01.10:816][326]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.06.05-12.01.10:816][326]LogWindows: WindowsPlatformFeatures enabled
[2025.06.05-12.01.10:816][326]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.06.05-12.01.10:816][326]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-12.01.10:820][326]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-12.01.10:864][326]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-12.01.10:872][326]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-17.31.10
[2025.06.05-12.01.10:880][326]LogWorld: Bringing up level for play took: 0.059247
[2025.06.05-12.01.10:883][326]LogOnline: OSS: Created online subsystem instance for: :Context_10
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-12.01.10:910][326]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-12.01.10:923][326]PIE: Server logged in
[2025.06.05-12.01.10:924][326]PIE: Play in editor total start time 0.329 seconds.
[2025.06.05-12.01.11:724][327]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-12.01.11:783][327]LogD3D12RHI: Creating RTPSO with 24 shaders (20 cached, 4 new) took 16.83 ms. Compile time 9.85 ms, link time 6.75 ms.
[2025.06.05-12.01.11:859][327]LogD3D12RHI: Creating RTPSO with 235 shaders (0 cached, 1 new) took 92.83 ms. Compile time 3.85 ms, link time 88.83 ms.
[2025.06.05-12.01.11:974][328]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_9
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.01.11:974][328]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_9
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.01.12:127][329]LogD3D12RHI: Creating RTPSO with 240 shaders (2 cached, 0 new) took 88.15 ms. Compile time 0.03 ms, link time 88.06 ms.
[2025.06.05-12.01.12:351][330]LogD3D12RHI: Creating RTPSO with 254 shaders (14 cached, 0 new) took 108.50 ms. Compile time 0.05 ms, link time 108.39 ms.
[2025.06.05-12.01.12:475][331]LogD3D12RHI: Creating RTPSO with 255 shaders (15 cached, 0 new) took 87.17 ms. Compile time 0.04 ms, link time 87.05 ms.
[2025.06.05-12.01.13:303][357]PIE: Error: No database assets provided for motion matching.
[2025.06.05-12.01.13:986][381]LogTemp: Warning: Focused Object = BP_ChestActorPawn_C_5
[2025.06.05-12.01.14:048][383]LogTemp: Warning: Focused Object = BP_ChestActorPawn_C_5
[2025.06.05-12.01.14:105][385]LogTemp: Warning: Focused Object = BP_ChestActorPawn_C_5
[2025.06.05-12.01.14:133][386]LogTemp: Warning: Focused Object = BP_ChestActorPawn_C_5
[2025.06.05-12.01.14:192][388]LogTemp: Warning: Focused Object = BP_ChestActorPawn_C_5
[2025.06.05-12.01.14:258][390]LogTemp: Warning: Focused Object = BP_ChestActorPawn_C_5
[2025.06.05-12.01.14:289][391]LogTemp: Warning: Focused Object = BP_ChestActorPawn_C_5
[2025.06.05-12.01.14:351][393]LogTemp: Warning: Focused Object = BP_ChestActorPawn_C_5
[2025.06.05-12.01.14:412][395]LogTemp: Warning: Focused Object = BP_ChestActorPawn_C_5
[2025.06.05-12.01.14:575][399]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_10
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.01.14:576][399]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_10
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.01.15:549][431]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'UE.Wave Player.Stereo (v1.0)': Interface change detected.
[2025.06.05-12.01.15:549][431]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'Array.Random Get.WaveAsset:Array': Newer version 'v1.1' found.
[2025.06.05-12.01.17:850][501]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.17:911][503]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.17:938][504]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.17:996][506]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.18:054][508]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.18:116][510]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.18:145][511]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.18:692][530]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.18:746][532]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.18:804][534]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.18:862][536]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.18:896][537]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.18:960][539]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.18:996][540]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.19:053][542]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.19:111][544]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.23:279][696]LogScript: Warning: Script Msg: TestBed_C_1 InterpAim SetTimer passed a negative or zero time. The associated timer may fail to be created/fire! If using InitialStartDelayVariance, be sure it is smaller than (Time + InitialStartDelay).
[2025.06.05-12.01.23:279][696]LogScript: Warning: Script Msg called by: BP_Bed_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.TestBed_C_1
[2025.06.05-12.01.26:640][810]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.01.26:640][810]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.01.26:644][810]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-12.01.26:644][810]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-12.01.26:644][810]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.01.26:647][810]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.01.26:664][810]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-12.01.26:672][810]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.01.26:692][810]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-12.01.26:719][810]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.06.05-12.01.26:719][810]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.05-12.01.26:721][810]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.05-12.01.26:725][810]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.01.26:732][810]LogUObjectHash: Compacting FUObjectHashTables data took   1.13ms
[2025.06.05-12.01.26:890][811]LogPlayLevel: Display: Destroying online subsystem :Context_10
[2025.06.05-12.01.28:260][863]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-12.01.33:988][ 64]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-12.01.33:989][ 64]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.01.34:047][ 64]LogPlayLevel: PIE: StaticDuplicateObject took: (0.058666s)
[2025.06.05-12.01.34:047][ 64]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.058711s)
[2025.06.05-12.01.34:066][ 64]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.06.05-12.01.34:069][ 64]r.RayTracing.Culling = "0"
[2025.06.05-12.01.34:069][ 64]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-12.01.34:069][ 64]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-12.01.34:070][ 64]LogPlayLevel: PIE: World Init took: (0.001563s)
[2025.06.05-12.01.34:072][ 64]LogAudio: Display: Creating Audio Device:                 Id: 3, Scope: Unique, Realtime: True
[2025.06.05-12.01.34:072][ 64]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-12.01.34:072][ 64]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-12.01.34:072][ 64]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-12.01.34:072][ 64]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-12.01.34:072][ 64]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-12.01.34:072][ 64]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-12.01.34:072][ 64]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-12.01.34:072][ 64]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-12.01.34:072][ 64]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-12.01.34:072][ 64]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-12.01.34:072][ 64]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-12.01.34:074][ 64]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-12.01.34:111][ 64]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-12.01.34:111][ 64]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-12.01.34:111][ 64]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-12.01.34:111][ 64]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-12.01.34:112][ 64]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=3
[2025.06.05-12.01.34:112][ 64]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=3
[2025.06.05-12.01.34:115][ 64]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=3
[2025.06.05-12.01.34:115][ 64]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=3
[2025.06.05-12.01.34:115][ 64]LogInit: FAudioDevice initialized with ID 3.
[2025.06.05-12.01.34:115][ 64]LogAudio: Display: Audio Device (ID: 3) registered with world 'DefaultLevel'.
[2025.06.05-12.01.34:115][ 64]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 3
[2025.06.05-12.01.34:115][ 64]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-12.01.34:118][ 64]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-12.01.34:146][ 64]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-12.01.34:153][ 64]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-17.31.34
[2025.06.05-12.01.34:161][ 64]LogWorld: Bringing up level for play took: 0.042731
[2025.06.05-12.01.34:163][ 64]LogOnline: OSS: Created online subsystem instance for: :Context_11
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-12.01.34:179][ 64]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-12.01.34:190][ 64]PIE: Server logged in
[2025.06.05-12.01.34:193][ 64]PIE: Play in editor total start time 0.208 seconds.
[2025.06.05-12.01.34:322][ 65]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-12.01.35:380][ 97]PIE: Error: No database assets provided for motion matching.
[2025.06.05-12.01.37:118][156]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:180][158]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:209][159]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:266][161]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:324][163]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:353][164]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:416][166]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:476][168]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:506][169]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:568][171]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:628][173]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:657][174]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:714][176]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.37:766][178]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:223][195]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:275][197]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:304][198]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:327][199]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0520
[2025.06.05-12.01.38:327][199]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0501
[2025.06.05-12.01.38:327][199]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:08E3
[2025.06.05-12.01.38:327][199]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:08C4
[2025.06.05-12.01.38:357][200]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0520
[2025.06.05-12.01.38:357][200]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0501
[2025.06.05-12.01.38:357][200]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:08E3
[2025.06.05-12.01.38:357][200]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:08C4
[2025.06.05-12.01.38:362][200]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:417][202]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:486][204]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:517][205]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:575][207]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:605][208]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:662][210]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:719][212]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.38:781][214]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.39:914][254]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.39:977][256]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:004][257]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:063][259]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:122][261]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:152][262]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:210][264]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:264][266]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0DA8
[2025.06.05-12.01.40:264][266]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0D89
[2025.06.05-12.01.40:267][266]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:294][267]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0DA8
[2025.06.05-12.01.40:294][267]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:0D89
[2025.06.05-12.01.40:329][268]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:362][269]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:430][271]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:472][272]Cmd: ke * SetLevelStyle 2
[2025.06.05-12.01.40:496][272]Called 'SetLevelStyle 2' on everything in the world and 1 instances succeeded
[2025.06.05-12.01.40:496][272]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:512][273]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:568][275]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:628][277]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.40:657][278]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.41:520][309]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.41:577][311]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.41:606][312]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.41:663][314]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:055][328]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:111][330]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:164][332]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:228][333]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:264][335]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:326][337]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:352][338]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:404][340]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:461][342]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:518][344]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:581][345]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:608][346]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:663][348]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:713][350]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:768][352]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:828][354]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:853][355]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:906][357]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.42:962][359]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:015][361]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:070][363]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:120][365]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:128D
[2025.06.05-12.01.43:120][365]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:126E
[2025.06.05-12.01.43:124][365]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:146][366]LogScript: Warning: Accessed None trying to read property CurrentSelectedDatabase
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:128D
[2025.06.05-12.01.43:146][366]LogScript: Warning: Accessed None
	ABP_SandboxCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:126E
[2025.06.05-12.01.43:151][366]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:202][368]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:254][370]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:312][372]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:369][374]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:422][376]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:472][378]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:526][380]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:554][381]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:607][383]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:663][385]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:715][387]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:772][389]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:825][391]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:850][392]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:905][394]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.43:959][396]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:015][398]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:067][400]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:118][402]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:170][404]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:220][406]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:280][408]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:306][409]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:357][411]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:412][413]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:463][415]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:517][417]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:569][419]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:621][421]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:671][423]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:723][425]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:754][426]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:808][428]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:859][430]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:911][432]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.44:964][434]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:019][436]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:070][438]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:122][440]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:175][442]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:206][443]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:259][445]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:312][447]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:366][449]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:429][451]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:456][452]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:516][454]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:567][456]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:620][458]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:655][459]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:711][461]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:767][463]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:823][465]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:851][466]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:912][468]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.45:969][470]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.46:024][472]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.01.50:195][617]LogScript: Warning: Script Msg: TestBed_C_1 InterpAim SetTimer passed a negative or zero time. The associated timer may fail to be created/fire! If using InitialStartDelayVariance, be sure it is smaller than (Time + InitialStartDelay).
[2025.06.05-12.01.50:195][617]LogScript: Warning: Script Msg called by: BP_Bed_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.TestBed_C_1
[2025.06.05-12.01.51:350][656]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.01.51:350][656]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.01.51:352][656]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-12.01.51:352][656]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-12.01.51:353][656]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.01.51:355][656]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.01.51:375][656]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-12.01.51:382][656]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.01.51:426][656]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.01.51:428][656]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 3
[2025.06.05-12.01.51:428][656]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.05-12.01.51:431][656]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.05-12.01.51:440][656]LogUObjectHash: Compacting FUObjectHashTables data took   1.30ms
[2025.06.05-12.01.51:595][657]LogPlayLevel: Display: Destroying online subsystem :Context_11
[2025.06.05-12.01.52:650][696]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-12.02.05:127][187]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Almirah.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bed.uasset'
[2025.06.05-12.02.05:406][196]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_Bulb.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_ChestActorPawn.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Fan.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_FlashLight.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameMinimal.uasset'
[2025.06.05-12.02.05:658][201]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_11
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.05:658][201]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_11
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.05:744][202]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.05:744][202]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.05:777][203]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_13
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.05:777][203]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_13
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.05:805][204]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_14
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.05:806][204]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_14
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.05:836][205]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_15
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.05:836][205]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_15
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.05:988][211]LogAssetEditorSubsystem: Opening Asset editor for AnimBlueprint /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter
[2025.06.05-12.02.05:989][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.06.05-12.02.06:023][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.06.05-12.02.06:031][211]LogStreaming: Display: FlushAsyncLoading(358): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-12.02.06:052][211]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.05-12.02.06:053][211]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.06:054][211]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.06:073][211]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-12.02.09:302][211]LogBlueprintEditor: Perf: 3.2 total seconds to load all 13 blueprint libraries in project. Avoid references to content in blueprint libraries to shorten this time.
[2025.06.05-12.02.09:302][211]LogBlueprintEditor: Perf: 3.1 seconds loading: /Game/UltraDynamicSky/Blueprints/Functions/UltraDynamicWeather_Functions
[2025.06.05-12.02.10:629][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.06.05-12.02.10:662][211]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-12.02.10:992][211]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_16
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.10:992][211]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_16
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:047][211]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.06.05-12.02.11:087][211]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:29:33
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-12.02.11:117][211]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-12.02.11:170][211]LogSlate: Took 0.000171 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.06.05-12.02.11:313][212]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:316][212]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-12.02.11:316][212]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-12.02.11:316][212]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:29:33
[2025.06.05-12.02.11:316][212]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-12.02.11:316][212]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-12.02.11:316][212]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-12.02.11:316][212]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-12.02.11:316][212]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-12.02.11:316][212]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-12.02.11:317][212]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-12.02.11:317][212]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-12.02.11:317][212]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-12.02.11:317][212]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-12.02.11:317][212]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-12.02.11:317][212]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-12.02.11:317][212]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-12.02.11:336][213]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:366][214]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_17
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:366][214]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_17
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:368][214]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:393][215]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:411][216]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_18
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:411][216]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_18
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:412][216]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:428][217]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:444][218]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:462][219]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.11:474][220]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_10:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.22:460][ 30]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-12.02.23:463][ 94]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset'
[2025.06.05-12.02.40:146][289]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Levels/_GENERATED/Anshul/038440b34b794408bde62c144133dac0_Textured_0_91B2DC1C.uasset H:/P4/dev/Baoli/Content/Levels/_GENERATED/Anshul/038440b34b794408bde62c144133dac0_Textured_10_187AF84B.uasset H:/P4/dev/Baoli/Content/Levels/_GENERATED/Anshul/038440b34b794408bde62c144133dac0_Textured_11_EA019811.uasset H:/P4/dev/Baoli/Content/Levels/_GENERATED/Anshul/038440b34b794408bde62c144133dac0_Textured_12_EA2C0BF7.uasset H:/P4/dev/Baoli/Content/Levels/_GENERATED/Anshul/038440b34b794408bde62c144133dac0_Textured_13_EB8A7786.uasset H:/P4/dev/Baoli/Content/Levels/_GENERATED/Anshul/038440b34b794408bde62c144133dac0_Textured_14_DC9C41A4.uasset H:/P4/dev/Baoli/Content/Levels/_GENERATED/Anshul/038440b34b794408bde62c144133dac0_Textured_15_534519D1.uasset H:/P4/dev/Baoli/Content/Levels/_GENERATED/Anshul/038440b34b794408bde62c144133dac0_Textured_16_30029BD9.uasset'
[2025.06.05-12.02.41:112][352]LogAssetEditorSubsystem: Opening Asset editor for ChooserTable /Game/Characters/UEFN_Mannequin/Animations/MotionMatchingData/CHT_PoseSearchDatabases_Dense.CHT_PoseSearchDatabases_Dense
[2025.06.05-12.02.41:192][352]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:29:33
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-12.02.41:369][352]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-12.02.46:995][704]LogUObjectHash: Compacting FUObjectHashTables data took   5.50ms
[2025.06.05-12.02.47:048][704]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_19
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.47:049][704]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_19
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.47:054][704]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_20
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.47:055][704]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_20
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.47:346][706]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_21
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.47:346][706]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_21
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.02.53:499][ 60]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-12.02.53:514][ 60]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-12.02.53:514][ 60]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.02.53:575][ 60]LogPlayLevel: PIE: StaticDuplicateObject took: (0.060503s)
[2025.06.05-12.02.53:575][ 60]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.060560s)
[2025.06.05-12.02.53:612][ 60]LogUObjectHash: Compacting FUObjectHashTables data took   1.81ms
[2025.06.05-12.02.53:619][ 60]r.RayTracing.Culling = "0"
[2025.06.05-12.02.53:619][ 60]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-12.02.53:619][ 60]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-12.02.53:621][ 60]LogPlayLevel: PIE: World Init took: (0.001713s)
[2025.06.05-12.02.53:622][ 60]LogAudio: Display: Creating Audio Device:                 Id: 4, Scope: Unique, Realtime: True
[2025.06.05-12.02.53:622][ 60]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-12.02.53:622][ 60]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-12.02.53:622][ 60]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-12.02.53:622][ 60]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-12.02.53:622][ 60]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-12.02.53:622][ 60]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-12.02.53:622][ 60]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-12.02.53:622][ 60]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-12.02.53:622][ 60]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-12.02.53:622][ 60]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-12.02.53:622][ 60]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-12.02.53:624][ 60]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-12.02.53:661][ 60]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-12.02.53:662][ 60]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-12.02.53:662][ 60]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-12.02.53:662][ 60]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-12.02.53:662][ 60]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=4
[2025.06.05-12.02.53:663][ 60]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=4
[2025.06.05-12.02.53:665][ 60]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=4
[2025.06.05-12.02.53:665][ 60]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=4
[2025.06.05-12.02.53:665][ 60]LogInit: FAudioDevice initialized with ID 4.
[2025.06.05-12.02.53:665][ 60]LogAudio: Display: Audio Device (ID: 4) registered with world 'DefaultLevel'.
[2025.06.05-12.02.53:665][ 60]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 4
[2025.06.05-12.02.53:665][ 60]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-12.02.53:668][ 60]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-12.02.53:697][ 60]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-12.02.53:705][ 60]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-17.32.53
[2025.06.05-12.02.53:712][ 60]LogWorld: Bringing up level for play took: 0.044092
[2025.06.05-12.02.53:715][ 60]LogOnline: OSS: Created online subsystem instance for: :Context_16
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-12.02.53:732][ 60]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-12.02.53:745][ 60]PIE: Server logged in
[2025.06.05-12.02.53:746][ 60]PIE: Play in editor total start time 0.241 seconds.
[2025.06.05-12.02.53:898][ 61]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-12.03.03:880][402]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.03.03:880][402]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.03.03:883][402]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-12.03.03:883][402]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-12.03.03:883][402]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.03.03:885][402]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.03.03:902][402]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-12.03.03:914][402]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.03.03:949][402]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-12.03.03:974][402]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 4
[2025.06.05-12.03.03:974][402]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4
[2025.06.05-12.03.03:976][402]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=4
[2025.06.05-12.03.03:981][402]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.03.03:988][402]LogUObjectHash: Compacting FUObjectHashTables data took   1.94ms
[2025.06.05-12.03.04:068][403]LogPlayLevel: Display: Destroying online subsystem :Context_16
[2025.06.05-12.03.22:362][529]LogSlate: Took 0.000149 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.06.05-12.03.23:757][606]LogUObjectHash: Compacting FUObjectHashTables data took   1.84ms
[2025.06.05-12.03.23:800][606]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_25
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.23:800][606]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_25
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.23:804][606]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_26
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.23:804][606]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_26
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.24:134][608]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_27
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.24:135][608]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_27
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.28:999][778]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-12.03.29:029][778]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.03.29:087][778]LogPlayLevel: PIE: StaticDuplicateObject took: (0.058320s)
[2025.06.05-12.03.29:087][778]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.058386s)
[2025.06.05-12.03.29:123][778]LogUObjectHash: Compacting FUObjectHashTables data took   1.85ms
[2025.06.05-12.03.29:131][778]r.RayTracing.Culling = "0"
[2025.06.05-12.03.29:131][778]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-12.03.29:131][778]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-12.03.29:134][778]LogPlayLevel: PIE: World Init took: (0.001790s)
[2025.06.05-12.03.29:134][778]LogAudio: Display: Creating Audio Device:                 Id: 5, Scope: Unique, Realtime: True
[2025.06.05-12.03.29:134][778]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-12.03.29:134][778]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-12.03.29:134][778]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-12.03.29:134][778]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-12.03.29:134][778]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-12.03.29:134][778]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-12.03.29:134][778]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-12.03.29:134][778]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-12.03.29:134][778]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-12.03.29:134][778]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-12.03.29:134][778]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-12.03.29:137][778]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-12.03.29:172][778]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-12.03.29:173][778]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-12.03.29:173][778]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-12.03.29:173][778]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-12.03.29:174][778]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=5
[2025.06.05-12.03.29:174][778]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=5
[2025.06.05-12.03.29:178][778]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=5
[2025.06.05-12.03.29:178][778]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=5
[2025.06.05-12.03.29:178][778]LogInit: FAudioDevice initialized with ID 5.
[2025.06.05-12.03.29:178][778]LogAudio: Display: Audio Device (ID: 5) registered with world 'DefaultLevel'.
[2025.06.05-12.03.29:178][778]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 5
[2025.06.05-12.03.29:178][778]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-12.03.29:181][778]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-12.03.29:210][778]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-12.03.29:219][778]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-17.33.29
[2025.06.05-12.03.29:228][778]LogWorld: Bringing up level for play took: 0.046852
[2025.06.05-12.03.29:231][778]LogOnline: OSS: Created online subsystem instance for: :Context_17
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-12.03.29:247][778]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-12.03.29:262][778]PIE: Server logged in
[2025.06.05-12.03.29:263][778]PIE: Play in editor total start time 0.274 seconds.
[2025.06.05-12.03.29:394][779]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-12.03.39:411][102]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.03.39:411][102]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.03.39:413][102]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-12.03.39:413][102]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-12.03.39:413][102]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.03.39:416][102]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.03.39:422][102]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-12.03.39:435][102]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.03.39:470][102]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-12.03.39:494][102]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 5
[2025.06.05-12.03.39:494][102]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5
[2025.06.05-12.03.39:497][102]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=5
[2025.06.05-12.03.39:501][102]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.03.39:508][102]LogUObjectHash: Compacting FUObjectHashTables data took   2.58ms
[2025.06.05-12.03.39:590][103]LogPlayLevel: Display: Destroying online subsystem :Context_17
[2025.06.05-12.03.49:192][614]LogUObjectHash: Compacting FUObjectHashTables data took   2.20ms
[2025.06.05-12.03.49:234][614]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_34
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.49:234][614]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_34
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.49:238][614]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_35
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.49:239][614]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_35
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.49:535][616]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_36
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.49:535][616]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_36
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.03.50:585][652]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.05-12.03.50:603][652]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-12.03.50:603][652]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.03.50:663][652]LogPlayLevel: PIE: StaticDuplicateObject took: (0.060310s)
[2025.06.05-12.03.50:663][652]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.060361s)
[2025.06.05-12.03.50:699][652]LogUObjectHash: Compacting FUObjectHashTables data took   1.69ms
[2025.06.05-12.03.50:705][652]r.RayTracing.Culling = "0"
[2025.06.05-12.03.50:705][652]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-12.03.50:705][652]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-12.03.50:708][652]LogPlayLevel: PIE: World Init took: (0.002200s)
[2025.06.05-12.03.50:708][652]LogAudio: Display: Creating Audio Device:                 Id: 6, Scope: Unique, Realtime: True
[2025.06.05-12.03.50:708][652]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-12.03.50:708][652]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-12.03.50:708][652]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-12.03.50:708][652]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-12.03.50:708][652]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-12.03.50:708][652]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-12.03.50:708][652]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-12.03.50:708][652]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-12.03.50:708][652]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-12.03.50:708][652]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-12.03.50:708][652]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-12.03.50:711][652]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-12.03.50:747][652]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-12.03.50:747][652]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-12.03.50:747][652]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-12.03.50:747][652]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-12.03.50:748][652]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=6
[2025.06.05-12.03.50:749][652]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=6
[2025.06.05-12.03.50:751][652]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=6
[2025.06.05-12.03.50:751][652]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=6
[2025.06.05-12.03.50:751][652]LogInit: FAudioDevice initialized with ID 6.
[2025.06.05-12.03.50:751][652]LogAudio: Display: Audio Device (ID: 6) registered with world 'DefaultLevel'.
[2025.06.05-12.03.50:751][652]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 6
[2025.06.05-12.03.50:751][652]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-12.03.50:754][652]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-12.03.50:786][652]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-12.03.50:795][652]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-17.33.50
[2025.06.05-12.03.50:802][652]LogWorld: Bringing up level for play took: 0.047995
[2025.06.05-12.03.50:805][652]LogOnline: OSS: Created online subsystem instance for: :Context_18
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-12.03.50:822][652]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-12.03.50:835][652]PIE: Server logged in
[2025.06.05-12.03.50:836][652]PIE: Play in editor total start time 0.245 seconds.
[2025.06.05-12.03.50:971][653]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-12.03.52:890][715]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.03.52:890][715]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.03.52:894][715]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-12.03.52:894][715]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-12.03.52:894][715]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.03.52:897][715]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.03.52:915][715]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-12.03.52:928][715]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.03.52:963][715]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.05-12.03.52:987][715]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 6
[2025.06.05-12.03.52:987][715]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=6
[2025.06.05-12.03.52:989][715]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=6
[2025.06.05-12.03.52:995][715]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.03.53:001][715]LogUObjectHash: Compacting FUObjectHashTables data took   1.90ms
[2025.06.05-12.03.53:084][716]LogPlayLevel: Display: Destroying online subsystem :Context_18
[2025.06.05-12.03.57:575][853]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.05-12.03.57:575][853]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.03.57:636][853]LogPlayLevel: PIE: StaticDuplicateObject took: (0.061161s)
[2025.06.05-12.03.57:636][853]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.061211s)
[2025.06.05-12.03.57:670][853]LogUObjectHash: Compacting FUObjectHashTables data took   1.78ms
[2025.06.05-12.03.57:675][853]r.RayTracing.Culling = "0"
[2025.06.05-12.03.57:675][853]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.05-12.03.57:675][853]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.05-12.03.57:677][853]LogPlayLevel: PIE: World Init took: (0.002230s)
[2025.06.05-12.03.57:678][853]LogAudio: Display: Creating Audio Device:                 Id: 7, Scope: Unique, Realtime: True
[2025.06.05-12.03.57:678][853]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.05-12.03.57:678][853]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.05-12.03.57:678][853]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.05-12.03.57:678][853]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.05-12.03.57:678][853]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.05-12.03.57:678][853]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.05-12.03.57:678][853]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.05-12.03.57:678][853]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.05-12.03.57:678][853]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.05-12.03.57:678][853]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.05-12.03.57:678][853]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.05-12.03.57:680][853]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.05-12.03.57:717][853]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.05-12.03.57:717][853]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.05-12.03.57:717][853]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.05-12.03.57:717][853]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.05-12.03.57:718][853]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=7
[2025.06.05-12.03.57:718][853]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=7
[2025.06.05-12.03.57:720][853]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=7
[2025.06.05-12.03.57:720][853]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=7
[2025.06.05-12.03.57:720][853]LogInit: FAudioDevice initialized with ID 7.
[2025.06.05-12.03.57:720][853]LogAudio: Display: Audio Device (ID: 7) registered with world 'DefaultLevel'.
[2025.06.05-12.03.57:720][853]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 7
[2025.06.05-12.03.57:720][853]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.05-12.03.57:724][853]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.05-12.03.57:752][853]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.05-12.03.57:760][853]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.05-17.33.57
[2025.06.05-12.03.57:767][853]LogWorld: Bringing up level for play took: 0.042880
[2025.06.05-12.03.57:770][853]LogOnline: OSS: Created online subsystem instance for: :Context_19
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.05-12.03.57:785][853]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.05-12.03.57:798][853]PIE: Server logged in
[2025.06.05-12.03.57:799][853]PIE: Play in editor total start time 0.232 seconds.
[2025.06.05-12.03.57:930][854]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.05-12.04.03:559][ 29]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.04.03:588][ 30]LogTemp: Warning: Focused Object = TestBed_C_1
[2025.06.05-12.04.05:179][ 83]LogScript: Warning: Accessed None trying to read property FlashLightRef
	BP_BaoliCharacter_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.BP_BaoliCharacter_C_0
	Function /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter_C:ExecuteUbergraph_BP_BaoliCharacter:0603
[2025.06.05-12.04.05:179][ 83]PIE: Error: Blueprint Runtime Error: "Accessed None trying to read property FlashLightRef". Node:  PowerFlashON Graph:  EventGraph Function:  Execute Ubergraph BP Baoli Character Blueprint:  BP_BaoliCharacter
[2025.06.05-12.04.05:179][ 83]LogBlueprintUserMessages: [BPC_Inventory] Printing Inventory
[2025.06.05-12.04.09:317][228]LogScript: Warning: Script Msg: TestBed_C_1 InterpAim SetTimer passed a negative or zero time. The associated timer may fail to be created/fire! If using InitialStartDelayVariance, be sure it is smaller than (Time + InitialStartDelay).
[2025.06.05-12.04.09:317][228]LogScript: Warning: Script Msg called by: BP_Bed_C /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel:PersistentLevel.TestBed_C_1
[2025.06.05-12.04.10:650][270]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.04.10:650][270]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.05-12.04.10:653][270]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.05-12.04.10:653][270]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.05-12.04.10:654][270]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.04.10:656][270]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.04.10:668][270]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.05-12.04.10:681][270]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.04.10:737][270]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.05-12.04.10:739][270]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 7
[2025.06.05-12.04.10:740][270]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=7
[2025.06.05-12.04.10:743][270]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=7
[2025.06.05-12.04.10:757][270]LogUObjectHash: Compacting FUObjectHashTables data took   2.26ms
[2025.06.05-12.04.10:921][271]LogPlayLevel: Display: Destroying online subsystem :Context_19
[2025.06.05-12.04.12:577][325]LogSlate: Window 'Message Log' being destroyed
[2025.06.05-12.04.20:699][740]LogUObjectHash: Compacting FUObjectHashTables data took   1.79ms
[2025.06.05-12.04.20:742][740]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_43
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.20:744][740]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_43
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.20:749][740]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_44
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.20:749][740]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_44
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.21:052][742]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_45
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.21:053][742]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_45
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.27:271][118]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 356.051636
[2025.06.05-12.04.27:583][136]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.05-12.04.27:583][136]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 356.346954, Update Interval: 305.579407
[2025.06.05-12.04.48:129][433]LogAssetEditorSubsystem: Opening Asset editor for ControlRigBlueprint /Game/Characters/BaoliMC/CR_HeadManager.CR_HeadManager
[2025.06.05-12.04.48:129][433]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.06.05-12.04.48:150][433]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-12.04.48:171][433]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.06.05-12.04.48:180][433]LogStreaming: Display: FlushAsyncLoading(557): 1 QueuedPackages, 0 AsyncPackages
[2025.06.05-12.04.48:182][433]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_14:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.05-12.04.48:182][433]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_14:PersistentLevel.AnimationEditorPreviewActor_0.ControlRigSkeletalMeshComponent_0) Already registered. Aborting.
[2025.06.05-12.04.48:412][433]LogUObjectHash: Compacting FUObjectHashTables data took   1.85ms
[2025.06.05-12.04.48:657][433]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.05-12.04.48:867][433]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:34:33
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-12.04.48:888][433]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-12.04.48:946][434]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_49
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.48:946][434]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_49
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.48:950][434]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_50
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.48:951][434]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_50
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/05 17:34:33
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.05-12.04.48:955][434]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.05-12.04.48:956][434]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.05-12.04.48:956][434]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.05-12.04.48:972][435]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_51
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.48:974][435]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_51
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.04.51:694][660]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.04.51:694][660]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.04.51:708][660]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.04.51:708][660]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.04.51:723][660]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.05-12.04.51:954][660]LogUObjectHash: Compacting FUObjectHashTables data took   1.99ms
[2025.06.05-12.05.00:682][237]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_52
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:682][237]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_52
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:769][238]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_53
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:769][238]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_53
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:802][239]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_54
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:803][239]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_54
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:836][240]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_55
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:837][240]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_55
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:885][241]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_56
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:885][241]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_56
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:922][242]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_57
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:923][242]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_57
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:963][243]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_58
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.00:964][243]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_58
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.01:008][244]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_59
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.01:009][244]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_59
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.01:045][245]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_60
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.01:045][245]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_60
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1129
[2025.06.05-12.05.02:955][305]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/CBP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/GM_Sandbox.uasset H:/P4/dev/Baoli/Content/Blueprints/Kettle_BP.uasset H:/P4/dev/Baoli/Content/Blueprints/QTE.uasset'
[2025.06.05-12.06.01:524][636]LogUObjectHash: Compacting FUObjectHashTables data took   1.93ms
[2025.06.05-12.06.04:238][636]LogSlate: Window 'Save Content' being destroyed
[2025.06.05-12.06.04:269][636]LogStall: Shutdown...
[2025.06.05-12.06.04:269][636]LogStall: Shutdown complete.
[2025.06.05-12.06.04:305][636]LogObj: Warning: Ini File 'H:/P4/dev/Baoli/Config/DefaultEditor.ini' is read-only and cannot be written to
[2025.06.05-12.06.04:313][636]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:314][636]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:329][636]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:329][636]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:340][636]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:340][636]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:362][636]LogSlate: Window 'CHT_PoseSearchDatabases_Dense' being destroyed
[2025.06.05-12.06.04:392][636]LogSlate: Window 'Baoli - Unreal Editor' being destroyed
[2025.06.05-12.06.04:495][636]LogUObjectHash: Compacting FUObjectHashTables data took   1.79ms
[2025.06.05-12.06.04:521][636]Cmd: QUIT_EDITOR
[2025.06.05-12.06.04:521][636]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.06.05-12.06.04:525][637]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.06.05-12.06.04:525][637]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.06.05-12.06.04:525][637]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.06.05-12.06.04:530][637]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:530][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:537][637]LogStylusInput: Shutting down StylusInput subsystem.
[2025.06.05-12.06.04:538][637]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.06.05-12.06.04:543][637]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:543][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:548][637]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:548][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:553][637]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:553][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:557][637]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:557][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:562][637]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:562][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:565][637]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:565][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:569][637]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:569][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:574][637]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:574][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:577][637]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:577][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:582][637]LogWorld: UWorld::CleanupWorld for World_15, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:582][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:585][637]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.06.05-12.06.04:585][637]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.05-12.06.04:590][637]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.06.05-12.06.04:593][637]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.06.05-12.06.04:593][637]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.06.05-12.06.04:593][637]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.06.05-12.06.05:583][637]LogGameFeatures: Shutting down game features subsystem
[2025.06.05-12.06.05:583][637]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.06.05-12.06.05:583][637]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.06.05-12.06.05:583][637]LogAudio: Display: Audio Device unregistered from world 'DefaultLevel'.
[2025.06.05-12.06.05:583][637]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.06.05-12.06.05:583][637]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.05-12.06.05:586][637]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.05-12.06.05:592][637]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.06.05-12.06.05:592][637]LogAudio: Display: Audio Device Manager Shutdown
[2025.06.05-12.06.05:595][637]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.06.05-12.06.05:595][637]LogExit: Preparing to exit.
[2025.06.05-12.06.05:669][637]LogUObjectHash: Compacting FUObjectHashTables data took   2.13ms
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.add_controls_for_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_null_above_selected_PY.add_null_above_selected' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_all' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_x' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_y' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_translation_z' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_rotation' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/align_items_PY.align_scale' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_name_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.search_replace_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_prefix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.add_suffix_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_dialog' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/rename_items_PY.do_rename_entry' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/set_bone_reference_pose_PY.set_bone_reference_pose' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/ControlRigWorkflows/workflow_fbik_import_ik_rig_PY.import_ik_rig_options' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.05:677][637]LogPython: Display: Object '/ControlRig/Python/RigHierarchy/add_controls_for_selected_PY.ControlOutputFormat' was externally referenced when shutting down Python. Forcibly releasing its Python resources!
[2025.06.05-12.06.06:578][637]LogEditorDataStorage: Deinitializing
[2025.06.05-12.06.07:839][637]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.06.05-12.06.07:950][637]LogExit: Editor shut down
[2025.06.05-12.06.07:952][637]LogExit: Transaction tracking system shut down
[2025.06.05-12.06.09:912][637]LogExit: Object subsystem successfully closed.
[2025.06.05-12.06.09:946][637]LogShaderCompilers: Display: Shaders left to compile 0
[2025.06.05-12.06.11:371][637]LogMemoryProfiler: Shutdown
[2025.06.05-12.06.11:383][637]LogNetworkingProfiler: Shutdown
[2025.06.05-12.06.11:383][637]LoadingProfiler: Shutdown
[2025.06.05-12.06.11:388][637]LogTimingProfiler: Shutdown
[2025.06.05-12.06.12:588][637]LogChaosDD: Chaos Debug Draw Shutdown
[2025.06.05-12.06.12:643][637]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.06.05-12.06.12:643][637]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B7D5E7838-450B-F953-4365-C78AFAB3A523%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.5.4-40574608%2B%2B%2BUE5%2BRelease-5.5&UserID=5eff80b14364fb2f37e5468dbd2f7de6%7C0de775007ac941b984ac36d970a4fb1c%7C6ba94555-8ddf-4db5-b84c-ec5c48d27de3&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.06.05-12.06.13:775][637]LogEOSSDK: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.06.05-12.06.13:796][637]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.06.05-12.06.13:796][637]LogNFORDenoise: NFORDenoise function shutting down
[2025.06.05-12.06.13:797][637]RenderDocPlugin: plugin has been unloaded.
[2025.06.05-12.06.13:831][637]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.06.05-12.06.13:849][637]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.06.05-12.06.13:850][637]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.06.05-12.06.13:850][637]LogPakFile: Destroying PakPlatformFile
[2025.06.05-12.06.14:293][637]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.06.05-12.06.14:498][637]LogExit: Exiting.
[2025.06.05-12.06.14:519][637]Log file closed, 06/05/25 17:36:14
