// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Baoli/Interactable/Cover/Almirah.h"
#include "Runtime/Engine/Classes/Animation/AnimNotifies/AnimNotify.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAlmirah() {}

// Begin Cross Module References
BAOLI_API UClass* Z_Construct_UClass_AAlmirah();
BAOLI_API UClass* Z_Construct_UClass_AAlmirah_NoRegister();
BAOLI_API UClass* Z_Construct_UClass_ABaoli_Character_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UAnimInstance_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAnimMontage_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UArrowComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FBranchingPointNotifyPayload();
UPackage* Z_Construct_UPackage__Script_Baoli();
// End Cross Module References

// Begin Class AAlmirah Function ClearGarbage
struct Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics
{
	struct Almirah_eventClearGarbage_Parms
	{
		FName NotifyName;
		FBranchingPointNotifyPayload BranchingPointNotifyPayload;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BranchingPointNotifyPayload_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_NotifyName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BranchingPointNotifyPayload;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::NewProp_NotifyName = { "NotifyName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Almirah_eventClearGarbage_Parms, NotifyName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::NewProp_BranchingPointNotifyPayload = { "BranchingPointNotifyPayload", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Almirah_eventClearGarbage_Parms, BranchingPointNotifyPayload), Z_Construct_UScriptStruct_FBranchingPointNotifyPayload, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BranchingPointNotifyPayload_MetaData), NewProp_BranchingPointNotifyPayload_MetaData) }; // 746097459
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::NewProp_NotifyName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::NewProp_BranchingPointNotifyPayload,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAlmirah, nullptr, "ClearGarbage", nullptr, nullptr, Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::Almirah_eventClearGarbage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::Almirah_eventClearGarbage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAlmirah_ClearGarbage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAlmirah_ClearGarbage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAlmirah::execClearGarbage)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_NotifyName);
	P_GET_STRUCT_REF(FBranchingPointNotifyPayload,Z_Param_Out_BranchingPointNotifyPayload);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearGarbage(Z_Param_NotifyName,Z_Param_Out_BranchingPointNotifyPayload);
	P_NATIVE_END;
}
// End Class AAlmirah Function ClearGarbage

// Begin Class AAlmirah Function GetFunctionNameAsString
struct Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics
{
	struct Almirah_eventGetFunctionNameAsString_Parms
	{
		FName FuncName;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Character Aim Controller\n" },
#endif
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Character Aim Controller" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_FuncName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::NewProp_FuncName = { "FuncName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Almirah_eventGetFunctionNameAsString_Parms, FuncName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Almirah_eventGetFunctionNameAsString_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::NewProp_FuncName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAlmirah, nullptr, "GetFunctionNameAsString", nullptr, nullptr, Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::Almirah_eventGetFunctionNameAsString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::Almirah_eventGetFunctionNameAsString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAlmirah::execGetFunctionNameAsString)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_FuncName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetFunctionNameAsString(Z_Param_FuncName);
	P_NATIVE_END;
}
// End Class AAlmirah Function GetFunctionNameAsString

// Begin Class AAlmirah Function HideInAlmirah
struct Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics
{
	struct Almirah_eventHideInAlmirah_Parms
	{
		ABaoli_Character* Character;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AlmirahInteraction" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Character;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::NewProp_Character = { "Character", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Almirah_eventHideInAlmirah_Parms, Character), Z_Construct_UClass_ABaoli_Character_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::NewProp_Character,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAlmirah, nullptr, "HideInAlmirah", nullptr, nullptr, Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::Almirah_eventHideInAlmirah_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::Almirah_eventHideInAlmirah_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAlmirah_HideInAlmirah()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAlmirah_HideInAlmirah_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAlmirah::execHideInAlmirah)
{
	P_GET_OBJECT(ABaoli_Character,Z_Param_Character);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideInAlmirah(Z_Param_Character);
	P_NATIVE_END;
}
// End Class AAlmirah Function HideInAlmirah

// Begin Class AAlmirah Function HideOutAlmirah
struct Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics
{
	struct Almirah_eventHideOutAlmirah_Parms
	{
		ABaoli_Character* Character;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AlmirahInteraction" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Character;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::NewProp_Character = { "Character", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Almirah_eventHideOutAlmirah_Parms, Character), Z_Construct_UClass_ABaoli_Character_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::NewProp_Character,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAlmirah, nullptr, "HideOutAlmirah", nullptr, nullptr, Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::Almirah_eventHideOutAlmirah_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::Almirah_eventHideOutAlmirah_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAlmirah_HideOutAlmirah()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAlmirah_HideOutAlmirah_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAlmirah::execHideOutAlmirah)
{
	P_GET_OBJECT(ABaoli_Character,Z_Param_Character);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideOutAlmirah(Z_Param_Character);
	P_NATIVE_END;
}
// End Class AAlmirah Function HideOutAlmirah

// Begin Class AAlmirah Function InterpAimAlmirah
struct Z_Construct_UFunction_AAlmirah_InterpAimAlmirah_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAlmirah_InterpAimAlmirah_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAlmirah, nullptr, "InterpAimAlmirah", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_InterpAimAlmirah_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAlmirah_InterpAimAlmirah_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AAlmirah_InterpAimAlmirah()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAlmirah_InterpAimAlmirah_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAlmirah::execInterpAimAlmirah)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InterpAimAlmirah();
	P_NATIVE_END;
}
// End Class AAlmirah Function InterpAimAlmirah

// Begin Class AAlmirah Function OnBlendingOutAnim
struct Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics
{
	struct Almirah_eventOnBlendingOutAnim_Parms
	{
		UAnimMontage* Montage;
		bool bInterupted;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Montage;
	static void NewProp_bInterupted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInterupted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::NewProp_Montage = { "Montage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Almirah_eventOnBlendingOutAnim_Parms, Montage), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::NewProp_bInterupted_SetBit(void* Obj)
{
	((Almirah_eventOnBlendingOutAnim_Parms*)Obj)->bInterupted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::NewProp_bInterupted = { "bInterupted", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Almirah_eventOnBlendingOutAnim_Parms), &Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::NewProp_bInterupted_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::NewProp_Montage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::NewProp_bInterupted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAlmirah, nullptr, "OnBlendingOutAnim", nullptr, nullptr, Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::Almirah_eventOnBlendingOutAnim_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::Almirah_eventOnBlendingOutAnim_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAlmirah::execOnBlendingOutAnim)
{
	P_GET_OBJECT(UAnimMontage,Z_Param_Montage);
	P_GET_UBOOL(Z_Param_bInterupted);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnBlendingOutAnim(Z_Param_Montage,Z_Param_bInterupted);
	P_NATIVE_END;
}
// End Class AAlmirah Function OnBlendingOutAnim

// Begin Class AAlmirah Function OnNotify
struct Z_Construct_UFunction_AAlmirah_OnNotify_Statics
{
	struct Almirah_eventOnNotify_Parms
	{
		FName NotifyName;
		FBranchingPointNotifyPayload BranchingPointNotifyPayload;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BranchingPointNotifyPayload_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_NotifyName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BranchingPointNotifyPayload;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AAlmirah_OnNotify_Statics::NewProp_NotifyName = { "NotifyName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Almirah_eventOnNotify_Parms, NotifyName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAlmirah_OnNotify_Statics::NewProp_BranchingPointNotifyPayload = { "BranchingPointNotifyPayload", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Almirah_eventOnNotify_Parms, BranchingPointNotifyPayload), Z_Construct_UScriptStruct_FBranchingPointNotifyPayload, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BranchingPointNotifyPayload_MetaData), NewProp_BranchingPointNotifyPayload_MetaData) }; // 746097459
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAlmirah_OnNotify_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_OnNotify_Statics::NewProp_NotifyName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_OnNotify_Statics::NewProp_BranchingPointNotifyPayload,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_OnNotify_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAlmirah_OnNotify_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAlmirah, nullptr, "OnNotify", nullptr, nullptr, Z_Construct_UFunction_AAlmirah_OnNotify_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_OnNotify_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAlmirah_OnNotify_Statics::Almirah_eventOnNotify_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_OnNotify_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAlmirah_OnNotify_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAlmirah_OnNotify_Statics::Almirah_eventOnNotify_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAlmirah_OnNotify()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAlmirah_OnNotify_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAlmirah::execOnNotify)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_NotifyName);
	P_GET_STRUCT_REF(FBranchingPointNotifyPayload,Z_Param_Out_BranchingPointNotifyPayload);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnNotify(Z_Param_NotifyName,Z_Param_Out_BranchingPointNotifyPayload);
	P_NATIVE_END;
}
// End Class AAlmirah Function OnNotify

// Begin Class AAlmirah Function OnNotifyBeginReceived
struct Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics
{
	struct Almirah_eventOnNotifyBeginReceived_Parms
	{
		FName NotifyName;
		FBranchingPointNotifyPayload BranchingPointNotifyPayload;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Binding functions on Montages\n" },
#endif
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Binding functions on Montages" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BranchingPointNotifyPayload_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_NotifyName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BranchingPointNotifyPayload;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::NewProp_NotifyName = { "NotifyName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Almirah_eventOnNotifyBeginReceived_Parms, NotifyName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::NewProp_BranchingPointNotifyPayload = { "BranchingPointNotifyPayload", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Almirah_eventOnNotifyBeginReceived_Parms, BranchingPointNotifyPayload), Z_Construct_UScriptStruct_FBranchingPointNotifyPayload, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BranchingPointNotifyPayload_MetaData), NewProp_BranchingPointNotifyPayload_MetaData) }; // 746097459
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::NewProp_NotifyName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::NewProp_BranchingPointNotifyPayload,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAlmirah, nullptr, "OnNotifyBeginReceived", nullptr, nullptr, Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::Almirah_eventOnNotifyBeginReceived_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::Almirah_eventOnNotifyBeginReceived_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAlmirah::execOnNotifyBeginReceived)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_NotifyName);
	P_GET_STRUCT_REF(FBranchingPointNotifyPayload,Z_Param_Out_BranchingPointNotifyPayload);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnNotifyBeginReceived(Z_Param_NotifyName,Z_Param_Out_BranchingPointNotifyPayload);
	P_NATIVE_END;
}
// End Class AAlmirah Function OnNotifyBeginReceived

// Begin Class AAlmirah
void AAlmirah::StaticRegisterNativesAAlmirah()
{
	UClass* Class = AAlmirah::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearGarbage", &AAlmirah::execClearGarbage },
		{ "GetFunctionNameAsString", &AAlmirah::execGetFunctionNameAsString },
		{ "HideInAlmirah", &AAlmirah::execHideInAlmirah },
		{ "HideOutAlmirah", &AAlmirah::execHideOutAlmirah },
		{ "InterpAimAlmirah", &AAlmirah::execInterpAimAlmirah },
		{ "OnBlendingOutAnim", &AAlmirah::execOnBlendingOutAnim },
		{ "OnNotify", &AAlmirah::execOnNotify },
		{ "OnNotifyBeginReceived", &AAlmirah::execOnNotifyBeginReceived },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AAlmirah);
UClass* Z_Construct_UClass_AAlmirah_NoRegister()
{
	return AAlmirah::StaticClass();
}
struct Z_Construct_UClass_AAlmirah_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "IncludePath", "Interactable/Cover/Almirah.h" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SceneRoot_MetaData[] = {
		{ "Category", "Construction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Construction\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Construction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlmirahFrame_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SK_AlmirahLeft_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SK_AlmirahRight_MetaData[] = {
		{ "Category", "Construction" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AReference_MetaData[] = {
		{ "Category", "References" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CBaoli_Character_MetaData[] = {
		{ "Category", "Refrences" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanGetOut_MetaData[] = {
		{ "Category", "References" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_In_Almirah_MetaData[] = {
		{ "Category", "References" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Out_Almirah_MetaData[] = {
		{ "Category", "References" },
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CharacterAnimInstance_MetaData[] = {
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlmirahAnimInstance_MetaData[] = {
		{ "ModuleRelativePath", "Interactable/Cover/Almirah.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SceneRoot;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AlmirahFrame;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SK_AlmirahLeft;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SK_AlmirahRight;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AReference;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CBaoli_Character;
	static void NewProp_bCanGetOut_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanGetOut;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_In_Almirah;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Out_Almirah;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CharacterAnimInstance;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AlmirahAnimInstance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAlmirah_ClearGarbage, "ClearGarbage" }, // 3901941445
		{ &Z_Construct_UFunction_AAlmirah_GetFunctionNameAsString, "GetFunctionNameAsString" }, // 1930673272
		{ &Z_Construct_UFunction_AAlmirah_HideInAlmirah, "HideInAlmirah" }, // 2135717468
		{ &Z_Construct_UFunction_AAlmirah_HideOutAlmirah, "HideOutAlmirah" }, // 3653191031
		{ &Z_Construct_UFunction_AAlmirah_InterpAimAlmirah, "InterpAimAlmirah" }, // 229975371
		{ &Z_Construct_UFunction_AAlmirah_OnBlendingOutAnim, "OnBlendingOutAnim" }, // 3110228730
		{ &Z_Construct_UFunction_AAlmirah_OnNotify, "OnNotify" }, // 539174603
		{ &Z_Construct_UFunction_AAlmirah_OnNotifyBeginReceived, "OnNotifyBeginReceived" }, // 3763537264
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAlmirah>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAlmirah_Statics::NewProp_SceneRoot = { "SceneRoot", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAlmirah, SceneRoot), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SceneRoot_MetaData), NewProp_SceneRoot_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAlmirah_Statics::NewProp_AlmirahFrame = { "AlmirahFrame", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAlmirah, AlmirahFrame), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlmirahFrame_MetaData), NewProp_AlmirahFrame_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAlmirah_Statics::NewProp_SK_AlmirahLeft = { "SK_AlmirahLeft", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAlmirah, SK_AlmirahLeft), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SK_AlmirahLeft_MetaData), NewProp_SK_AlmirahLeft_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAlmirah_Statics::NewProp_SK_AlmirahRight = { "SK_AlmirahRight", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAlmirah, SK_AlmirahRight), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SK_AlmirahRight_MetaData), NewProp_SK_AlmirahRight_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAlmirah_Statics::NewProp_AReference = { "AReference", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAlmirah, AReference), Z_Construct_UClass_UArrowComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AReference_MetaData), NewProp_AReference_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAlmirah_Statics::NewProp_CBaoli_Character = { "CBaoli_Character", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAlmirah, CBaoli_Character), Z_Construct_UClass_ABaoli_Character_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CBaoli_Character_MetaData), NewProp_CBaoli_Character_MetaData) };
void Z_Construct_UClass_AAlmirah_Statics::NewProp_bCanGetOut_SetBit(void* Obj)
{
	((AAlmirah*)Obj)->bCanGetOut = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAlmirah_Statics::NewProp_bCanGetOut = { "bCanGetOut", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAlmirah), &Z_Construct_UClass_AAlmirah_Statics::NewProp_bCanGetOut_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanGetOut_MetaData), NewProp_bCanGetOut_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAlmirah_Statics::NewProp_In_Almirah = { "In_Almirah", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAlmirah, In_Almirah), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_In_Almirah_MetaData), NewProp_In_Almirah_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAlmirah_Statics::NewProp_Out_Almirah = { "Out_Almirah", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAlmirah, Out_Almirah), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Out_Almirah_MetaData), NewProp_Out_Almirah_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAlmirah_Statics::NewProp_CharacterAnimInstance = { "CharacterAnimInstance", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAlmirah, CharacterAnimInstance), Z_Construct_UClass_UAnimInstance_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CharacterAnimInstance_MetaData), NewProp_CharacterAnimInstance_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAlmirah_Statics::NewProp_AlmirahAnimInstance = { "AlmirahAnimInstance", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAlmirah, AlmirahAnimInstance), Z_Construct_UClass_UAnimInstance_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlmirahAnimInstance_MetaData), NewProp_AlmirahAnimInstance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAlmirah_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAlmirah_Statics::NewProp_SceneRoot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAlmirah_Statics::NewProp_AlmirahFrame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAlmirah_Statics::NewProp_SK_AlmirahLeft,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAlmirah_Statics::NewProp_SK_AlmirahRight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAlmirah_Statics::NewProp_AReference,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAlmirah_Statics::NewProp_CBaoli_Character,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAlmirah_Statics::NewProp_bCanGetOut,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAlmirah_Statics::NewProp_In_Almirah,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAlmirah_Statics::NewProp_Out_Almirah,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAlmirah_Statics::NewProp_CharacterAnimInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAlmirah_Statics::NewProp_AlmirahAnimInstance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAlmirah_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAlmirah_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_Baoli,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAlmirah_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAlmirah_Statics::ClassParams = {
	&AAlmirah::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAlmirah_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAlmirah_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAlmirah_Statics::Class_MetaDataParams), Z_Construct_UClass_AAlmirah_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAlmirah()
{
	if (!Z_Registration_Info_UClass_AAlmirah.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAlmirah.OuterSingleton, Z_Construct_UClass_AAlmirah_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAlmirah.OuterSingleton;
}
template<> BAOLI_API UClass* StaticClass<AAlmirah>()
{
	return AAlmirah::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAlmirah);
AAlmirah::~AAlmirah() {}
// End Class AAlmirah

// Begin Registration
struct Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAlmirah, AAlmirah::StaticClass, TEXT("AAlmirah"), &Z_Registration_Info_UClass_AAlmirah, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAlmirah), 1250457747U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_1929283018(TEXT("/Script/Baoli"),
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Interactable_Cover_Almirah_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
