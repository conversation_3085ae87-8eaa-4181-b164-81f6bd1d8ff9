[/Script/GameplayTags.GameplayTagsSettings]
ImportTagsFromConfig=True
WarnOnInvalidTags=True
ClearInvalidTags=False
AllowEditorTagUnloading=True
AllowGameTagUnloading=False
FastReplication=False
InvalidTagCharacters="\"\',"
NumBitsForContainerSize=6
NetIndexFirstBitSegment=16
+GameplayTagList=(Tag="Abilities.ContextualAnim",DevComment="")
+GameplayTagList=(Tag="Abilities.MoveTo",DevComment="")
+GameplayTagList=(Tag="Foley",DevComment="")
+GameplayTagList=(Tag="Foley.Event",DevComment="")
+GameplayTagList=(Tag="Foley.Event.Handplant",DevComment="")
+GameplayTagList=(Tag="Foley.Event.Jump",DevComment="")
+GameplayTagList=(Tag="Foley.Event.Land",DevComment="")
+GameplayTagList=(Tag="Foley.Event.Run",DevComment="")
+GameplayTagList=(Tag="Foley.Event.RunBackwds",DevComment="")
+GameplayTagList=(Tag="Foley.Event.RunStrafe",DevComment="")
+GameplayTagList=(Tag="Foley.Event.Scuff",DevComment="")
+GameplayTagList=(Tag="Foley.Event.ScuffPivot",DevComment="")
+GameplayTagList=(Tag="Foley.Event.ScuffWall",DevComment="")
+GameplayTagList=(Tag="Foley.Event.Tumble",DevComment="")
+GameplayTagList=(Tag="Foley.Event.Walk",DevComment="")
+GameplayTagList=(Tag="Foley.Event.WalkBackwds",DevComment="")
+GameplayTagList=(Tag="GameplayCue.ContextualAnim",DevComment="")
+GameplayTagList=(Tag="MotionMatching",DevComment="")
+GameplayTagList=(Tag="MotionMatching.Default",DevComment="")
+GameplayTagList=(Tag="MotionMatching.Idle",DevComment="")
+GameplayTagList=(Tag="MotionMatching.Loops",DevComment="")
+GameplayTagList=(Tag="MotionMatching.Pivots",DevComment="")
+GameplayTagList=(Tag="MotionMatching.Starts",DevComment="")
+GameplayTagList=(Tag="MotionMatching.Stops",DevComment="")

