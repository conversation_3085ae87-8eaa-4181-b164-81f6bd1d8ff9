// Copyright Epic Games, Inc. All Rights Reserved.
// Modifications Copyright (C) 2025 Anshul <PERSON>

#include "OptimizedWebBrowserModule.h"
#include "WebBrowserModule.h"
#include "IWebBrowserSingleton.h"
#include "Misc/Paths.h"
#include "HAL/FileManager.h"

//////////////////////////////////////////////////////////////////////////
// FOptimizedWebBrowserModule

class FOptimizedWebBrowserModule : public IOptimizedWebBrowserModule
{
public:
	virtual void StartupModule() override
	{
		// Ensure the WebBrowser module is loaded
		IWebBrowserModule::Get();

		// Copy the optimization JavaScript file to the project's content directory
		CopyOptimizationJavaScriptToContent();
	}

	virtual void ShutdownModule() override
	{
	}

private:
	void CopyOptimizationJavaScriptToContent()
	{
		// Get the path to the optimization JavaScript file in the plugin
		FString SourcePath = FPaths::Combine(FPaths::ProjectPluginsDir(), TEXT("OptimizedWebBrowser"), TEXT("Content"), TEXT("WebBrowserOptimizations.js"));

		// Get the path to the destination in the project's content directory
		FString DestPath = FPaths::Combine(FPaths::ProjectContentDir(), TEXT("WebBrowserOptimizations.js"));

		// Copy the file if it doesn't exist in the destination
		if (!FPaths::FileExists(DestPath) && FPaths::FileExists(SourcePath))
		{
			IFileManager::Get().Copy(*DestPath, *SourcePath);
		}
	}
};

//////////////////////////////////////////////////////////////////////////

IMPLEMENT_MODULE(FOptimizedWebBrowserModule, OptimizedWebBrowser);
