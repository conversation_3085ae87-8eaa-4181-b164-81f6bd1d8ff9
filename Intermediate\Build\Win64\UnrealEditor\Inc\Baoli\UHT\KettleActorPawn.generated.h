// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Interactable/Chest/KettleActorPawn.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef BAOLI_KettleActorPawn_generated_h
#error "KettleActorPawn.generated.h already included, missing '#pragma once' in KettleActorPawn.h"
#endif
#define BAOLI_KettleActorPawn_generated_h

#define FID_Baoli_Source_Baoli_Interactable_Chest_KettleActorPawn_h_14_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAKettleActorPawn(); \
	friend struct Z_Construct_UClass_AKettleActorPawn_Statics; \
public: \
	DECLARE_CLASS(AKettleActorPawn, APawn, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Baoli"), NO_API) \
	DECLARE_SERIALIZER(AKettleActorPawn)


#define FID_Baoli_Source_Baoli_Interactable_Chest_KettleActorPawn_h_14_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AKettleActorPawn(AKettleActorPawn&&); \
	AKettleActorPawn(const AKettleActorPawn&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AKettleActorPawn); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AKettleActorPawn); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AKettleActorPawn) \
	NO_API virtual ~AKettleActorPawn();


#define FID_Baoli_Source_Baoli_Interactable_Chest_KettleActorPawn_h_11_PROLOG
#define FID_Baoli_Source_Baoli_Interactable_Chest_KettleActorPawn_h_14_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Baoli_Source_Baoli_Interactable_Chest_KettleActorPawn_h_14_INCLASS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_Interactable_Chest_KettleActorPawn_h_14_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BAOLI_API UClass* StaticClass<class AKettleActorPawn>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Baoli_Source_Baoli_Interactable_Chest_KettleActorPawn_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
