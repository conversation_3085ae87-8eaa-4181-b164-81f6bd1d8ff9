// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for OptimizedWebBrowser
#pragma once
#include "H:/P4/dev/Baoli/Intermediate/Build/Win64/x64/BaoliEditor/Development/UnrealEd/SharedDefinitions.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h"
#undef OPTIMIZEDWEBBROWSER_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 1
#define UE_PROJECT_NAME Baoli
#define UE_TARGET_NAME BaoliEditor
#define UE_MODULE_NAME "OptimizedWebBrowser"
#define UE_PLUGIN_NAME "OptimizedWebBrowser"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define WEBBROWSERTEXTURE_API DLLIMPORT
#define MEDIAUTILS_API DLLIMPORT
#define MEDIA_API DLLIMPORT
#define OPTIMIZEDWEBBROWSER_API DLLEXPORT
#define WEBBROWSER_API DLLIMPORT
#define WEBBROWSERWIDGET_API DLLIMPORT
