// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Baoli/Player/Baoli_Controller.h"
#include "Runtime/Engine/Classes/Engine/HitResult.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeBaoli_Controller() {}

// Begin Cross Module References
BAOLI_API UClass* Z_Construct_UClass_ABaoli_Character_NoRegister();
BAOLI_API UClass* Z_Construct_UClass_ABaoli_Controller();
BAOLI_API UClass* Z_Construct_UClass_ABaoli_Controller_NoRegister();
BAOLI_API UEnum* Z_Construct_UEnum_Baoli_ECharacterState();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APlayerController();
ENGINE_API UClass* Z_Construct_UClass_UCameraComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputAction_NoRegister();
UPackage* Z_Construct_UPackage__Script_Baoli();
// End Cross Module References

// Begin Enum ECharacterState
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ECharacterState;
static UEnum* ECharacterState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ECharacterState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ECharacterState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Baoli_ECharacterState, (UObject*)Z_Construct_UPackage__Script_Baoli(), TEXT("ECharacterState"));
	}
	return Z_Registration_Info_UEnum_ECharacterState.OuterSingleton;
}
template<> BAOLI_API UEnum* StaticEnum<ECharacterState>()
{
	return ECharacterState_StaticEnum();
}
struct Z_Construct_UEnum_Baoli_ECharacterState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "GameMenu.DisplayName", "Game Menu" },
		{ "GameMenu.Name", "ECharacterState::GameMenu" },
		{ "Mechanic.DisplayName", "Mechanic" },
		{ "Mechanic.Name", "ECharacterState::Mechanic" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
		{ "OverWorld.DisplayName", "OverWorld" },
		{ "OverWorld.Name", "ECharacterState::OverWorld" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ECharacterState::OverWorld", (int64)ECharacterState::OverWorld },
		{ "ECharacterState::Mechanic", (int64)ECharacterState::Mechanic },
		{ "ECharacterState::GameMenu", (int64)ECharacterState::GameMenu },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Baoli_ECharacterState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Baoli,
	nullptr,
	"ECharacterState",
	"ECharacterState",
	Z_Construct_UEnum_Baoli_ECharacterState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Baoli_ECharacterState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Baoli_ECharacterState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Baoli_ECharacterState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Baoli_ECharacterState()
{
	if (!Z_Registration_Info_UEnum_ECharacterState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ECharacterState.InnerSingleton, Z_Construct_UEnum_Baoli_ECharacterState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ECharacterState.InnerSingleton;
}
// End Enum ECharacterState

// Begin Class ABaoli_Controller Function ChangeCharacterState
struct Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics
{
	struct Baoli_Controller_eventChangeCharacterState_Parms
	{
		ECharacterState pCharacterState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_pCharacterState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_pCharacterState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::NewProp_pCharacterState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::NewProp_pCharacterState = { "pCharacterState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Baoli_Controller_eventChangeCharacterState_Parms, pCharacterState), Z_Construct_UEnum_Baoli_ECharacterState, METADATA_PARAMS(0, nullptr) }; // 709515122
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::NewProp_pCharacterState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::NewProp_pCharacterState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABaoli_Controller, nullptr, "ChangeCharacterState", nullptr, nullptr, Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::Baoli_Controller_eventChangeCharacterState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::Baoli_Controller_eventChangeCharacterState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaoli_Controller::execChangeCharacterState)
{
	P_GET_ENUM(ECharacterState,Z_Param_pCharacterState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ChangeCharacterState(ECharacterState(Z_Param_pCharacterState));
	P_NATIVE_END;
}
// End Class ABaoli_Controller Function ChangeCharacterState

// Begin Class ABaoli_Controller Function GetCharacterState
struct Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics
{
	struct Baoli_Controller_eventGetCharacterState_Parms
	{
		ECharacterState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Get current state of character\n" },
#endif
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current state of character" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000008000582, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Baoli_Controller_eventGetCharacterState_Parms, ReturnValue), Z_Construct_UEnum_Baoli_ECharacterState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) }; // 709515122
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABaoli_Controller, nullptr, "GetCharacterState", nullptr, nullptr, Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::Baoli_Controller_eventGetCharacterState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::Baoli_Controller_eventGetCharacterState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaoli_Controller_GetCharacterState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaoli_Controller_GetCharacterState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaoli_Controller::execGetCharacterState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ECharacterState*)Z_Param__Result=P_THIS->GetCharacterState();
	P_NATIVE_END;
}
// End Class ABaoli_Controller Function GetCharacterState

// Begin Class ABaoli_Controller Function LineTrace
struct Z_Construct_UFunction_ABaoli_Controller_LineTrace_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaoli_Controller_LineTrace_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABaoli_Controller, nullptr, "LineTrace", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_LineTrace_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaoli_Controller_LineTrace_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ABaoli_Controller_LineTrace()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaoli_Controller_LineTrace_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaoli_Controller::execLineTrace)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LineTrace();
	P_NATIVE_END;
}
// End Class ABaoli_Controller Function LineTrace

// Begin Class ABaoli_Controller Function MovementInput
struct Baoli_Controller_eventMovementInput_Parms
{
	FVector2D Value;
	const UInputAction* Action;
};
static const FName NAME_ABaoli_Controller_MovementInput = FName(TEXT("MovementInput"));
void ABaoli_Controller::MovementInput(FVector2D Value, const UInputAction* Action)
{
	Baoli_Controller_eventMovementInput_Parms Parms;
	Parms.Value=Value;
	Parms.Action=Action;
	UFunction* Func = FindFunctionChecked(NAME_ABaoli_Controller_MovementInput);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Action_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Action;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Baoli_Controller_eventMovementInput_Parms, Value), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::NewProp_Action = { "Action", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Baoli_Controller_eventMovementInput_Parms, Action), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Action_MetaData), NewProp_Action_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::NewProp_Action,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABaoli_Controller, nullptr, "MovementInput", nullptr, nullptr, Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::PropPointers), sizeof(Baoli_Controller_eventMovementInput_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08820800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::Function_MetaDataParams) };
static_assert(sizeof(Baoli_Controller_eventMovementInput_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaoli_Controller_MovementInput()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaoli_Controller_MovementInput_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class ABaoli_Controller Function MovementInput

// Begin Class ABaoli_Controller Function TraceForCamera
struct Z_Construct_UFunction_ABaoli_Controller_TraceForCamera_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaoli_Controller_TraceForCamera_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABaoli_Controller, nullptr, "TraceForCamera", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_TraceForCamera_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaoli_Controller_TraceForCamera_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ABaoli_Controller_TraceForCamera()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaoli_Controller_TraceForCamera_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaoli_Controller::execTraceForCamera)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TraceForCamera();
	P_NATIVE_END;
}
// End Class ABaoli_Controller Function TraceForCamera

// Begin Class ABaoli_Controller Function TraceForInteraction
struct Z_Construct_UFunction_ABaoli_Controller_TraceForInteraction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "//---\n" },
#endif
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaoli_Controller_TraceForInteraction_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABaoli_Controller, nullptr, "TraceForInteraction", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Controller_TraceForInteraction_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaoli_Controller_TraceForInteraction_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ABaoli_Controller_TraceForInteraction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaoli_Controller_TraceForInteraction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaoli_Controller::execTraceForInteraction)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TraceForInteraction();
	P_NATIVE_END;
}
// End Class ABaoli_Controller Function TraceForInteraction

// Begin Class ABaoli_Controller
void ABaoli_Controller::StaticRegisterNativesABaoli_Controller()
{
	UClass* Class = ABaoli_Controller::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ChangeCharacterState", &ABaoli_Controller::execChangeCharacterState },
		{ "GetCharacterState", &ABaoli_Controller::execGetCharacterState },
		{ "LineTrace", &ABaoli_Controller::execLineTrace },
		{ "TraceForCamera", &ABaoli_Controller::execTraceForCamera },
		{ "TraceForInteraction", &ABaoli_Controller::execTraceForInteraction },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(ABaoli_Controller);
UClass* Z_Construct_UClass_ABaoli_Controller_NoRegister()
{
	return ABaoli_Controller::StaticClass();
}
struct Z_Construct_UClass_ABaoli_Controller_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "HideCategories", "Collision Rendering Transformation" },
		{ "IncludePath", "Player/Baoli_Controller.h" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Baoli_Character_MetaData[] = {
		{ "Category", "Refrences" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/////////////////////////////////////////////////// Refrences //////////////////////////////////////////////////////\n" },
#endif
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refrences" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Mesh_MetaData[] = {
		{ "Category", "Refrences" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Cam_MetaData[] = {
		{ "Category", "Refrences" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FocusedObject_MetaData[] = {
		{ "Category", "Variables" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "///////////////////////////////////////////////////// Variables ////////////////////////////////////////////////////\n" },
#endif
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Variables /" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveComponent_MetaData[] = {
		{ "Category", "Variables" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHoldTrace_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TraceDistance_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_hitResult_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLineHit_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInspecting_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SetFocalDistance_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SkillCheckResult_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDrawWidget_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MoveAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Input Actions & Functions\n" },
#endif
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input Actions & Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_IgnoreLineTraceActors_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CharacterState_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Controller.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Baoli_Character;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Mesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Cam;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FocusedObject;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveComponent;
	static void NewProp_bHoldTrace_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHoldTrace;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TraceDistance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_hitResult;
	static void NewProp_bLineHit_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLineHit;
	static void NewProp_bIsInspecting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInspecting;
	static const UECodeGen_Private::FDoublePropertyParams NewProp_SetFocalDistance;
	static void NewProp_SkillCheckResult_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_SkillCheckResult;
	static void NewProp_bDrawWidget_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDrawWidget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MoveAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_IgnoreLineTraceActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_IgnoreLineTraceActors;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CharacterState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CharacterState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ABaoli_Controller_ChangeCharacterState, "ChangeCharacterState" }, // 2248005630
		{ &Z_Construct_UFunction_ABaoli_Controller_GetCharacterState, "GetCharacterState" }, // 3454057514
		{ &Z_Construct_UFunction_ABaoli_Controller_LineTrace, "LineTrace" }, // 2652163212
		{ &Z_Construct_UFunction_ABaoli_Controller_MovementInput, "MovementInput" }, // 1876921012
		{ &Z_Construct_UFunction_ABaoli_Controller_TraceForCamera, "TraceForCamera" }, // 3049412084
		{ &Z_Construct_UFunction_ABaoli_Controller_TraceForInteraction, "TraceForInteraction" }, // 3753759639
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ABaoli_Controller>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_Baoli_Character = { "Baoli_Character", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Controller, Baoli_Character), Z_Construct_UClass_ABaoli_Character_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Baoli_Character_MetaData), NewProp_Baoli_Character_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_Mesh = { "Mesh", nullptr, (EPropertyFlags)0x001000000008000c, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Controller, Mesh), Z_Construct_UClass_USkeletalMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Mesh_MetaData), NewProp_Mesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_Cam = { "Cam", nullptr, (EPropertyFlags)0x001000000008000c, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Controller, Cam), Z_Construct_UClass_UCameraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Cam_MetaData), NewProp_Cam_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_FocusedObject = { "FocusedObject", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Controller, FocusedObject), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FocusedObject_MetaData), NewProp_FocusedObject_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_ActiveComponent = { "ActiveComponent", nullptr, (EPropertyFlags)0x001000000008000c, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Controller, ActiveComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveComponent_MetaData), NewProp_ActiveComponent_MetaData) };
void Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bHoldTrace_SetBit(void* Obj)
{
	((ABaoli_Controller*)Obj)->bHoldTrace = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bHoldTrace = { "bHoldTrace", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABaoli_Controller), &Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bHoldTrace_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHoldTrace_MetaData), NewProp_bHoldTrace_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_TraceDistance = { "TraceDistance", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Controller, TraceDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TraceDistance_MetaData), NewProp_TraceDistance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_hitResult = { "hitResult", nullptr, (EPropertyFlags)0x0010008000000004, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Controller, hitResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_hitResult_MetaData), NewProp_hitResult_MetaData) }; // 4100991306
void Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bLineHit_SetBit(void* Obj)
{
	((ABaoli_Controller*)Obj)->bLineHit = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bLineHit = { "bLineHit", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABaoli_Controller), &Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bLineHit_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLineHit_MetaData), NewProp_bLineHit_MetaData) };
void Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bIsInspecting_SetBit(void* Obj)
{
	((ABaoli_Controller*)Obj)->bIsInspecting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bIsInspecting = { "bIsInspecting", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABaoli_Controller), &Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bIsInspecting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInspecting_MetaData), NewProp_bIsInspecting_MetaData) };
const UECodeGen_Private::FDoublePropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_SetFocalDistance = { "SetFocalDistance", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Double, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Controller, SetFocalDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SetFocalDistance_MetaData), NewProp_SetFocalDistance_MetaData) };
void Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_SkillCheckResult_SetBit(void* Obj)
{
	((ABaoli_Controller*)Obj)->SkillCheckResult = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_SkillCheckResult = { "SkillCheckResult", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABaoli_Controller), &Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_SkillCheckResult_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SkillCheckResult_MetaData), NewProp_SkillCheckResult_MetaData) };
void Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bDrawWidget_SetBit(void* Obj)
{
	((ABaoli_Controller*)Obj)->bDrawWidget = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bDrawWidget = { "bDrawWidget", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABaoli_Controller), &Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bDrawWidget_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDrawWidget_MetaData), NewProp_bDrawWidget_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_MoveAction = { "MoveAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Controller, MoveAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MoveAction_MetaData), NewProp_MoveAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_IgnoreLineTraceActors_Inner = { "IgnoreLineTraceActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_IgnoreLineTraceActors = { "IgnoreLineTraceActors", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Controller, IgnoreLineTraceActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_IgnoreLineTraceActors_MetaData), NewProp_IgnoreLineTraceActors_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_CharacterState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_CharacterState = { "CharacterState", nullptr, (EPropertyFlags)0x0020080000000004, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Controller, CharacterState), Z_Construct_UEnum_Baoli_ECharacterState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CharacterState_MetaData), NewProp_CharacterState_MetaData) }; // 709515122
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ABaoli_Controller_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_Baoli_Character,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_Mesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_Cam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_FocusedObject,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_ActiveComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bHoldTrace,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_TraceDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_hitResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bLineHit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bIsInspecting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_SetFocalDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_SkillCheckResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_bDrawWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_MoveAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_IgnoreLineTraceActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_IgnoreLineTraceActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_CharacterState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Controller_Statics::NewProp_CharacterState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ABaoli_Controller_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ABaoli_Controller_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_APlayerController,
	(UObject* (*)())Z_Construct_UPackage__Script_Baoli,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ABaoli_Controller_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ABaoli_Controller_Statics::ClassParams = {
	&ABaoli_Controller::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ABaoli_Controller_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ABaoli_Controller_Statics::PropPointers),
	0,
	0x009002A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ABaoli_Controller_Statics::Class_MetaDataParams), Z_Construct_UClass_ABaoli_Controller_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ABaoli_Controller()
{
	if (!Z_Registration_Info_UClass_ABaoli_Controller.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ABaoli_Controller.OuterSingleton, Z_Construct_UClass_ABaoli_Controller_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ABaoli_Controller.OuterSingleton;
}
template<> BAOLI_API UClass* StaticClass<ABaoli_Controller>()
{
	return ABaoli_Controller::StaticClass();
}
ABaoli_Controller::ABaoli_Controller(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(ABaoli_Controller);
ABaoli_Controller::~ABaoli_Controller() {}
// End Class ABaoli_Controller

// Begin Registration
struct Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ECharacterState_StaticEnum, TEXT("ECharacterState"), &Z_Registration_Info_UEnum_ECharacterState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 709515122U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ABaoli_Controller, ABaoli_Controller::StaticClass, TEXT("ABaoli_Controller"), &Z_Registration_Info_UClass_ABaoli_Controller, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ABaoli_Controller), 468176452U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_139080831(TEXT("/Script/Baoli"),
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Controller_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
