// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Baoli/AI/AI_CharacterBase.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAI_CharacterBase() {}

// Begin Cross Module References
BAOLI_API UClass* Z_Construct_UClass_AAI_CharacterBase();
BAOLI_API UClass* Z_Construct_UClass_AAI_CharacterBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_ACharacter();
UPackage* Z_Construct_UPackage__Script_Baoli();
// End Cross Module References

// Begin Class AAI_CharacterBase Function ForceDash
struct Z_Construct_UFunction_AAI_CharacterBase_ForceDash_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dash Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Called to Force Dash\n" },
#endif
		{ "ModuleRelativePath", "AI/AI_CharacterBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called to Force Dash" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAI_CharacterBase_ForceDash_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAI_CharacterBase, nullptr, "ForceDash", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAI_CharacterBase_ForceDash_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAI_CharacterBase_ForceDash_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AAI_CharacterBase_ForceDash()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAI_CharacterBase_ForceDash_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAI_CharacterBase::execForceDash)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceDash();
	P_NATIVE_END;
}
// End Class AAI_CharacterBase Function ForceDash

// Begin Class AAI_CharacterBase
void AAI_CharacterBase::StaticRegisterNativesAAI_CharacterBase()
{
	UClass* Class = AAI_CharacterBase::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ForceDash", &AAI_CharacterBase::execForceDash },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AAI_CharacterBase);
UClass* Z_Construct_UClass_AAI_CharacterBase_NoRegister()
{
	return AAI_CharacterBase::StaticClass();
}
struct Z_Construct_UClass_AAI_CharacterBase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "AI/AI_CharacterBase.h" },
		{ "ModuleRelativePath", "AI/AI_CharacterBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShouldDash_MetaData[] = {
		{ "Category", "Dash Properties" },
		{ "ModuleRelativePath", "AI/AI_CharacterBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDelayTime_MetaData[] = {
		{ "Category", "Dash Properties" },
		{ "ModuleRelativePath", "AI/AI_CharacterBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDelayTime_MetaData[] = {
		{ "Category", "Dash Properties" },
		{ "ModuleRelativePath", "AI/AI_CharacterBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashDuration_MetaData[] = {
		{ "Category", "Dash Properties" },
		{ "ModuleRelativePath", "AI/AI_CharacterBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlaybackMultiplier_MetaData[] = {
		{ "Category", "Animation Playback" },
		{ "ModuleRelativePath", "AI/AI_CharacterBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAtacking_MetaData[] = {
		{ "Category", "Enemy Properties" },
		{ "ModuleRelativePath", "AI/AI_CharacterBase.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bShouldDash_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShouldDash;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDelayTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDelayTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlaybackMultiplier;
	static void NewProp_bIsAtacking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAtacking;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAI_CharacterBase_ForceDash, "ForceDash" }, // 759469216
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAI_CharacterBase>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_bShouldDash_SetBit(void* Obj)
{
	((AAI_CharacterBase*)Obj)->bShouldDash = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_bShouldDash = { "bShouldDash", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAI_CharacterBase), &Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_bShouldDash_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShouldDash_MetaData), NewProp_bShouldDash_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_MaxDelayTime = { "MaxDelayTime", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAI_CharacterBase, MaxDelayTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDelayTime_MetaData), NewProp_MaxDelayTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_MinDelayTime = { "MinDelayTime", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAI_CharacterBase, MinDelayTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDelayTime_MetaData), NewProp_MinDelayTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_DashDuration = { "DashDuration", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAI_CharacterBase, DashDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashDuration_MetaData), NewProp_DashDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_PlaybackMultiplier = { "PlaybackMultiplier", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAI_CharacterBase, PlaybackMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlaybackMultiplier_MetaData), NewProp_PlaybackMultiplier_MetaData) };
void Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_bIsAtacking_SetBit(void* Obj)
{
	((AAI_CharacterBase*)Obj)->bIsAtacking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_bIsAtacking = { "bIsAtacking", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAI_CharacterBase), &Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_bIsAtacking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAtacking_MetaData), NewProp_bIsAtacking_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAI_CharacterBase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_bShouldDash,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_MaxDelayTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_MinDelayTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_DashDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_PlaybackMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAI_CharacterBase_Statics::NewProp_bIsAtacking,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAI_CharacterBase_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAI_CharacterBase_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ACharacter,
	(UObject* (*)())Z_Construct_UPackage__Script_Baoli,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAI_CharacterBase_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAI_CharacterBase_Statics::ClassParams = {
	&AAI_CharacterBase::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAI_CharacterBase_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAI_CharacterBase_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAI_CharacterBase_Statics::Class_MetaDataParams), Z_Construct_UClass_AAI_CharacterBase_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAI_CharacterBase()
{
	if (!Z_Registration_Info_UClass_AAI_CharacterBase.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAI_CharacterBase.OuterSingleton, Z_Construct_UClass_AAI_CharacterBase_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAI_CharacterBase.OuterSingleton;
}
template<> BAOLI_API UClass* StaticClass<AAI_CharacterBase>()
{
	return AAI_CharacterBase::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAI_CharacterBase);
AAI_CharacterBase::~AAI_CharacterBase() {}
// End Class AAI_CharacterBase

// Begin Registration
struct Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAI_CharacterBase, AAI_CharacterBase::StaticClass, TEXT("AAI_CharacterBase"), &Z_Registration_Info_UClass_AAI_CharacterBase, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAI_CharacterBase), 2462011574U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_1365813909(TEXT("/Script/Baoli"),
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_AI_AI_CharacterBase_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
