// Copyright Epic Games, Inc. All Rights Reserved.
// Modifications Copyright (C) 2025 Anshul <PERSON>

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "WebBrowserOptimizer.generated.h"

// Forward declarations
class SWebBrowser;

/**
 * Configuration settings for the web browser optimizer
 */
USTRUCT(BlueprintType)
struct FWebBrowserOptimizerSettings
{
    GENERATED_BODY()

    /** The frame rate at which the browser will render. Higher values are smoother but use more CPU. */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "15", ClampMax = "120"))
    int32 BrowserFrameRate = 30;

    /** Whether hardware acceleration is enabled. */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bHardwareAccelerationEnabled = true;

    /** Background color of the browser. Using opaque colors can improve performance. */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    FColor BackgroundColor = FColor(255, 255, 255, 255);

    /** Whether to throttle background tabs to save CPU resources. */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bBackgroundTabThrottlingEnabled = true;

    /** Whether to optimize scrolling performance. */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bOptimizeScrolling = true;

    /** Whether to lazy load images for better performance. */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bLazyLoadImages = true;

    /** Whether to disable animations when the page is not visible. */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bDisableAnimationsWhenHidden = true;
};

/**
 * Helper class to optimize web browser performance
 */
UCLASS(BlueprintType)
class OPTIMIZEDWEBBROWSER_API UWebBrowserOptimizer : public UObject
{
    GENERATED_BODY()

public:
    UWebBrowserOptimizer();

    /**
     * Apply optimization settings to a web browser widget
     *
     * @param WebBrowserPtr The web browser widget to optimize (as a void pointer)
     * @param Settings The optimization settings to apply
     */
    UFUNCTION(BlueprintCallable, Category = "Web Browser|Performance")
    static void ApplyOptimizations(UObject* WebBrowserWidget, const FWebBrowserOptimizerSettings& Settings);

    /**
     * Get the JavaScript optimization code
     *
     * @return The JavaScript code for optimizations
     */
    static FString GetOptimizationJavaScript();
};
