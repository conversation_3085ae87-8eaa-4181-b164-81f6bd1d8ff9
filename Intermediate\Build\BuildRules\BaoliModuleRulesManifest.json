{"SourceFiles": ["H:\\P4\\dev\\Baoli\\Source\\Baoli\\Baoli.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\BlockoutToolsPlugin\\Source\\BlockoutToolsEditorPlugin\\BlockoutToolsEditorPlugin.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\BlockoutToolsPlugin\\Source\\BlockoutToolsPlugin\\BlockoutToolsPlugin.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\DLSS\\Source\\DLSSBlueprint\\DLSSBlueprint.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\DLSS\\Source\\DLSSEditor\\DLSSEditor.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\DLSS\\Source\\DLSSUtility\\DLSSUtility.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\DLSS\\Source\\DLSS\\DLSS.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\DLSS\\Source\\NGXD3D11RHI\\NGXD3D11RHI.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\DLSS\\Source\\NGXD3D12RHI\\NGXD3D12RHI.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\DLSS\\Source\\NGXRHI\\NGXRHI.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\DLSS\\Source\\NGXVulkanRHIPreInit\\NGXVulkanRHIPreInit.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\DLSS\\Source\\NGXVulkanRHI\\NGXVulkanRHI.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\DLSS\\Source\\ThirdParty\\NGX\\NGX.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\FSR3\\Source\\FFXD3D12Backend\\FFXD3D12Backend.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\FSR3\\Source\\FFXD3D12\\FFXD3D12.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\FSR3\\Source\\FFXFSR3Api\\FFXFSR3Api.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\FSR3\\Source\\FFXFSR3Settings\\FFXFSR3Settings.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\FSR3\\Source\\FFXFSR3TemporalUpscaling\\FFXFSR3TemporalUpscaling.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\FSR3\\Source\\FFXFrameInterpolationApi\\FFXFrameInterpolationApi.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\FSR3\\Source\\FFXFrameInterpolation\\FFXFrameInterpolation.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\FSR3\\Source\\FFXOpticalFlowApi\\FFXOpticalFlowApi.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\FSR3\\Source\\FFXRHIBackend\\FFXRHIBackend.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\FSR3\\Source\\FFXShared\\FFXShared.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\Inkpot\\Source\\InkPlusPlus\\InkPlusPlus.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\Inkpot\\Source\\InkpotEditor\\InkpotEditor.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\Inkpot\\Source\\Inkpot\\Inkpot.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\MergeAssist-master\\Source\\MergeAssist\\MergeAssist.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\OptimizedWebBrowser\\Source\\OptimizedWebBrowser\\OptimizedWebBrowser.build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\PhysicalLayout\\Source\\PhysicalLayout\\PhysicalLayout.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\PlatformFunctionsPlugin_5.4\\Source\\PlatformFunctions\\PlatformFunctions.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\rdBPtools\\Source\\rdBPtools\\rdBPtools.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\rdInst\\Source\\rdInst\\rdInst.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\SnappingHelper\\Source\\SnappingHelper\\SnappingHelper.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\XV3dGS\\Source\\GSEditor\\GSEditor.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\XV3dGS\\Source\\GSImporter\\GSImporter.Build.cs", "H:\\P4\\dev\\Baoli\\Plugins\\XV3dGS\\Source\\GSRuntime\\GSRuntime.Build.cs", "H:\\P4\\dev\\Baoli\\Source\\Baoli.Target.cs", "H:\\P4\\dev\\Baoli\\Source\\BaoliEditor.Target.cs"], "EngineVersion": "5.5.4"}