{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13395742969210778", "port": 443, "protocol_str": "quic"}], "isolation": [], "network_stats": {"srtt": 25609}, "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"isolation": [], "server": "https://ddinktqu5prvc.cloudfront.net", "supports_spdy": true}, {"isolation": [], "server": "https://d3uwib8iif8w1p.cloudfront.net", "supports_spdy": true}, {"isolation": [], "server": "https://cdn.quixel.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13395742070840688", "port": 443, "protocol_str": "quic"}], "isolation": [], "network_stats": {"srtt": 25124}, "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13395742070761350", "port": 443, "protocol_str": "quic"}], "isolation": [], "network_stats": {"srtt": 26720}, "server": "https://www.google-analytics.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13395742070887467", "port": 443, "protocol_str": "quic"}], "isolation": [], "network_stats": {"srtt": 22960}, "server": "https://fonts.googleapis.com", "supports_spdy": true}], "supports_quic": {"address": "************", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}