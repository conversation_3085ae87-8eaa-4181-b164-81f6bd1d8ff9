// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Interactable/Cover/Bed.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class ABaoli_Character;
class UAnimMontage;
class UArrowComponent;
struct FBranchingPointNotifyPayload;
#ifdef BAOLI_Bed_generated_h
#error "Bed.generated.h already included, missing '#pragma once' in Bed.h"
#endif
#define BAOLI_Bed_generated_h

#define FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_15_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execClearGarbage); \
	DECLARE_FUNCTION(execOnNotify); \
	DECLARE_FUNCTION(execHideOut); \
	DECLARE_FUNCTION(execHideIn); \
	DECLARE_FUNCTION(execOnCompleted); \
	DECLARE_FUNCTION(execOnBlendOut); \
	DECLARE_FUNCTION(execInterpAim); \
	DECLARE_FUNCTION(execGetArrow);


#define FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesABed(); \
	friend struct Z_Construct_UClass_ABed_Statics; \
public: \
	DECLARE_CLASS(ABed, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Baoli"), NO_API) \
	DECLARE_SERIALIZER(ABed)


#define FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_15_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ABed(ABed&&); \
	ABed(const ABed&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ABed); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ABed); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ABed) \
	NO_API virtual ~ABed();


#define FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_12_PROLOG
#define FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_15_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_15_INCLASS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BAOLI_API UClass* StaticClass<class ABed>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Baoli_Source_Baoli_Interactable_Cover_Bed_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
