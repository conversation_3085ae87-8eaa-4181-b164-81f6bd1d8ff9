// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "BaoliGameUserSettings.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class UBaoliGameUserSettings;
#ifdef BAOLI_BaoliGameUserSettings_generated_h
#error "BaoliGameUserSettings.generated.h already included, missing '#pragma once' in BaoliGameUserSettings.h"
#endif
#define BAOLI_BaoliGameUserSettings_generated_h

#define FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_15_RPC_WRAPPERS \
	DECLARE_FUNCTION(execGetBaoliGameUserSettings); \
	DECLARE_FUNCTION(execSetCustomVariable);


#define FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_15_INCLASS \
private: \
	static void StaticRegisterNativesUBaoliGameUserSettings(); \
	friend struct Z_Construct_UClass_UBaoliGameUserSettings_Statics; \
public: \
	DECLARE_CLASS(UBaoliGameUserSettings, UGameUserSettings, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Baoli"), NO_API) \
	DECLARE_SERIALIZER(UBaoliGameUserSettings)


#define FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_15_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UBaoliGameUserSettings(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UBaoliGameUserSettings) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UBaoliGameUserSettings); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UBaoliGameUserSettings); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UBaoliGameUserSettings(UBaoliGameUserSettings&&); \
	UBaoliGameUserSettings(const UBaoliGameUserSettings&); \
public: \
	NO_API virtual ~UBaoliGameUserSettings();


#define FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_12_PROLOG
#define FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_15_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_15_RPC_WRAPPERS \
	FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_15_INCLASS \
	FID_Baoli_Source_Baoli_BaoliGameUserSettings_h_15_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BAOLI_API UClass* StaticClass<class UBaoliGameUserSettings>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Baoli_Source_Baoli_BaoliGameUserSettings_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
