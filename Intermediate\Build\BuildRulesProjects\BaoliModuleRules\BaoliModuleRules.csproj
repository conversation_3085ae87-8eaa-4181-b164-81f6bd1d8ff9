﻿<!-- This file was generated by UnrealBuildTool.ProjectFileGenerator.CreateRulesAssemblyProject() -->
<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="D:\UE_5.5\Engine\Source\Programs\Shared\UnrealEngine.csproj.props" />
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Configurations>Debug;Release;Development</Configurations>
    <DefineConstants>$(DefineConstants);WITH_FORWARDED_MODULE_RULES_CTOR;WITH_FORWARDED_TARGET_RULES_CTOR;UE_4_17_OR_LATER;UE_4_18_OR_LATER;UE_4_19_OR_LATER;UE_4_20_OR_LATER;UE_4_21_OR_LATER;UE_4_22_OR_LATER;UE_4_23_OR_LATER;UE_4_24_OR_LATER;UE_4_25_OR_LATER;UE_4_26_OR_LATER;UE_4_27_OR_LATER;UE_4_28_OR_LATER;UE_4_29_OR_LATER;UE_4_30_OR_LATER;UE_5_0_OR_LATER;UE_5_1_OR_LATER;UE_5_2_OR_LATER;UE_5_3_OR_LATER;UE_5_4_OR_LATER;UE_5_5_OR_LATER</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="D:\UE_5.5\Engine\Intermediate\Build\BuildRulesProjects\MarketplaceRules\MarketplaceRules.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="D:\UE_5.5\Engine\Intermediate\Build\BuildRulesProjects\UE5ProgramRules\UE5ProgramRules.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="D:\UE_5.5\Engine\Intermediate\Build\BuildRulesProjects\UE5Rules\UE5Rules.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="D:\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Build\EpicGames.Build.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="D:\UE_5.5\Engine\Source\Programs\UnrealBuildTool\UnrealBuildTool.csproj"><Private>false</Private></ProjectReference>
  </ItemGroup>
  <ItemGroup>
  <Compile Include="..\..\..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\BlockoutToolsEditorPlugin.Build.cs"><Link>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsEditorPlugin\BlockoutToolsEditorPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\BlockoutToolsPlugin.Build.cs"><Link>Plugins\BlockoutToolsPlugin\Source\BlockoutToolsPlugin\BlockoutToolsPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DLSS\Source\DLSSBlueprint\DLSSBlueprint.Build.cs"><Link>Plugins\DLSS\Source\DLSSBlueprint\DLSSBlueprint.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DLSS\Source\DLSSEditor\DLSSEditor.Build.cs"><Link>Plugins\DLSS\Source\DLSSEditor\DLSSEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DLSS\Source\DLSSUtility\DLSSUtility.Build.cs"><Link>Plugins\DLSS\Source\DLSSUtility\DLSSUtility.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DLSS\Source\DLSS\DLSS.Build.cs"><Link>Plugins\DLSS\Source\DLSS\DLSS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DLSS\Source\NGXD3D11RHI\NGXD3D11RHI.Build.cs"><Link>Plugins\DLSS\Source\NGXD3D11RHI\NGXD3D11RHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DLSS\Source\NGXD3D12RHI\NGXD3D12RHI.Build.cs"><Link>Plugins\DLSS\Source\NGXD3D12RHI\NGXD3D12RHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DLSS\Source\NGXRHI\NGXRHI.Build.cs"><Link>Plugins\DLSS\Source\NGXRHI\NGXRHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DLSS\Source\NGXVulkanRHIPreInit\NGXVulkanRHIPreInit.Build.cs"><Link>Plugins\DLSS\Source\NGXVulkanRHIPreInit\NGXVulkanRHIPreInit.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DLSS\Source\NGXVulkanRHI\NGXVulkanRHI.Build.cs"><Link>Plugins\DLSS\Source\NGXVulkanRHI\NGXVulkanRHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DLSS\Source\ThirdParty\NGX\NGX.Build.cs"><Link>Plugins\DLSS\Source\ThirdParty\NGX\NGX.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FSR3\Source\FFXD3D12Backend\FFXD3D12Backend.Build.cs"><Link>Plugins\FSR3\Source\FFXD3D12Backend\FFXD3D12Backend.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FSR3\Source\FFXD3D12\FFXD3D12.Build.cs"><Link>Plugins\FSR3\Source\FFXD3D12\FFXD3D12.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FSR3\Source\FFXFSR3Api\FFXFSR3Api.Build.cs"><Link>Plugins\FSR3\Source\FFXFSR3Api\FFXFSR3Api.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FSR3\Source\FFXFSR3Settings\FFXFSR3Settings.Build.cs"><Link>Plugins\FSR3\Source\FFXFSR3Settings\FFXFSR3Settings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\FFXFSR3TemporalUpscaling.Build.cs"><Link>Plugins\FSR3\Source\FFXFSR3TemporalUpscaling\FFXFSR3TemporalUpscaling.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FSR3\Source\FFXFrameInterpolationApi\FFXFrameInterpolationApi.Build.cs"><Link>Plugins\FSR3\Source\FFXFrameInterpolationApi\FFXFrameInterpolationApi.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FSR3\Source\FFXFrameInterpolation\FFXFrameInterpolation.Build.cs"><Link>Plugins\FSR3\Source\FFXFrameInterpolation\FFXFrameInterpolation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FSR3\Source\FFXOpticalFlowApi\FFXOpticalFlowApi.Build.cs"><Link>Plugins\FSR3\Source\FFXOpticalFlowApi\FFXOpticalFlowApi.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FSR3\Source\FFXRHIBackend\FFXRHIBackend.Build.cs"><Link>Plugins\FSR3\Source\FFXRHIBackend\FFXRHIBackend.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FSR3\Source\FFXShared\FFXShared.Build.cs"><Link>Plugins\FSR3\Source\FFXShared\FFXShared.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Inkpot\Source\InkPlusPlus\InkPlusPlus.Build.cs"><Link>Plugins\Inkpot\Source\InkPlusPlus\InkPlusPlus.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Inkpot\Source\InkpotEditor\InkpotEditor.Build.cs"><Link>Plugins\Inkpot\Source\InkpotEditor\InkpotEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Inkpot\Source\Inkpot\Inkpot.Build.cs"><Link>Plugins\Inkpot\Source\Inkpot\Inkpot.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MergeAssist-master\Source\MergeAssist\MergeAssist.Build.cs"><Link>Plugins\MergeAssist-master\Source\MergeAssist\MergeAssist.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\OptimizedWebBrowser.build.cs"><Link>Plugins\OptimizedWebBrowser\Source\OptimizedWebBrowser\OptimizedWebBrowser.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PhysicalLayout\Source\PhysicalLayout\PhysicalLayout.Build.cs"><Link>Plugins\PhysicalLayout\Source\PhysicalLayout\PhysicalLayout.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\PlatformFunctions.Build.cs"><Link>Plugins\PlatformFunctionsPlugin_5.4\Source\PlatformFunctions\PlatformFunctions.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\SnappingHelper\Source\SnappingHelper\SnappingHelper.Build.cs"><Link>Plugins\SnappingHelper\Source\SnappingHelper\SnappingHelper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\XV3dGS\Source\GSEditor\GSEditor.Build.cs"><Link>Plugins\XV3dGS\Source\GSEditor\GSEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\XV3dGS\Source\GSImporter\GSImporter.Build.cs"><Link>Plugins\XV3dGS\Source\GSImporter\GSImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\XV3dGS\Source\GSRuntime\GSRuntime.Build.cs"><Link>Plugins\XV3dGS\Source\GSRuntime\GSRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\rdBPtools\Source\rdBPtools\rdBPtools.Build.cs"><Link>Plugins\rdBPtools\Source\rdBPtools\rdBPtools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\rdInst\Source\rdInst\rdInst.Build.cs"><Link>Plugins\rdInst\Source\rdInst\rdInst.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Baoli.Target.cs"><Link>Source\Baoli.Target.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\BaoliEditor.Target.cs"><Link>Source\BaoliEditor.Target.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Baoli\Baoli.Build.cs"><Link>Source\Baoli\Baoli.Build.cs</Link></Compile>
  </ItemGroup>
</Project>
