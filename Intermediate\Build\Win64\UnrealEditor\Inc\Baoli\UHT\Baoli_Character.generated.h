// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Player/Baoli_Character.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef BAOLI_Baoli_Character_generated_h
#error "Baoli_Character.generated.h already included, missing '#pragma once' in Baoli_Character.h"
#endif
#define BAOLI_Baoli_Character_generated_h

#define FID_Baoli_Source_Baoli_Player_Baoli_Character_h_40_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execEnableCharacter); \
	DECLARE_FUNCTION(execDisableCharacter);


#define FID_Baoli_Source_Baoli_Player_Baoli_Character_h_40_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesABaoli_Character(); \
	friend struct Z_Construct_UClass_ABaoli_Character_Statics; \
public: \
	DECLARE_CLASS(ABaoli_Character, ACharacter, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/Baoli"), NO_API) \
	DECLARE_SERIALIZER(ABaoli_Character)


#define FID_Baoli_Source_Baoli_Player_Baoli_Character_h_40_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ABaoli_Character(ABaoli_Character&&); \
	ABaoli_Character(const ABaoli_Character&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ABaoli_Character); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ABaoli_Character); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ABaoli_Character) \
	NO_API virtual ~ABaoli_Character();


#define FID_Baoli_Source_Baoli_Player_Baoli_Character_h_37_PROLOG
#define FID_Baoli_Source_Baoli_Player_Baoli_Character_h_40_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Baoli_Source_Baoli_Player_Baoli_Character_h_40_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_Player_Baoli_Character_h_40_INCLASS_NO_PURE_DECLS \
	FID_Baoli_Source_Baoli_Player_Baoli_Character_h_40_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BAOLI_API UClass* StaticClass<class ABaoli_Character>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Baoli_Source_Baoli_Player_Baoli_Character_h


#define FOREACH_ENUM_EMYGAIT(op) \
	op(Walk) \
	op(Run) \
	op(Sprint) 
#define FOREACH_ENUM_ECOVERSYSTEM(op) \
	op(None) \
	op(Almari) \
	op(Bed) 
PRAGMA_ENABLE_DEPRECATION_WARNINGS
