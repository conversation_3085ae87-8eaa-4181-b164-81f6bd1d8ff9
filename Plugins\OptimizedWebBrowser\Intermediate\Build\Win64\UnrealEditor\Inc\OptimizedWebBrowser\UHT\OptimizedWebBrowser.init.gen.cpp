// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeOptimizedWebBrowser_init() {}
	OPTIMIZEDWEBBROWSER_API UFunction* Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature();
	OPTIMIZEDWEBBROWSER_API UFunction* Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature();
	OPTIMIZEDWEBBROWSER_API UFunction* Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_OptimizedWebBrowser;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_OptimizedWebBrowser()
	{
		if (!Z_Registration_Info_UPackage__Script_OptimizedWebBrowser.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnBeforePopup__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnConsoleMessage__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UOptimizedWebBrowser_OnUrlChanged__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/OptimizedWebBrowser",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0xE0F63B31,
				0xE002BEAE,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_OptimizedWebBrowser.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_OptimizedWebBrowser.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_OptimizedWebBrowser(Z_Construct_UPackage__Script_OptimizedWebBrowser, TEXT("/Script/OptimizedWebBrowser"), Z_Registration_Info_UPackage__Script_OptimizedWebBrowser, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xE0F63B31, 0xE002BEAE));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
